#ifndef ZPATH_SYNC_PAUSE_H
#define ZPATH_SYNC_PAUSE_H

#include "wally/wally.h"
#include "zhash/zhash_table.h"
#include "zpath_lib/zpath_debug.h"
#include "zpath_lib/zpath_partition_common.h"

typedef struct zpath_sync_pause_db_state {
    const char              *db_name;
    int64_t                 config_pause_db;
    int                     cmd_line_paused;
    struct zhash_table      *all_tables;         /* table_name => table_state */
    struct wally            *wally;              /* Pointer to the wally for this db */
} zpath_sync_pause_db_state_t;

struct zpath_sync_pause_table_state {
    struct wally_table *table;
    struct wally *wally;
    bool is_paused;
    int paused;
    struct zpath_sync_pause_db_state *db_state;
};

#ifdef __cplusplus
extern "C" {
#endif

int zpath_sync_pause_init(const char *role_name);

void zpath_sync_pause_register_pausable_table(struct wally *wally, struct wally_table *table);

/* Function for handling command-line pause/resume */
int zpath_sync_pause_handle_local_pause(struct zpath_debug_state *request_state,
                                       const char **query_values,
                                       int query_value_count,
                                       void *object);

/* Callback function executed after all tables are resumed */
int zpath_wally_post_resume_callback(struct wally *wally);

/* Function to validate broker table registration (for development/assertions) */
void zpath_sync_pause_validate_broker_tables(struct wally *wally);

/* Function to check if global wally tables are paused */
int zpath_sync_pause_are_global_wally_tables_paused(void);

/*
 * The following functions are primarily internal helpers or specific walk functions.
 * They should generally NOT be declared here as they are static in production builds.
 * They are declared extern "C" in the test file when needed for direct testing.
 */
// int zpath_sync_pause_walk_f_eval_db_state(void *cookie, void *object, void *key, size_t key_len);
// int zpath_sync_pause_walk_f_validate_table(void *cookie, void *object, void *key, size_t key_len);
// int zpath_sync_pause_walk_f_eval_table_state(void *cookie, void *object, void *key, size_t key_len);

#ifdef __cplusplus
}
#endif

#endif /* ZPATH_SYNC_PAUSE_H */
