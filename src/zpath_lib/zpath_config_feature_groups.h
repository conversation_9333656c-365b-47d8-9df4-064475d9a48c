/*
 * zpath_config_feature_groups.h. Copyright (C) 2021 Zscaler Inc, All Rights Reserved
 */

#ifndef _ZPATH_CONFIG_OVERRIDE_FEATURE_GROUPS_H
#define _ZPATH_CONFIG_OVERRIDE_FEATURE_GROUPS_H

/* Naming convention for Feature Group Names:
* 1. Names should be alphanumerical
* 2. Allowed special chars are (- , ())
* 3. Beginning of word should be capital letter
* 4. Acronyms should be capitalized
* 5. Words should be separated by single space
* 6. Conjunction words should be lower case (eg: for, to, in)
* 7. Avoid abbreviations for ZPA terminology (eg: Micro Tunnel instead of Mtunnel)
*/


/* We shall define the feature groups here for the DB SCHEMA changes */

#define FEATURE_GROUP_APP_PROTECTION                                               "App Protection"
#define FEATURE_GROUP_APP_SCALING                                                  "App Scaling"
#define FEATURE_GROUP_BROKER_RATE_LIMITING                                         "Broker Rate Limiting"
#define FEATURE_GROUP_BROKER_REDIRECT                                              "Broker Redirect"
#define FEATURE_GROUP_BROKER_REDIRECT_RETRY                                        "Broker Redirect Retry"
#define FEATURE_GROUP_CLIENT_TO_CLIENT                                             "Client to Client (C2C)"
#define FEATURE_GROUP_C2C_MULTIPLE_FQDN_REG_PROTECTION                             "Protection for C2C Multiple FQDN Registrations"
#define FEATURE_GROUP_SERVER_TO_CLIENT                                             "Server to Client (S2C)"
#define FEATURE_GROUP_CORS_REQUEST                                                 "CORS Request"
#define FEATURE_GROUP_DTLS                                                         "DTLS"
#define FEATURE_GROUP_FOHH                                                         "FOHH"
#define FEATURE_GROUP_MTUNNEL_REUSE                                                "Micro Tunnel Reuse"
#define FEATURE_GROUP_PARTNER_LOGIN                                                "Partner Login"
#define FEATURE_GROUP_SAMESITE_COOKIE_ATTRIBUTE                                    "SameSite Cookie Attribute"
#define FEATURE_GROUP_SESSION_AUTHENTICATION                                       "Session Authentication"
#define FEATURE_GROUP_URL_COOKIE_ENCRYPTION                                        "URL Cookie Encryption"
#define FEATURE_GROUP_ZDX_TPG_PUBLIC_CERT                                          "ZDX Public Certificate"
#define FEATURE_GROUP_ZDX_WEBPROBE                                                 "ZDX Web Probe"
#define FEATURE_GROUP_ZDX_WEBPROBE_IPV6                                            "ZDX Web Probe IPV6"
#define FEATURE_GROUP_ZDX_HTTPS_WEBPROBE                                           "ZDX HTTPS Web Probe"
#define FEATURE_GROUP_ZDX_HTTPS_WEBPROBE_IPV6                                      "ZDX HTTPS Web Probe IPV6"
#define FEATURE_GROUP_ZDX_RAW_SOCKETS                                              "ZDX Raw Sockets"
#define FEATURE_GROUP_ZDX_ENABLE                                                   "ZDX Enablement"
#define FEATURE_GROUP_ZDX_MTR_SPEEDUP                                              "ZDX MTR Speed Up"
#define FEATURE_GROUP_ZDX_IPV6_MTR_SPEEDUP                                         "ZDX MTR IPv6 Speed Up"
#define FEATURE_GROUP_ZDX_MTR_RATE_LIMIT                                           "ZDX MTR Rate Limit"
#define FEATURE_GROUP_ZDX_MTR_IPV6                                                 "ZDX MTR IPv6"
#define FEATURE_GROUP_ZDX_MTR_LIMIT                                                "ZDX MTR Limit"
#define FEATURE_GROUP_ZDX_BROKER_TRACEROUTE                                        "ZDX Broker Traceroute"
#define FEATURE_GROUP_ZDX_BROKER_DISABLE_FC_MTR                                    "ZDX Broker Disable Flow Control MTR"
#define FEATURE_GROUP_CUSTOMER_DISASTER_RECOVERY                                   "Customer Disaster Recovery"
#define FEATURE_GROUP_DELEGATED_TENANT_ADMIN                                       "Delegated Tenant Admin"
#define FEATURE_GROUP_PRIVATE_SERVICE_EDGE_COMMAND_PROBE                           "Private Service Edge Command Probes"
#define FEATURE_GROUP_PRIVATE_SERVICE_EDGE_LSS                                     "Private Service Edge LSS"
#define FEATURE_GROUP_PRIVATE_SERVICE_EDGE_CLIENT_MONITOR                          "Private Service Edge Client Monitoring"
#define FEATURE_GROUP_PRIVATE_SERVICE_EDGE_ARGO                                    "Private Service Edge ARGO"
#define FEATURE_GROUP_PRIVATE_SERVICE_EDGE_MEMORY                                  "Private Service Edge Memory Requirement"
#define FEATURE_GROUP_PRIVATE_SERVICE_EDGE_TWO_HOP                                 "Private Service Edge Two Hop Support"
#define FEATURE_GROUP_COUNTRY_CODE_POLICY                                          "Country Code in Policies"
#define FEATURE_GROUP_PRIVATE_SERVICE_EDGE_STATIC_WALLY                            "Private Service Edge Registration Wally"
#define FEATURE_GROUP_PRIVATE_SERVICE_EDGE_C2C_LOCAL_DISP_BYPASS                   "Private Service Edge C2C Local Dispatcher Bypass"
#define FEATURE_GROUP_PRIVATE_SERVICE_EDGE_C2C_LOCAL_DISP_BYPASS_PHASE2            "Private Service Edge C2C Local Dispatcher Bypass Phase2"
#define FEATURE_GROUP_PRIVATE_SERVICE_EDGE_HOSTNAME_VALIDATION                     "Private Service Edge Hostname Validation"
#define FEATURE_GROUP_PRIVATE_SERVICE_EDGE_APP_BUFFER_TUNE                         "Private Service Edge Application Buffer Tune"
#define FEATURE_GROUP_PRIVATE_SERVICE_EDGE_USER_RISK                               "Private Service Edge User Risk"
#define FEATURE_GROUP_PRIVATE_SERVICE_EDGE_STATS                                   "Private Service Edge Stats"
#define FEATURE_GROUP_REDIRECT_POLICY                                              "Policy Redirect"
#define FEATURE_GROUP_REDIRECT_GRACE_DISTANCE                                      "Grace Distance Redirect"
#define FEATURE_GROUP_REDIRECT_USER_PREDICTION                                     "User Prediction Redirect"
#define FEATURE_GROUP_PRIVILEGED_REMOTE_ACCESS_ADVANCED                            "Privileged Remote Access - Advanced"
#define FEATURE_GROUP_PRIVILEGED_REMOTE_ACCESS_ESSENTIALS                          "Privileged Remote Access - Essentials"
#define FEATURE_GROUP_ARBITRARY_DOMAINS                                            "Arbitrary Domains"
#define FEATURE_GROUP_EXPORTER_CORS_TOKEN                                          "Exporter CORS Token"
#define FEATURE_GROUP_EXPORTER                                                     "Exporter"
#define FEATURE_GROUP_APP_CONNECTOR_STATS                                          "App Connector Stats"
#define FEATURE_GROUP_KEEP_ALIVE_ZCC_SESSION                                       "Keep Alive ZCC Session"
#define FEATURE_GROUP_SLOGGER                                                      "Slogger"
#define FEATURE_GROUP_PRIVATE_SERVICE_EDGE                                         "Private Service Edge"
#define FEATURE_GROUP_BRANCH_CONNECTOR                                             "Branch Connector"
#define FEATURE_GROUP_WALLY                                                        "Wally"
#define FEATURE_GROUP_GUACD_OPTIONAL                                               "Guacd Optional"
#define FEATURE_GROUP_ARGO_LOGGING                                                 "ARGO Logging"
#define FEATURE_GROUP_PSE_RESILIENCY                                               "Private Service Edge Resiliency"
#define FEATURE_GROUP_C_DISPATCHER_CONFIG                                          "C Dispatcher Config"
#define FEATURE_GROUP_ALT_CLOUD                                                    "Alternate Cloud"
#define FEATURE_GROUP_SARGE                                                        "SARGE"
#define FEATURE_GROUP_QUICKACK                                                     "Quickack"
#define FEATURE_GROUP_FOHH_CONFIG                                                  "FOHH Config"
#define FEATURE_GROUP_APP_CONNECTOR_CONFIG                                         "App Connector Config"
#define FEATURE_GROUP_APP_CONNECTOR_ZCDNS                                          "App Connector DNS Retry"
#define FEATURE_GROUP_APP_CONNECTOR_FLOW_CONTROL                                   "App Connector FOHH Flow Control"
#define FEATURE_GROUP_PSE_FLOW_CONTROL                                             "Private Service Edge FOHH Flow Control"
#define FEATURE_GROUP_FOHH_INTERVAL                                                "FOHH Interval"
#define FEATURE_GROUP_APP_CONNECTOR_APP_BUFFER                                     "App Connector App Buffer"
#define FEATURE_GROUP_APPLICATION_MULTI_MATCH                                      "Application Multi Match"
#define FEATURE_GROUP_APP_CONNECTOR_KEEPALIVE                                      "App Connector Keepalive"
#define FEATURE_GROUP_ADMIN_PROBE                                                  "Admin Probe"
#define FEATURE_GROUP_TCP_ADMIN_PROBE                                              "TCP Admin Probe"
#define FEATURE_GROUP_DNS_ADMIN_PROBE                                              "DNS Admin Probe"
#define FEATURE_GROUP_ICMP_ADMIN_PROBE                                             "ICMP Admin Probe"
#define FEATURE_GROUP_TCPDUMP_ADMIN_PROBE                                          "Tcpdump Admin Probe"
#define FEATURE_GROUP_APP_CONNECTOR_ADMIN_PROBE                                    "App Connector Admin Probe"
#define FEATURE_GROUP_APP_CONNECTOR_TCP_ADMIN_PROBE                                "App Connector TCP Admin Probe"
#define FEATURE_GROUP_APP_CONNECTOR_DNS_ADMIN_PROBE                                "App Connector DNS Admin Probe"
#define FEATURE_GROUP_APP_CONNECTOR_ICMP_ADMIN_PROBE                               "App Connector ICMP Admin Probe"
#define FEATURE_GROUP_APP_CONNECTOR_ADMIN_PROBE_RESTART                            "App Connector Admin Probe Restart"
#define FEATURE_GROUP_APP_CONNECTOR_TCPDUMP_ADMIN_PROBE                            "App Connector Tcpdump Admin Probe"
#define FEATURE_GROUP_LEAF_WALLY_RECONNECT_TABLE_REG_PRIORITY                      "Wally Reconnect Table Registration Priority"
#define FEATURE_GROUP_BROKER_APP_BUFFER                                            "Broker App Buffer"
#define FEATURE_GROUP_BROKER_MTUNNEL                                               "Broker Micro Tunnel"
#define FEATURE_GROUP_RESTRICT_APPLICATION_DOWNLOAD                                "Restrict Application Download"
#define FEATURE_GROUP_ENQUEUE_DEQUEUE_FIX                                          "Enqueue Dequeue Fix"
#define FEATURE_GROUP_BROKER                                                       "Broker"
#define FEATURE_GROUP_APPLICATION_PATTERN_MATCH                                    "Application Pattern Match"
#define FEATURE_GROUP_POLICY_REEVALUATION                                          "Policy Re-evaluation"
#define FEATURE_GROUP_SERVER_VALIDATED_CLIENT_CERTIFICATE_POSTURE                  "Server Validated Client Certificate Posture"
#define FEATURE_GROUP_POLICY_EVALUATE_TYPE                                         "Policy Evaluate Type"
#define FEATURE_GROUP_POLICY_OVERRIDE                                              "Policy FQDN To Server IP"
#define FEATURE_GROUP_APPLICATION_MATCH_SIPA_ONLY_APPS                             "Application Match SIPA Only Apps For SIPA Client"
#define FEATURE_GROUP_NATURAL_RATE_LIMITING                                        "Natural Rate Limiting"
#define FEATURE_GROUP_BROKER_USER_RISK                                             "Broker User Risk"
#define FEATURE_GROUP_ZIA_INSPECTION                                               "ZIA Inspection"
#define FEATURE_GROUP_APP_CONNECTOR_EVENT                                          "App Connector Event"
#define FEATURE_GROUP_PRIVATE_SERVICE_EDGE_EVENT                                   "Private Service Edge Event"
#define FEATURE_GROUP_BROKER_BALANCE_REDIRECT                                      "Broker Balance Redirect"
#define FEATURE_GROUP_BROKER_PATH_CACHE                                            "Broker Path Cache"
#define FEATURE_GROUP_BROKER_DNS                                                   "Broker DNS"
#define FEATURE_GROUP_BROKER_DISPATCHER_GENERIC                                    "Broker Dispatcher Generic"
#define FEATURE_GROUP_BROKER_REQUEST_DIFFERENT_DISPATCHER                          "Broker Request Different Dispatcher"
#define FEATURE_GROUP_BROKER_TIMEOUT_DIFFERENT_DISPATCHER                          "Broker Request Timeout Different Dispatcher"
#define FEATURE_GROUP_BROKER_CLIENT                                                "Broker Client"
#define FEATURE_GROUP_BROKER_HOSTNAME_VALIDATION                                   "Broker Hostname Validation"
#define FEATURE_GROUP_BROKER_SIPA_CLIENT                                           "Broker SIPA Client"
#define FEATURE_GROUP_LATENCY_BASED_BROKER                                         "Latency Based Broker"
#define FEATURE_GROUP_BROKER_SESSION_AUTHENTICATION                                "Broker Session Authentication"
#define FEATURE_GROUP_LOGICAL_PARTITION_REDIRECT                                   "Logical Partition Redirect"
#define FEATURE_GROUP_BROKER_SYNC_PAUSE                                            "Broker Sync Pause"
#define FEATURE_GROUP_BROKER_WALLY_CLIENT                                          "Broker Wally Client"
#define FEATURE_GROUP_NEGATIVE_CACHE                                               "Negative Cache"
#define FEATURE_GROUP_VDI_ENABLE                                                   "VDI Enable"
#define FEATURE_GROUP_BROKER_DNS_STRICT_CHECK                                      "Broker DNS Strict Check"
#define FEATURE_GROUP_BROKER_SIEM                                                  "Broker to Slogger SIEM Transmit Context Controls"
#define FEATURE_GROUP_APP_CONNECTOR_DNS_STRICT_CHECK                               "App Connector DNS Strict Check"
#define FEATURE_GROUP_DISABLE_WINDOW_UPDATE_INNER_TUNNEL                           "Disable Window Update Inner Tunnel"
#define FEATURE_GROUP_EXPORTER_XFF_HEADER                                          "Exporter XFF Header"
#define FEATURE_GROUP_EXPORTER_FORWARDED_FOR_HEADER                                "Exporter Forwarded For Header"
#define FEATURE_GROUP_EXPORTER_BROWSER_FIN_PROPAGATION                             "Exporter FIN propagation to Browser"
#define FEATURE_GROUP_BROKER_PSE_IPV6_REDIRECT                                     "Broker PSE IPv6 Redirect"
#define FEATURE_GROUP_POLICY_REBUILD                                               "Policy Rebuild"
#define FEATURE_GROUP_MCONN_TRACK_PERF_STATS                                       "MConn Track Performance Statistics"
#define FEATURE_GROUP_EXPORTER_CLEAR_COOKIES                                       "Exporter Clear Cookies"
#define FEATURE_GROUP_STEPUP_AUTH                                                  "StepUp Authentication"

#define FEATURE_GROUP_SITE_CONTROLLER_STATS                                        "Site Controller Stats"
#define FEATURE_GROUP_SITE_CONTROLLER_LSS                                          "Site Controller LSS"
#define FEATURE_GROUP_SITE_CONTROLLER_STATIC_WALLY                                 "Site Controller Registration Wally"
#define FEATURE_GROUP_SITEC_SESSION_AUTHENTICATION                                 "Site Controller Session Authentication"
#define FEATURE_GROUP_SITE_CONTROLLER                                              "Site Controller"
#define FEATURE_GROUP_WORKLOAD_TAG_GROUP                                           "Workload Tag Group"
#define FEATURE_GROUP_AUTH_SNI                                                     "ZPA Authenticated SNI Tunnel"
#define FEATURE_GROUP_EXPORTER_CIPHER_KEY                                          "Cipher Key 128/256"
#define FEATURE_GROUP_EXPORTER_MANAGED_CHROME                                      "Exporter Managed-Chrome"
#define FEATURE_GROUP_EXTRANET                                                     "Extranet"
#define FEATURE_GROUP_ASST_DATABROKER_RESILIENCE                                   "App Connector Data Broker Resilience"
#define FEATURE_GROUP_NETWORK_PRESENCE                                             "Network Presence"
#define FEATURE_GROUP_NETWORK_PRESENCE_ACCESS_POLICY                               "Network Presence Access Policy"
#define FEATURE_GROUP_ZPN_SYS_STATS                                                "ZPN System Stats"
#define FEATURE_GROUP_DONT_DUMP                                                    "Dont Dump"
#define FEATURE_GROUP_AAE                                                          "Adapative Access Engine"
#define FEATURE_GROUP_REGION_RESTRICTION                                           "Region Restriction"
#define FEATURE_GROUP_POLICY_RE_EVAL_ON_SCIM_UPDATE                                "Policy Re-evaluation on SCIM Update"
#define FEATURE_GROUP_DISABLE_TX_ROUTE_REG_TO_PUB_BROKER                           "Disabling tx route registration msg public broker"
#define FEATURE_GROUP_QBR                                                          "QBR Insights"
#define FEATURE_GROUP_RBAC                                                         "Role-based access control(RBAC)"
#define FEATURE_GROUP_LIBEVENT_LOW_WRITE_WATERMARK                                 "Libevent output buffer low watermark"
#define FEATURE_GROUP_FOHH_FLOW_CONTROL_ENHANCEMENTS                               "Fohh flow control enhancements"
#define FEATURE_GROUP_ASST_UDP_HEALTH_TIMEOUT_FAILURE                              "App Connector UDP Health Timeout Failure"
#define FEATURE_GROUP_MCONN_WINDOW_UPDATES                                         "Mconn window updates"
#define FEATURE_GROUP_APP_RTT                                                      "APP RTT"
#define FEATURE_GROUP_PIPELINE_LATENCY_TRACE                                       "Pipeline latency trace"
#define FEATURE_GROUP_PRIVATE_SERVICE_EDGE_MALLOC_TRIM                             "Trigger Malloc trim on Pse"
#define FEATURE_GROUP_BROKER_DISPATCHER_CIRCUIT_BREAKER                            "Broker - Dispatcher Circuit Breaker"
#define FEATURE_GROUP_APP_THREAD_HEARTBEAT_OVERRIDE                                "App Thread Heartbeat Override"

#endif /* _ZPATH_CONFIG_FEATURE_GROUPS_H_ */
