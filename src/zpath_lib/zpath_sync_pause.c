/*
 * Pause/Resume syncing of a configured wally
 *
 * Entry points:
 *   register_pausable_table -> a table calls up the infra to add as a pausable table
 *   reevaluate_all_config -> a change has happened in feature configuration.
 */
#define _GNU_SOURCE

#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include <assert.h>

#include "wally/wally_private.h"
#include "zhash/zhash_table.h"
#include "zpath_lib/zpath_lib.h"
#include "zpath_lib/zpath_table.h"
#include "zpath_lib/zpath_sync_pause.h"
#include "zpath_lib/zpath_instance.h"
#include "zpath_lib/zpath_debug.h"
#include "zpath_lib/zpath_debug_wally.h"
#include "zpath_lib/zpath_app.h"
#include "zpath_lib/zpath_config_override.h"
#include "zpath_lib/zpath_config_override_keys.h"
#include "zpath_lib/zpath_partition_common.h"
#include "zpath_lib/zpath_assert.h"

// Conditional compilation for testing
#ifdef ZPATH_SYNC_PAUSE_TEST
extern int g_mock_tables_paused_return;
#endif

#define ZPATH_SYNC_PAUSE_STR_CONFIG_MAX_LEN 2048

/* Using the struct definitions from the header file instead of redefining them */
typedef struct zpath_sync_pause_db_state zpath_sync_pause_db_state_t;
typedef struct zpath_sync_pause_table_state zpath_sync_pause_table_state_t;

static struct {
    zpath_mutex_t           lock;
    int                     dynamic_init_done;
    struct zhash_table      *db_name_to_db_states;
} global_state;

/* Structure to hold table registration info */
struct zpath_sync_pause_table_info {
    const char *table_name;     /* Name of the table */
    int is_pausable;            /* Whether table can be paused */
};

/*
 * List of all master shard tables for broker. Any new table in master shard MUST be added here.
 * Set is_pausable=1 if table's updates need to be paused during rollout. Otherwise set it to 0.
 * NOTE: Not registering table here will cause assertion error. This is a development-time
 * enforcement to ensure explicit registration of all tables with sync pause infrastructure.
 */
static const struct zpath_sync_pause_table_info master_tables[] = {
    {"et_blocked_country", 1},
    {"et_customer_userdb", 1},
    {"et_customer_zone", 1},
    {"et_exempt_customer", 1},
    {"et_exempt_sni", 1},
    {"et_geoip_override", 1},
    {"et_service_endpoint", 1},
    {"et_translate", 0}, /* non-pausable; contains data only changed with DB upgrade; need not pause */
    {"et_translate_code", 0}, /* non-pausable; contains data only changed with DB upgrade; need not pause */
    {"et_userdb", 1},
    {"et_zone", 1},
    {"zpath_cloud", 0}, /* non-pausable; critical for cloud configuration during broker bootstrapping */
    {"zpath_config_override", 0}, /* non-pausable; essential for configuration overrides during broker bootstrapping */
    {"zpath_constellation", 1},
    {"zpath_constellation_instance", 1},
    {"zpath_customer_partition_override", 1},
    {"zpath_customer_to_constellation", 1},
    {"zpath_domain_lookup", 1},
    {"zpath_instance", 0}, /* non-pausable; necessary for instance management during broker bootstrapping */
    {"zpath_instance_partition_override", 1},
    {"zpath_partition", 1},
    {"zpath_partition_profile", 1},
    {"zpath_partition_profile_instance", 1},
    {"zpath_partition_profile_partition", 1},
    {"zpath_table", 0}, /* non-pausable; required for table management during broker bootstrapping */
    {"zpn_broker_balance_control", 0}, /* non-pausable; configuration information; not necessary to pause */
    {"zpn_broker_load", 0}, /* non-pausable; dynamic data reported by brokers; not necessary to pause */
    {"zpn_broker_proxy_sni", 1},
    {"zpn_inspection_config_data", 1},
    {"zpn_inspection_zsdefined_control", 1},
    {"zpn_version_control", 1},
    {NULL, 0}  /* Sentinel */
};

#define lock_global() ZPATH_MUTEX_LOCK(&(global_state.lock), __FILE__, __LINE__)
#define unlock_global() ZPATH_MUTEX_UNLOCK(&(global_state.lock), __FILE__, __LINE__)

/* fw declarations */
static __inline__ int zpath_sync_pause_should_pause_table(zpath_sync_pause_table_state_t *table_state);
/* Helper function to check if all tables have been resumed */
static int zpath_sync_pause_should_execute_post_resume_cb(void *cookie, void *object, void *key, size_t key_len);

/* Exported functions for test (always non-static) - Forward Declarations */
extern int zpath_sync_pause_walk_f_eval_table_state(void *cookie, void *object, void *key, size_t key_len);
extern int zpath_sync_pause_handle_local_pause(struct zpath_debug_state *request_state,
                                       const char **query_values,
                                       int query_value_count,
                                       void *object);
extern int zpath_wally_post_resume_callback(struct wally *wally);

/* forward declarations for internal static functions */
static void zpath_sync_pause_reevaluate_all_config();
static int zpath_sync_pause_show_config(struct zpath_debug_state *request_state,
                                        const char **query_values, int query_value_count, void *object);
static int zpath_sync_pause_show_status(struct zpath_debug_state *request_state,
                                        const char **query_values, int query_value_count, void *object);

static __inline__ void zpath_sync_pause_int_config_changed(const int64_t *target_address, int64_t impacted_gid)
{
    ZPATH_LOG(AL_NOTICE, "wally_pause_debug: Int changed Target: %"PRId64"  impacted_gid: %"PRId64" ",
                                *target_address, impacted_gid);
    if (*target_address != 0 && *target_address != 1) {
        ZPATH_LOG(AL_ERROR, "wally_pause_debug: Expected 0|1 received : %"PRId64" for impacted_gid: %"PRId64", ignoring!",
                                *target_address, impacted_gid);
        return;
    }
    zpath_sync_pause_reevaluate_all_config();
}

static __inline__ int zpath_sync_pause_should_pause_table(zpath_sync_pause_table_state_t *table_state)
{
    zpath_sync_pause_db_state_t *db_state = table_state->db_state;

    /* If db is unpaused, table is unpaused as well */
    return (int)(db_state->config_pause_db || db_state->cmd_line_paused);
}

int zpath_sync_pause_walk_f_eval_table_state(void *cookie, void *object, void *key, size_t key_len)
{
    int res = ZPATH_RESULT_NO_ERROR;
    zpath_sync_pause_table_state_t *table_state = object;
    const char* table_name = wally_table_name(table_state->table);

    if (table_state->paused != zpath_sync_pause_should_pause_table(table_state)) {
        ZPATH_LOG(AL_NOTICE, "wally_pause_debug: Table %s.%s needs state change; current pause status: %d (config=%"PRId64" cmd=%d)",
                        table_state->db_state->db_name, table_name, table_state->paused,
                        table_state->db_state->config_pause_db,
                        table_state->db_state->cmd_line_paused);
        // need to flip the state
        if (table_state->paused) {
            res = wally_table_resume(table_state->wally, table_name);
            /* Flip the state anyway, but log the error, if any*/
            table_state->paused = 0;
            ZPATH_LOG(res ? AL_ERROR : AL_NOTICE, "wally_pause_debug: resume table %s.%s, result=%s",
                        table_state->db_state->db_name, table_name, zpath_result_string(res));
        } else {
            res = wally_table_pause(table_state->wally, table_name);
            /* Flip the state anyway, but log the error, if any*/
            table_state->paused = 1;
            ZPATH_LOG(res ? AL_ERROR : AL_NOTICE, "wally_pause_debug: pause table %s.%s, result=%s",
                        table_state->db_state->db_name, table_name, zpath_result_string(res));
        }
    }
    /* We need hash table iteration to complete; hence return no error */
    return ZPATH_RESULT_NO_ERROR;
}

#ifdef ZPATH_SYNC_PAUSE_TEST
int zpath_sync_pause_walk_f_eval_db_state(void *cookie, void *object, void *key, size_t key_len)
#else
static int zpath_sync_pause_walk_f_eval_db_state(void *cookie, void *object, void *key, size_t key_len)
#endif
{
    int res = ZPATH_RESULT_NO_ERROR;
    zpath_sync_pause_db_state_t *db_state = object;
    int64_t table_key = 0;
    int all_tables_resumed = 1; /* assume all tables resumed until proven otherwise */

    /* First, check if any tables are currently paused */
    table_key = 0;
    while (zhash_table_walk(db_state->all_tables, &table_key, zpath_sync_pause_should_execute_post_resume_cb, &all_tables_resumed)
           == ZHASH_RESULT_INCOMPLETE) {}

    /* If all tables are already resumed, no need to check for post-resume callback later */
    int tables_were_paused = !all_tables_resumed;

    /* Process all tables */
    table_key = 0;
    res = zhash_table_walk(db_state->all_tables,
                        &table_key,
                        zpath_sync_pause_walk_f_eval_table_state,
                        NULL);

    /* After processing all tables, check if we need to trigger the post-resume callback */
    if (tables_were_paused) {
        /* Re-check if all tables are now resumed */
        all_tables_resumed = 1;
        table_key = 0;
        while (zhash_table_walk(db_state->all_tables, &table_key, zpath_sync_pause_should_execute_post_resume_cb, &all_tables_resumed)
               == ZHASH_RESULT_INCOMPLETE) {}

        /* If all tables have been resumed and we have a valid wally pointer, call the post-resume callback once */
        if (all_tables_resumed && db_state->wally) {
            ZPATH_LOG(AL_NOTICE, "wally_pause_debug: All tables processed for db %s, trigerring post-resume callback",
                     db_state->db_name);
            wally_check_post_resume_callback(db_state->wally);
        }
    }

    return res;
}

/* Helper function to check if all tables have been resumed */
static int zpath_sync_pause_should_execute_post_resume_cb(void *cookie, void *object, void *key, size_t key_len)
{
    int *all_tables_resumed = (int *)cookie;
    zpath_sync_pause_table_state_t *table_state = (zpath_sync_pause_table_state_t *)object;

    /* If any table is paused, we should not execute the callback */
    if (table_state->paused) {
        *all_tables_resumed = 0;
    }

    return 0; /* Return 0 to continue walking through all tables */
}

static void zpath_sync_pause_reevaluate_all_config()
{
    int64_t key = 0;
    // no-op if no table of interest has been triggered yet.
    if (!global_state.dynamic_init_done)
        return;

    lock_global();
    (void)zhash_table_walk(global_state.db_name_to_db_states, &key,
                            zpath_sync_pause_walk_f_eval_db_state, NULL);
    unlock_global();
}

/*
 * zpath_sync_pause_check_global_config_init
 *  Checks if override_init is success, if not, we will not pick this config.
 */
static int zpath_sync_pause_check_global_config_init(void)
{
    int res = ZPATH_RESULT_NO_ERROR;

    if (global_state.dynamic_init_done)
        return res;

    res = zpath_config_override_init(NULL, 0, 0, config_component_broker);
    if (res) {
        ZPATH_LOG(AL_ERROR, "wally_pause_debug: zpath_config_override_init failed: %s",
                            zpath_result_string(res));
        return res;
    }
    global_state.dynamic_init_done = 1;
    return res;
}

const char * zpath_sync_pause_get_config_override_key(const char *db_name)
{
    if (!db_name)
        return NULL;

    /*
     * get config override macro based on the db.
     * only global_wally supported as of now ; add more here when its
     */
    if (!strcmp("global_wally", db_name)) {
        return CONFIG_FEATURE_BROKER_SYNC_PAUSE_WALLY_GLOBAL;
    }
    return NULL;
}

static struct zpath_config_override_desc zpn_broker_config_override_desc_sync_pause_common = {
    .key                = CONFIG_FEATURE_BROKER_SYNC_PAUSE_WALLY_GLOBAL,
    .desc               = "Global database sync pause/resume",
    .details            = "Instructs broker to pause/resume db sync on certain tables in master\n"
                            "1: feature is enabled.\n"
                            "0: feature is disabled.\n"
                            "Order of check: component id, root customer id, global\n"
                            "default: 0, feature is disabled(no tables are paused)",
    .val_type           = config_type_int,
    .component_types    = config_component_broker,
    .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_zsroot | config_target_gid_type_global,
    .int_range_lo       = CONFIG_FEATURE_BROKER_SYNC_PAUSE_WALLY_DISABLED,
    .int_range_hi       = CONFIG_FEATURE_BROKER_SYNC_PAUSE_WALLY_ENABLED,
    .int_default        = CONFIG_FEATURE_BROKER_SYNC_PAUSE_WALLY_DEFAULT,
    .feature_group      = FEATURE_GROUP_BROKER_WALLY_CLIENT,
    .value_traits       = config_value_traits_feature_enablement
};

/*
 * zpath_sync_pause_get_or_create_db_state
 *  This routine does the initializations for sync pause
 *
 *  This is invoked when a table wants to register itself as a pausable table.
 *  Invocation by first table in a wally would initialize the wally config as well.
 *
 * IMPORTANT: assume global lock held.
 */
static zpath_sync_pause_db_state_t *
zpath_sync_pause_get_or_create_db_state(struct wally *wally)
{
    const char* db_name = wally_name(wally);
    zpath_sync_pause_db_state_t *db_state = zhash_table_lookup(global_state.db_name_to_db_states,
                                                db_name, strlen(db_name), NULL);
    if (db_state) {
        /* Make sure the wally pointer is set */
        if (!db_state->wally) {
            db_state->wally = wally;
        }
        return db_state;
    }

    if (ZPATH_INSTANCE_GID == 0) {
        ZPATH_LOG(AL_CRITICAL, "wally_pause_debug: zpath_instance is not yet initialized"
                                " when trying to create pause config for %s", db_name);
        assert(0);
    }

    db_state = ZLIB_CALLOC(sizeof(zpath_sync_pause_db_state_t));
    db_state->db_name = ZLIB_STRDUP(db_name, strlen(db_name));
    db_state->all_tables = zhash_table_alloc(&zhash_table_allocator);
    db_state->cmd_line_paused = 0;
    db_state->wally = wally;  /* Store the wally pointer */
    zhash_table_store(global_state.db_name_to_db_states, (void*)db_name, strlen(db_name),
                        0, (void*)db_state);

    /* update state in wally as well */
    zpath_debug_wally_register_sync_pause(wally);

    /* register config override .. we support only global_wally now */
    zpn_broker_config_override_desc_sync_pause_common.key = zpath_sync_pause_get_config_override_key(db_name);
    if (zpn_broker_config_override_desc_sync_pause_common.key) {
        int res = zpath_config_override_desc_register(&zpn_broker_config_override_desc_sync_pause_common);
        if (res) {  /* we will log the error; not bail out though */
            ZPATH_LOG(AL_ERROR, "wally_pause_debug: Unable to register sync pause global key: %s, err: %s",
                    zpn_broker_config_override_desc_sync_pause_common.key, zpath_result_string(res));
        }
    }

    /* is db paused? e.g config.feature.broker.pause.wally.<wally_name>  */
    char db_config_key[ZPATH_SYNC_PAUSE_STR_CONFIG_MAX_LEN];
    snprintf(db_config_key, sizeof(db_config_key), CONFIG_FEATURE_BROKER_SYNC_PAUSE_WALLY, db_name);
    db_state->config_pause_db =
                zpath_config_override_get_config_int(db_config_key,
                                        &db_state->config_pause_db,
                                        0,
                                        (int64_t)ZPATH_INSTANCE_GID,
                                        (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                        (int64_t)0);
    zpath_config_override_monitor_int(db_config_key,
                                        &db_state->config_pause_db,
                                        zpath_sync_pause_int_config_changed,
                                        0,
                                        (int64_t)ZPATH_INSTANCE_GID,
                                        (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                        (int64_t)0);
    ZPATH_LOG(AL_NOTICE, "wally_pause_debug: monitor %s. initial value is %"PRId64, db_config_key, db_state->config_pause_db);
    ZPATH_LOG(AL_NOTICE, "wally_pause_debug: db state for wally %s created", db_name);
    return db_state;
}

/*
 * zpath_sync_pause_register_pausable_table
 *  Register a table with the sync pause infra to enable pause/unpause.
 *  Any table that wishes to enroll for this ,has to call this API.
 */
void zpath_sync_pause_register_pausable_table(struct wally *wally, struct wally_table *table)
{
    if (!wally || !table) {
        ZPATH_LOG(AL_ERROR, "wally_pause_debug: Invalid input in register pausable table");
        return;
    }

    const char* db_name = wally_name(wally);
    const char* table_name = wally_table_name(table);
    ZPATH_LOG(AL_NOTICE, "wally_pause_debug: registering table  %s.%s", db_name, table_name);

    lock_global();

    if (zpath_sync_pause_check_global_config_init()) {
        ZPATH_LOG(AL_ERROR, "wally_pause_debug: Registering table %s.%s failed", db_name, table_name);
        goto pause_exit;
    }

    zpath_sync_pause_db_state_t *db_state = zpath_sync_pause_get_or_create_db_state(wally);
    if (!db_state) {
        ZPATH_LOG(AL_ERROR, "wally_pause_debug: Failed to register table %s.%s : couldnt get db_state",
                            db_name, table_name);
        goto pause_exit;
    }

    zpath_sync_pause_table_state_t *table_state = zhash_table_lookup(db_state->all_tables, table_name, strlen(table_name), NULL);
    if (!table_state) {
        table_state = ZLIB_CALLOC(sizeof(zpath_sync_pause_table_state_t));
        table_state->wally = wally;
        table_state->table = table;
        table_state->db_state = db_state;
        table_state->paused = 0;
        zhash_table_store(db_state->all_tables, (void*)table_name, strlen(table_name), 0, (void*)table_state);

        /* update state in wally_table */
        wally_table_set_is_pausable(table, 1);

        ZPATH_LOG(AL_NOTICE, "wally_pause_debug: table state for %s.%s created", db_name, table_name);
    }

    if (zpath_sync_pause_should_pause_table(table_state)) {
        int res = wally_table_pause(wally, table_name);
        table_state->paused = 1;
        ZPATH_LOG(res ? AL_ERROR : AL_NOTICE, "wally_pause_debug: pause table %s.%s, res=%s", db_name, table_name, zpath_result_string(res));
    }

pause_exit:
    unlock_global();
    return;
}

/*
 * zpath_sync_pause_handle_local_pause
 *  Handles command-line requests to pause/unpause a global wally.
 *  Implements precedence rules:
 *  - Cannot override config pause with command
 *  - Only unpause via command if command-line paused
 *  - Maintains both pause states when both sources pause
 */
int zpath_sync_pause_handle_local_pause(struct zpath_debug_state *request_state,
                                       const char **query_values,
                                       int query_value_count,
                                       void *object)
{
    int result;
    const char* wally_name;
    int pause;
    zpath_sync_pause_db_state_t *db_state;

    result = ZPATH_RESULT_NO_ERROR;

    if (!query_values || query_value_count < 2 || !query_values[0] || !query_values[1]) {
        ZPATH_LOG(AL_ERROR, "wally_pause_debug: Both wally and pause parameters are required. Usage: wally=<name>&pause=<0|1>");
        ZDP("Encountered query string error. Both wally and pause parameters are required.\n"
            "Usage: wally=<name>&pause=<0|1>\n"
            "Note: Currently only global_wally is supported for wally name.\n");
        return ZPATH_RESULT_NO_ERROR;
    }

    wally_name = query_values[0];
    pause = atoi(query_values[1]);

    if (pause != 0 && pause != 1) {
        ZPATH_LOG(AL_ERROR, "wally_pause_debug: Invalid pause value %d. Must be 0 or 1", pause);
        ZDP("Invalid Parameter: pause value %d is not valid. Must be 0 or 1\n", pause);
        return ZPATH_RESULT_ERR;
    }

    if (!wally_name) {
        ZPATH_LOG(AL_ERROR, "wally_pause_debug: NULL wally name");
        return ZPATH_RESULT_ERR;
    }

    if (strcmp(wally_name, "global_wally") != 0) {
        ZPATH_LOG(AL_ERROR, "wally_pause_debug: Only global_wally supported, got %s", wally_name);
        return ZPATH_RESULT_ERR;
    }

    lock_global();

    db_state = zhash_table_lookup(global_state.db_name_to_db_states,
                                  wally_name,
                                  strlen(wally_name),
                                  NULL);

    if (!db_state) {
        ZPATH_LOG(AL_ERROR, "wally_pause_debug: DB state not found for %s", wally_name);
        unlock_global();
        return ZPATH_RESULT_NOT_FOUND;
    }

    if (pause) {
        if (db_state->config_pause_db) {
            ZPATH_LOG(AL_NOTICE, "wally_pause_debug: %s already paused via config, ignoring command",
                    wally_name);
            ZDP("Command-line pause ignored: %s already paused via config\n", wally_name);
            unlock_global();
            return result;
        }

        db_state->cmd_line_paused = 1;
        ZPATH_LOG(AL_NOTICE, "wally_pause_debug: Command-line pause set for %s", wally_name);
        ZDP("Command-line pause set for %s\n", wally_name);
    } else {
        if (!db_state->cmd_line_paused) {
            ZPATH_LOG(AL_NOTICE, "wally_pause_debug: %s not command-line paused, ignoring unpause",
                    wally_name);
            ZDP("Command-line unpause ignored: %s not command-line paused\n", wally_name);
            unlock_global();
            return result;
        }

        db_state->cmd_line_paused = 0;
        ZPATH_LOG(AL_NOTICE, "wally_pause_debug: Command-line pause cleared for %s", wally_name);
        ZDP("Command-line pause cleared for %s\n", wally_name);
    }

    unlock_global();
    zpath_sync_pause_reevaluate_all_config();

    return result;
}

#ifdef ZPATH_SYNC_PAUSE_TEST
int zpath_sync_pause_walk_f_validate_table(void *cookie, void *object, void *key, size_t key_len)
#else
static int zpath_sync_pause_walk_f_validate_table(void *cookie, void *object, void *key, size_t key_len)
#endif
{
    if (!object) {
        ZPATH_LOG(AL_ERROR, "wally_pause_debug: Invalid table state in validate_table");
        return ZPATH_RESULT_NO_ERROR;  // Return success to continue iteration
    }

    struct wally_table *table = (struct wally_table *)object;
    const char *table_name = wally_table_name(table);
    struct zhash_table *master_tables_hash = (struct zhash_table *)cookie;

    if (table_name) {
        void *found = zhash_table_lookup(master_tables_hash, table_name, strlen(table_name), NULL);
        ZPATH_ASSERT_HARD(found ? 1 : 0, "zpn_lib", zpath_is_dev_environment(), "zpath_sync_pause",
                         "Table %s exists in wally but not registered in sync pause infrastructure master_tables array",
                         table_name);
    } else {
        ZPATH_LOG(AL_ERROR, "wally_pause_debug: NULL table name encountered in validation");
    }

    return ZPATH_RESULT_NO_ERROR;
}

/*
 * zpath_sync_pause_validate_broker_tables
 *  Validates if all broker tables are registered with sync pause infrastructure.
 *  For each table loaded in broker, checks if it exists in master_tables array.
 *  Will assert if any broker table is not registered, with error logs.
 */
void zpath_sync_pause_validate_broker_tables(struct wally *wally)
{
    if (!wally || !wally->tables_hash) {
        ZPATH_LOG(AL_ERROR, "wally_pause_debug: Invalid wally or tables_hash in validate_broker_tables");
        return;
    }

    /* Create a hash table of master_tables for lookup */
    struct zhash_table *master_tables_hash = zhash_table_alloc(&zpath_lib_allocator);
    if (!master_tables_hash) {
        ZPATH_LOG(AL_ERROR, "wally_pause_debug: Failed to allocate hash table for master_tables");
        return;
    }

    /* Populate the hash with master_tables entries */
    const struct zpath_sync_pause_table_info *info;
    static int dummy_value = 1;

    for (info = master_tables; info->table_name != NULL; info++) {
        /* Store the table name with a dummy value (not NULL) */
        if (zhash_table_store(master_tables_hash, (void*)info->table_name,
                             strlen(info->table_name), 1, &dummy_value) != ZHASH_RESULT_NO_ERROR) {
            ZPATH_LOG(AL_ERROR, "wally_pause_debug: Failed to store table %s in master_tables_hash",
                     info->table_name);
        }
    }

    /* Walk through all tables in wally and validate against master_tables_hash */
    int64_t table_key = 0;  // Fix: Declare the table_key variable
    (void)zhash_table_walk(wally->tables_hash, &table_key,
                           zpath_sync_pause_walk_f_validate_table, master_tables_hash);

    zhash_table_free(master_tables_hash);

    lock_global();

    /* Check if pausable tables are properly registered with sync pause infrastructure */
    if (info->is_pausable) {
        zpath_sync_pause_db_state_t *db_state = zhash_table_lookup(global_state.db_name_to_db_states,
                                                                    wally_name(wally),
                                                                    strlen(wally_name(wally)),
                                                                    NULL);
        if (db_state) {
            if (info->table_name) {
                zpath_sync_pause_table_state_t *table_state = zhash_table_lookup(db_state->all_tables,
                                                                                info->table_name,
                                                                                strlen(info->table_name),
                                                                                NULL);
                ZPATH_ASSERT_HARD(table_state ? 1 : 0, "zpn_lib", zpath_is_dev_environment(), "zpath_sync_pause",
                                    "Pausable table %s is not registered with sync pause infrastructure. Call zpath_lib_sync_pause_register_table!",
                                    info->table_name);
            } else {
                ZPATH_LOG(AL_ERROR, "wally_pause_debug: Null table name encountered in table registration check");
            }
        }
    }
    unlock_global();
}

/*
 * zpath_sync_pause_are_global_wally_tables_paused
 * Checks if any tables are currently paused or in the process of resuming.
 * Returns 1 if paused or resuming, 0 if all tables are fully resumed.
 * Uses mock implementation if ZPATH_SYNC_PAUSE_TEST is defined.
 */
int zpath_sync_pause_are_global_wally_tables_paused(void)
{
#ifdef ZPATH_SYNC_PAUSE_TEST
    // Mock implementation: return the controlled value immediately
    return g_mock_tables_paused_return;
#endif // ZPATH_SYNC_PAUSE_TEST

    int result = 0;

    lock_global();

    /* Look for the global_wally db_state */
    zpath_sync_pause_db_state_t *db_state = zhash_table_lookup(global_state.db_name_to_db_states,
                                            "global_wally", strlen("global_wally"), NULL);
    if (!db_state) {
        unlock_global();
        return 0;
    }

    /* If the entire DB is paused via config or command line, then all tables are paused */
    if (db_state->config_pause_db || db_state->cmd_line_paused) {
        result = 1;
    } else {
        if (!wally_all_tables_resume_completed(zpath_global_wally)) {
            ZPATH_LOG(AL_NOTICE, "wally_pause_debug: Some tables are still in the process of resuming");
            result = 1;
        }
    }

    unlock_global();
    return result;
}

/*
 * zpath_wally_post_resume_callback
 * Generic callback triggered after all paused tables in a wally have been resumed.
 * Can be used by multiple features that need post-resume operations.
 */
int zpath_wally_post_resume_callback(struct wally *wally)
{
    int res = ZPATH_RESULT_NO_ERROR;

    if (!wally) {
        ZPATH_LOG(AL_ERROR, "wally_pause_debug: Null wally in post resume callback");
        return ZPATH_RESULT_ERR;
    }

    ZPATH_LOG(AL_NOTICE, "wally_pause_debug: Post-resume callback triggered for wally %s", wally->name);

    /*
     * Logical Partitioning Profile Activation
     * This will get the latest profile version after resume and apply it
     */
    ZPATH_LOG(AL_NOTICE, "wally_pause_debug: Getting latest partition profile version after tables resumed");
    res = zpath_partition_profile_activate_latest_profile();
    if (res) {
        ZPATH_LOG(AL_ERROR, "wally_pause_debug: Failed to reactivate partition profile after resume");
    } else {
        ZPATH_LOG(AL_NOTICE, "wally_pause_debug: Successfully updated to latest profile version after resume");
    }

    return ZPATH_RESULT_NO_ERROR;
}

int zpath_sync_pause_init(const char* role)
{
    int res = ZPATH_RESULT_NO_ERROR;

    /* Initialize the infra only for public brokers */
    if (!role || strcmp(role, "zpn_brokerd"))
        return res;

    memset(&global_state, 0, sizeof(global_state));
    global_state.lock = ZPATH_MUTEX_INIT;
    global_state.db_name_to_db_states = zhash_table_alloc(&zpath_lib_allocator);
    zhash_table_set_lock_protected(global_state.db_name_to_db_states);

    /* setup the registration callback */
    zpath_lib_sync_pause_regn_cb = zpath_sync_pause_register_pausable_table;

    res = zpath_debug_add_safe_read_command("show current wally table pause/resume configuration",
                "/wally/sync_pause/config",
                zpath_sync_pause_show_config,
                NULL,
                "wally",        "optional, wally name (partial match)",
                "table",        "optional, table name (partial match)",
                "all",          "optional, print all tables (otherwise only paused tables)",
                NULL);

    res = zpath_debug_add_safe_read_command("show status of current wally table pause/resume configuration",
                "/wally/sync_pause/status",
                zpath_sync_pause_show_status,
                NULL,
                "wally",        "optional, wally name (partial match)",
                "table",        "optional, table name (partial match)",
                NULL);
    if (res != ZPATH_RESULT_NO_ERROR) {
        ZPATH_LOG(AL_ERROR, "could not add debug command /wally/sync_pause/status");
        return res;
    }

    return res;
}

static int zpath_sync_pause_walk_f_print_table_state(void *cookie1, void *cookie2,
                                                     void *cookie3, void *cookie4, void *cookie5,
                                                    void *object, void *key, size_t key_len)
{
    struct zpath_debug_state *request_state = (struct zpath_debug_state*)cookie1;
    zpath_sync_pause_table_state_t *table_state = object;

    /* match "table" search parameter */
    char *table_filter = (char *)cookie3;
    if (table_filter && !strcasestr(wally_table_name(table_state->table), table_filter))
        return ZPATH_RESULT_NO_ERROR;

    /* if not "all", print only paused tables */
    if (!cookie4 && !table_state->paused)
        return ZPATH_RESULT_NO_ERROR;

    ZDP("\t%-48s paused: %-2d", wally_table_name(table_state->table), table_state->paused);
    if (table_state->paused) {
        ZDP(" paused_seq: %-8"PRId64" ", wally_table_paused_sequence(table_state->table));
        if (wally_table_max_sequence_paused(table_state->table))
            ZDP(" max_seq_paused: %-8"PRId64" ", wally_table_max_sequence_paused(table_state->table));
    }
    ZDP("\n");

    return ZPATH_RESULT_NO_ERROR;
}

static int zpath_sync_pause_walk_f_print_db_state(void *cookie1, void *cookie2,
                                                  void *cookie3, void *cookie4, void *cookie5,
                                                  void *object,
                                                  void *key, size_t key_len)
{
    struct zpath_debug_state *request_state = (struct zpath_debug_state*)cookie1;
    zpath_sync_pause_db_state_t *db_state = object;

    /* match "wally" search parameter */
    char *wally_filter = (char *)cookie2;
    if (wally_filter && !strcasestr(db_state->db_name, wally_filter))
        return ZPATH_RESULT_NO_ERROR;

    ZDP("%s Pause: %d\n", db_state->db_name, db_state->config_pause_db ? 1 : 0);

    int64_t table_key = 0;
    (void)zhash_table_walk2(db_state->all_tables,
                        &table_key,
                        zpath_sync_pause_walk_f_print_table_state,
                        cookie1, cookie2, cookie3, cookie4, cookie5);
    return ZPATH_RESULT_NO_ERROR;
}

static int zpath_sync_pause_show_config(struct zpath_debug_state *request_state,
                                        const char **query_values,
                                        int query_value_count,
                                        void *object)
{
    int64_t wally_key = 0;

    lock_global();
    (void)zhash_table_walk2(global_state.db_name_to_db_states,
                        &wally_key,
                        zpath_sync_pause_walk_f_print_db_state,
                        request_state,          /* req state */
                        (void *)query_values[0], /* wally name */
                        (void *)query_values[1], /* table name */
                        (void *)query_values[2], /* 'all' option */
                        NULL);
    unlock_global();

    return ZPATH_RESULT_NO_ERROR;
}

static int zpath_sync_pause_walk_table_status(void *cookie1, void *cookie2,
                                              void *cookie3, void *cookie4, void *cookie5,
                                              void *object, void *key, size_t key_len)
{
    struct zpath_debug_state *request_state = (struct zpath_debug_state*)cookie1;
    zpath_sync_pause_table_state_t *table_state = object;

    /* match "table" search parameter */
    char *table_filter = (char *)cookie3;
    if (table_filter && !strcasestr(wally_table_name(table_state->table), table_filter))
        return ZPATH_RESULT_NO_ERROR;

    ZDP("\t\t%-45s Status: %-20s\n", wally_table_name(table_state->table),
        wally_table_get_pause_status_str(wally_table_pause_status(table_state->table)));

    return ZPATH_RESULT_NO_ERROR;
}

static int zpath_sync_pause_walk_db_status(void *cookie1, void *cookie2,
                                            void *cookie3, void *cookie4, void *cookie5,
                                            void *object,
                                            void *key, size_t key_len)
{
    struct zpath_debug_state *request_state = (struct zpath_debug_state*)cookie1;
    zpath_sync_pause_db_state_t *db_state = object;

    /* match "wally" search parameter */
    char *wally_filter = (char *)cookie2;
    if (wally_filter && !strcasestr(db_state->db_name, wally_filter))
        return ZPATH_RESULT_NO_ERROR;

    ZDP("%s\n", db_state->db_name);
    ZDP("Pause Status: Config Override: %d, Command Line: %d, Effective Status: %s\n\n",
        db_state->config_pause_db ? 1 : 0,
        db_state->cmd_line_paused ? 1 : 0,
        (db_state->config_pause_db || db_state->cmd_line_paused) ? "Paused" : "Not Paused");

    int64_t table_key = 0;
    (void)zhash_table_walk2(db_state->all_tables,
                        &table_key,
                        zpath_sync_pause_walk_table_status,
                        cookie1, cookie2, cookie3, cookie4, cookie5);
    return ZPATH_RESULT_NO_ERROR;
}

static int zpath_sync_pause_show_status(struct zpath_debug_state *request_state,
                                        const char **query_values,
                                        int query_value_count,
                                        void *object)
{
    int64_t wally_key = 0;

    lock_global();
    (void)zhash_table_walk2(global_state.db_name_to_db_states,
                        &wally_key,
                        zpath_sync_pause_walk_db_status,
                        request_state,          /* req state */
                        (void *)query_values[0], /* wally name */
                        (void *)query_values[1], /* table name */
                        NULL,
                        NULL);
    unlock_global();

    return ZPATH_RESULT_NO_ERROR;
}
