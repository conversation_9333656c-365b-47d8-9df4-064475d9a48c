/*
 * assistant_data.c. Copyright (C) 2019 Zscaler Inc. All Rights Reserved.
 *
 * connector makes a data connection (TLS pipe) to each of the brokers that are asked by dispatchers using
 * SNI string of <assistant_gid>.<broker_gid>.adata.<cloud_name>
 *
 * If the data connection didn't see any tx/rx for 5hrs, we disconnect (note, its not deleted), which means the fohh
 * pointers are still valid, just that it doesn't try to reconnect till the time it is asked to connect again.
 *
 * For any data connection to pbroker, we have two options to gather more data about the pbroker - the config table
 * or pbroker_control module. While config table is the logical choice, it makes more engineering sense to depend on
 * pbroker_control just because pbroker_control module already tries hard to keep the environment sane (like deleting
 * the channel after 7 days of internet disconnection, deleting the control channel when pbroker is deleted from GUI..)
 *
 * For pbroker-data connection, when the pbroker is deleted by customer from GUI we are not doing anything special.
 * We just let the existing data flow through. Why? We expect the pbroker to kill itself when the config is deleted.
 * As of 2019, pbroker don't yet kill itself, <PERSON><PERSON> will fix it.
 */
#include "argo/argo_hash.h"
#include "fohh/fohh_private.h"
#include "fohh/fohh_resolver.h"
#include "ztlv/zpn_tlv.h"
#include "zpath_lib/zpath_config_override.h"
#include <netinet/in.h>
#include <netinet/tcp.h>
#include "zvm/zvm.h"
#include "zpn_assistantd/zpn_assistant_private.h"
#include "zpn_assistantd/zpn_assistant_mtunnel.h"
#include "zpn/zpn_system.h"
#include "zlibevent/zlibevent_bufferevent.h"
#include "zpn_assistantd/assistant_state.h"
#include "zpn/assistant_log.h"
#include "zpn_assistantd/assistant_pbroker.h"
#include "zpn_assistantd/assistant_cfg_pbroker.h"
#include "zpn_assistantd/assistant_data.h"
#include "zpn_assistantd/assistant_rpc_rx.h"
#include "zpn_assistantd/assistant_broker.h"
#include "zpn_assistantd/assistant_control_tx.h"
#include "zpn_assistantd/assistant_assert.h"
#include "zpn_assistantd/assistant_features.h"
#include "zpn_assistantd/assistant_cfg_override_feature.h"
#include "zpn_assistantd/assistant_pbroker_control.h"
#include "zpn_assistantd/assistant_util.h"
#include "zpn/zpn_fohh_worker.h"
#include "zpn_zdx/zpn_zdx_combiner.h"
#include "zpn_assistantd/assistant_additional_debug_logs.h"
#include "zpn_assistantd/zpn_assistant_two_hop_data_health.h"

/* 10 mins */
#define ASSISTANT_DATA_CONNECTION_LOG_FREQUENCY_US (10ll * 60ll * 1000ll * 1000ll)
#define ZPN_DOUBLE_HOP_DATA_BROKER_CONN_MAX_BACKOFF_S 30

static struct {
    /* brokers_lock is to protect brokers */
    struct zhash_table*     brokers;
    pthread_mutex_t         brokers_lock;
} state;

static struct assistant_data_stats {                                            /* _ARGO: * object_definition */
    /*
     * Below stats are delayed by upto 1min
     */
    int64_t stats_aggregated_cloud_time_us;                                     /* _ARGO: integer */
    int64_t data_conn_to_broker_fohh;                                           /* _ARGO: integer */
    int64_t data_conn_to_broker_dtls;                                           /* _ARGO: integer */
    int64_t backed_off_data_conn_to_broker_fohh;                                /* _ARGO: integer */
    int64_t backed_off_data_conn_to_broker_dtls;                                /* _ARGO: integer */
    int64_t to_broker_data_past_fohh;                                           /* _ARGO: integer */
    int64_t to_broker_data_past_dtls;                                           /* _ARGO: integer */
    int64_t to_broker_data_active_fohh;                                         /* _ARGO: integer */
    int64_t to_broker_data_active_dtls;                                         /* _ARGO: integer */
    int64_t from_broker_data_past_fohh;                                         /* _ARGO: integer */
    int64_t from_broker_data_past_dtls;                                         /* _ARGO: integer */
    int64_t from_broker_data_active_fohh;                                       /* _ARGO: integer */
    int64_t from_broker_data_active_dtls;                                       /* _ARGO: integer */
    int64_t data_conn_to_pbroker_fohh;                                          /* _ARGO: integer */
    int64_t backed_off_data_conn_to_pbroker_fohh;                               /* _ARGO: integer */
    int64_t data_conn_to_pbroker_dtls;                                          /* _ARGO: integer */
    int64_t backed_off_data_conn_to_pbroker_dtls;                               /* _ARGO: integer */
    int64_t to_pbroker_data_past_fohh;                                          /* _ARGO: integer */
    int64_t to_pbroker_data_past_dtls;                                          /* _ARGO: integer */
    int64_t to_pbroker_data_active_fohh;                                        /* _ARGO: integer */
    int64_t to_pbroker_data_active_dtls;                                        /* _ARGO: integer */
    int64_t from_pbroker_data_past_fohh;                                        /* _ARGO: integer */
    int64_t from_pbroker_data_past_dtls;                                        /* _ARGO: integer */
    int64_t from_pbroker_data_active_fohh;                                      /* _ARGO: integer */
    int64_t from_pbroker_data_active_dtls;                                      /* _ARGO: integer */

    int64_t tx_broker_request_ack_fail_no_broker;                               /* _ARGO: integer */
    int64_t tx_broker_request_ack_fail_no_connection;                           /* _ARGO: integer */
} stats;
#include "zpn_assistantd/assistant_data_compiled_c.h"


static struct {
    int64_t number_of_bind_messages_to_be_dropped;
    int64_t number_of_end_messages_to_be_dropped;
} ut_hook;

/*
 * exported_stats is the copy of the stats object which is exported to the cloud. This copy is made, to calculate
 * the delta/bps between previous and current stats. Delta/bps is useful only if the time window is significant and
 * meaningful to be able to calculate the delta/bps values. So, this value is updated only when a successful delta/bps
 * is calculated, otherwise will let the next run to calculate the delta and update this.
 */
static struct zpn_assistant_data_stats exported_stats;

static struct zpn_assistant_comprehensive_stats exported_comprehensive_stats;

static struct argo_structure_description*  assistant_data_stats_description;

static char assistant_data_sni_suffix[256];

static int
assistant_data_ut_setup_connection(struct zpath_debug_state*  request_state,
                                   const char **               query_values,
                                   int                         query_value_count,
                                   void*                       cookie);
static int
assistant_data_dump_connections(struct zpath_debug_state*  request_state,
                                const char **              query_values,
                                int                        query_value_count,
                                void*                      cookie);
static int
assistant_data_dump_connection(struct zpath_debug_state*  request_state,
                               const char **              query_values,
                               int                        query_value_count,
                               void*                      cookie);
static void
assistant_data_free(struct zpn_assistant_broker_data* broker,
                    struct fohh_connection* f_conn,
                    struct zrdt_conn* z_conn,
                    struct ztlv_info *info);
static int
assistant_data_stats_dump(struct zpath_debug_state*  request_state,
                          const char**               query_values,
                          int                        query_value_count,
                          void*                      cookie);

static void
assistant_data_log(evutil_socket_t sock, short flags, void *cookie);

static void assistant_data_broker_destroy(struct zpn_assistant_broker_data *broker);

static void
assistant_data_flow_control_recover_enable_check(struct fohh_connection* f_conn);

static void
assistant_data_flow_control_recover_enable_check(struct fohh_connection* f_conn)
{
    if (assistant_cfg_override_is_fc_reset_recover_enabled()) {

        int64_t timeout_interval_s;
        int64_t threshold_percent_times_hundred;

        timeout_interval_s = assistant_cfg_override_get_fc_reset_recover_interval_s();
        threshold_percent_times_hundred = assistant_cfg_override_get_fc_reset_recover_threshold_times_hundred();
        fohh_connection_enable_fc_reset_recover_and_update_parameters(f_conn, timeout_interval_s, threshold_percent_times_hundred);

        ASSISTANT_DEBUG_DATA("FOHH FC reset recover enable with timeout_interval %"PRId64", threshold percent %.2f ", timeout_interval_s, (double) (threshold_percent_times_hundred / 100 ) );
    }
}


static struct zpn_assistant_broker_data*
assistant_data_get_locked_internal(int64_t           broker_gid,
                                   int64_t           end_broker_gid,
                                   enum zpn_tlv_type tlv_type)
{
    struct zpn_assistant_broker_data* broker;
    struct zpn_assistant_broker_data_id id;

    id.broker_id = broker_gid;
    id.end_broker_id = end_broker_gid;
    id.tlv_type = tlv_type;

    pthread_mutex_lock(&(state.brokers_lock));
    broker = zhash_table_lookup(state.brokers, &id, sizeof(id), NULL);
    if (broker) pthread_mutex_lock(&(broker->lock));
    pthread_mutex_unlock(&state.brokers_lock);

    return broker;
}


struct zpn_assistant_broker_data*
assistant_data_get_locked(int64_t broker_gid, int64_t end_broker_gid)
{
    struct zpn_assistant_broker_data* broker;

    broker = assistant_data_get_locked_internal(broker_gid, end_broker_gid, zpn_fohh_tlv);
    if (!broker) {
        broker = assistant_data_get_locked_internal(broker_gid, end_broker_gid, zpn_zrdt_tlv);
    }

    return broker;
}

struct zpn_tlv *
assistant_data_get_tlv(struct zpn_assistant_broker_data *broker)
{
    if (broker->tlv_type == zpn_fohh_tlv) {
        return &(broker->broker_tlv_state.tlv);
    } else {
        return &(broker->broker_zrdt_tlv_state.tlv);
    }
}


/*
 * Debug interface for setting/unsetting UT behavior - drop bind msg. This is used to simulate the case of connector
 * dropping the bind message on its side and observe the effects.
 */
static int
assistant_data_rpc_tx_ut_drop_bind_msgs(struct zpath_debug_state*    request_state,
                                        const char**                 query_values,
                                        int                          query_value_count,
                                        void*                        cookie)
{
    if (query_values[0]) {
        ut_hook.number_of_bind_messages_to_be_dropped = strtoll(query_values[0], NULL, 10);
    } else {
        ut_hook.number_of_bind_messages_to_be_dropped = 1;
    }
    ZDP("Set to drop %lld bind messages\n", (long long)ut_hook.number_of_bind_messages_to_be_dropped);

    return ZPATH_RESULT_NO_ERROR;
}

/*
 * Debug interface for setting/unsetting UT behavior - drop bind msg. This is used to simulate the case of connector
 * dropping the end message on its side and observe the effects.
 */
static int
assistant_data_rpc_tx_ut_drop_end_msgs(struct zpath_debug_state*    request_state,
                                       const char**                 query_values,
                                       int                          query_value_count,
                                       void*                        cookie)
{
    if (query_values[0]) {
        ut_hook.number_of_end_messages_to_be_dropped = strtoll(query_values[0], NULL, 10);
    } else {
        ut_hook.number_of_end_messages_to_be_dropped = 1;
    }
    ZDP("Set to drop %lld zpn_mtunnel_end messages\n", (long long)ut_hook.number_of_end_messages_to_be_dropped);

    return ZPATH_RESULT_NO_ERROR;
}

int
assistant_data_init()
{
    int res;

    state.brokers = zhash_table_alloc(assistant_state_get_allocator());
    state.brokers_lock = (pthread_mutex_t) PTHREAD_MUTEX_INITIALIZER;

    if (!(assistant_data_stats_description = argo_register_global_structure(ASSISTANT_DATA_STATS_HELPER))) {
        ASSISTANT_LOG(AL_NOTICE, "Unable to create assistant data stats desc");
        res = ZPN_RESULT_ERR;
        goto done;
    }

    snprintf(assistant_data_sni_suffix, sizeof(assistant_data_sni_suffix), ".adata.%s",
             assistant_state_get_cloud_name());

    res = assistant_data_mtunnel_init();
    if (res) {
        ASSISTANT_LOG(AL_ERROR, "Could not initialize assistant data mtunnel : %s", zpn_result_string(res));
        goto done;
    }

    res = zpath_debug_add_write_command("UT: setup data connection to a broker/pbroker",
                                  "/assistant/data/ut/setup_connection",
                                  assistant_data_ut_setup_connection,
                                  NULL,
                                  "broker_gid", "GID of the broker/pbroker",
                                  "hostname", "IP or hostname of the broker/pbroker",
                                  "is_pbroker", "1 if gid is pbroker's 0 otherwise",
                                  NULL);
    if (res) {
        ASSISTANT_LOG(AL_ERROR, "Unable to register /assistant/data/ut/setup_connection");
        goto done;
    }

    res = zpath_debug_add_read_command("Dump data connection to a broker/pbroker",
                                  "/assistant/data/dump_connections",
                                  assistant_data_dump_connections,
                                  NULL,
                                  NULL);
    if (res) {
        ASSISTANT_LOG(AL_ERROR, "Unable to register /assistant/data/dump_connections");
        goto done;
    }

    res = zpath_debug_add_read_command("Dump data connection details of a broker/pbroker",
                                  "/assistant/data/dump_connection",
                                  assistant_data_dump_connection,
                                  NULL,
                                  "broker_gid", "Broker/pbroker gid",
                                  NULL);
    if (res) {
        ASSISTANT_LOG(AL_ERROR, "Unable to register /assistant/data/dump_connections");
        goto done;
    }

    res = zpath_debug_add_read_command("Dump data connection stats",
                                  "/assistant/data/dump",
                                  assistant_data_stats_dump,
                                  NULL,
                                  NULL);
    if (res) {
        ASSISTANT_LOG(AL_ERROR, "Could not register /assistant/data/dump to debug: %s", zpn_result_string(res));
        goto done;
    }

    if (assistant_state_is_dev_environment()) {
        res = zpath_debug_add_admin_command("Unit Test: Drop a certain number of bind messages to broker",
                                    "/assistant/data/rpc/tx/ut/drop_bind_msgs",
                                    assistant_data_rpc_tx_ut_drop_bind_msgs,
                                    NULL,
                                    "count", "Number of bind messages to drop",
                                    NULL);
        if (res) {
            ASSISTANT_LOG(AL_NOTICE, "Unable to register /assistant/data/rpc/tx/ut/drop_bind_msgs");
            goto done;
        }

        res = zpath_debug_add_admin_command("Unit Test: Drop a certain number of end messages to broker",
                                    "/assistant/data/rpc/tx/ut/drop_end_msgs",
                                    assistant_data_rpc_tx_ut_drop_end_msgs,
                                    NULL,
                                    "count", "Number of end messages to drop",
                                    NULL);
        if (res) {
            ASSISTANT_LOG(AL_NOTICE, "Unable to register /assistant/data/rpc/tx/ut/drop_end_msgs");
            goto done;
        }
    }

done:
    return res;
}

const char*
assistant_data_get_sni_suffix_from_broker_name(const char* buf, int buf_len)
{
    const char *s = buf;
    const char *e = buf + buf_len;
    int fragment = 0;

    while (s < e) {
        if (*s == '.') {
            fragment++;
        } else {
            if (fragment == 2) break; /* stop after 2 fragments */
        }
        s++;
    }
    return s;
}

static char *
assistant_data_get_sni(int64_t      broker_gid,
                       const char*  brk_name,
                       int          brk_name_len,
                       char*        out_buffer,
                       int          out_buffer_len,
                       int          is_pbroker,
                       char         *alt_cloud,
                       int          alt_cloud_enabled)
{
    char sni_suffix[256] = {0};
    if (global_assistant.alt_cloud_enabled) {
        if(is_pbroker) {
            snprintf(sni_suffix, sizeof(sni_suffix), ".adata.%s",(alt_cloud_enabled && alt_cloud && alt_cloud[0]) ? alt_cloud : assistant_state_get_cloud_name());
        } else {
            snprintf(sni_suffix, sizeof(sni_suffix), ".adata.%s", assistant_data_get_sni_suffix_from_broker_name(brk_name, brk_name_len));
        }
        snprintf(out_buffer, out_buffer_len, "%"PRId64".%"PRId64"%s", global_assistant.gid, broker_gid, sni_suffix);
    } else {
        snprintf(out_buffer, out_buffer_len, "%"PRId64".%"PRId64"%s", global_assistant.gid, broker_gid, assistant_data_sni_suffix);
    }
    ASSISTANT_DEBUG_DATA("SNI suffix used in connection %s SNI %s %"PRId64" %d %d %s", sni_suffix, out_buffer, global_assistant.alt_cloud_enabled, is_pbroker, alt_cloud_enabled, alt_cloud? alt_cloud: "");
    return out_buffer;
}


/*
 * If the data connection is IDLE for 5hrs, close it.
 */
static void
assistant_data_connection_monitor_cb(evutil_socket_t    sock,
                                     short              flags,
                                     void*              cookie)
{
    struct zpn_fohh_tlv *fohh_tlv = cookie;

    (void)sock;
    (void)flags;

    static int64_t counter = 0;

    zpn_mconn_fohh_tlv_fc_monitor(fohh_tlv, 0);

    if (fohh_tlv->tlv.type == zpn_fohh_tlv) {
        if (0 == (++counter % 5)) {
            assistant_data_flow_control_recover_enable_check(zpn_mconn_fohh_tlv_get_conn(fohh_tlv));
        }
    }

    if (fohh_tlv->fc_blocked_timestamp_initial) {
        if (epoch_us() - fohh_tlv->fc_blocked_timestamp_initial > FOHH_TLV_BLOCKED_RESET_US) {
            ASSISTANT_LOG(AL_ERROR, "Reset connection to %s, as it is flow control blocked for more than %ld seconds",
                    fohh_description(zpn_mconn_fohh_tlv_get_conn(fohh_tlv)), (long)FOHH_TLV_BLOCKED_RESET_US/1000000);
            /* Kill the connection */
            fohh_connection_disconnect(zpn_mconn_fohh_tlv_get_conn(fohh_tlv), FOHH_CLOSE_REASON_AST_DATA_CONN_FLOW_CONTROL);
        } else if (fohh_tlv->kick_flow_control_reset) {
            ASSISTANT_LOG(AL_ERROR, "Reset connection to %s, as fohh_tlv->continuous_kick_flow_control_seen %"PRId64" ",
                    fohh_description(zpn_mconn_fohh_tlv_get_conn(fohh_tlv)), fohh_tlv->continuous_kick_flow_control_seen);
            fohh_connection_disconnect(zpn_mconn_fohh_tlv_get_conn(fohh_tlv), FOHH_CLOSE_REASON_AST_DATA_CONN_FLOW_CONTROL);
        }
    }
}

static void
assistant_data_zrdt_connection_monitor_cb(evutil_socket_t    sock,
                                          short              flags,
                                          void*              cookie)
{
    //struct zpn_zrdt_tlv *zrdt_tlv = cookie;
}

static int
assistant_data_window_update_cb(void*               argo_cookie_ptr,
                                void*               argo_structure_cookie_ptr,
                                struct argo_object* object)
{
    struct zpn_assistant_broker_data *broker = argo_structure_cookie_ptr;
    struct zpn_tlv *tlv = assistant_data_get_tlv(broker);
    struct zpn_fohh_tlv_window_update *req = object->base_structure_void;
    char dump[800];
    int64_t now_us = epoch_us();

    (void)argo_cookie_ptr;
    global_assistant.num_window_update++;

    if (zpn_debug_get_from_bit(ASSISTANT_DEBUG_DATA_BIT)) {
        if (argo_object_dump(object, dump, sizeof(dump), NULL, 0) == ARGO_RESULT_NO_ERROR) {
            ASSISTANT_LOG(AL_DEBUG, "%s, Rx: %s",
                          fohh_description(zpn_mconn_fohh_tlv_get_conn(&(broker->broker_tlv_state))), dump);
        }
    }

    if (tlv->type != zpn_fohh_tlv) {
        /* Only do window update for fohh connection */
        return ZPN_RESULT_NO_ERROR;
    }
    ASSISTANT_LOG(AL_DEBUG, "Window update received %s with tag_id %d",
                  fohh_description(zpn_mconn_fohh_tlv_get_conn(&(broker->broker_tlv_state))),
                  req->tag_id);
    if (!req->tag_id) {
        /* For overall FOHH connection */
        ZPATH_MUTEX_LOCK(&(broker->broker_tlv_state.lock), __FILE__, __LINE__);
        if (req->tx_limit > broker->broker_tlv_state.tx_limit) {
            broker->broker_tlv_state.tx_limit = req->tx_limit;
            broker->broker_tlv_state.tx_limit_update_us = now_us;
            fohh_connection_set_stats_fohh_tx_limit(broker->broker_tlv_state.tlv.conn.f_conn, broker->broker_tlv_state.tx_limit);
        }
        if (req->tx_limit) {
            broker->broker_tlv_state.remote_fc_status = flow_ctrl_enabled;
        } else {
            broker->broker_tlv_state.remote_fc_status = flow_ctrl_disabled;
        }
        if (broker->broker_tlv_state.remote_rx_data != req->rx_data) {
            broker->broker_tlv_state.remote_rx_data_change_us = now_us;
            broker->broker_tlv_state.remote_rx_data = req->rx_data;
            fohh_connection_set_stats_fohh_remote_rx_data(broker->broker_tlv_state.tlv.conn.f_conn, broker->broker_tlv_state.tx_limit);
        }
        ZPATH_MUTEX_UNLOCK(&(broker->broker_tlv_state.lock), __FILE__, __LINE__);

        zpn_fohh_tlv_unblock_cb(&(broker->broker_tlv_state));
    } else {
        /* For an mconn inside FOHH connection */
        zpn_assistant_mtunnel_window_update(broker, req->tag_id, req->tx_limit, req->rx_data);
    }

    return ZPN_RESULT_NO_ERROR;
}

static int
assistant_data_window_update_batch_cb(void* argo_cookie_ptr,
                                      void* argo_structure_cookie_ptr,
                                      struct argo_object* object)
{
    struct zpn_assistant_broker_data *broker = argo_structure_cookie_ptr;
    struct zpn_tlv *tlv = assistant_data_get_tlv(broker);
    struct zpn_fohh_tlv_window_update_batch *zpn_fohh_tlv_window_update_batch = object->base_structure_void;
    char dump[800];
    int64_t now_us = epoch_us();
    (void)argo_cookie_ptr;
    global_assistant.num_window_update++;

    if (zpn_debug_get_from_bit(ASSISTANT_DEBUG_DATA_BIT)) {
        if (argo_object_dump(object, dump, sizeof(dump), NULL, 0) == ARGO_RESULT_NO_ERROR) {
            ASSISTANT_LOG(AL_DEBUG, "%s, Rx: %s",
                          fohh_description(zpn_mconn_fohh_tlv_get_conn(&(broker->broker_tlv_state))), dump);
        }
    }

    if (tlv->type != zpn_fohh_tlv) {
        /* Only do window update for fohh tlv connection */
        return ZPN_RESULT_NO_ERROR;
    }
    ASSISTANT_LOG(AL_DEBUG, "Batch window update; tag count: %d", zpn_fohh_tlv_window_update_batch->tag_id_count);
    for (int idx = 0; idx < zpn_fohh_tlv_window_update_batch->tag_id_count; idx++) {
      ASSISTANT_LOG(AL_DEBUG, "Batch window update received for tag_id: %d; tx_limit: %"PRId64" rx_data: %"PRId64,
                    zpn_fohh_tlv_window_update_batch->tag_id[idx],
                    zpn_fohh_tlv_window_update_batch->tx_limit[idx],
                    zpn_fohh_tlv_window_update_batch->rx_data[idx]);
      if (!zpn_fohh_tlv_window_update_batch->tag_id[idx]) {
          /* For overall FOHH connection */
          ZPATH_MUTEX_LOCK(&(broker->broker_tlv_state.lock), __FILE__, __LINE__);
          if (zpn_fohh_tlv_window_update_batch->tx_limit[idx] > broker->broker_tlv_state.tx_limit) {
            broker->broker_tlv_state.tx_limit = zpn_fohh_tlv_window_update_batch->tx_limit[idx];
            broker->broker_tlv_state.tx_limit_update_us = now_us;
            fohh_connection_set_stats_fohh_tx_limit(broker->broker_tlv_state.tlv.conn.f_conn, broker->broker_tlv_state.tx_limit);
          }
          if (zpn_fohh_tlv_window_update_batch->tx_limit[idx]) {
            broker->broker_tlv_state.remote_fc_status = flow_ctrl_enabled;
          } else {
            broker->broker_tlv_state.remote_fc_status = flow_ctrl_disabled;
          }
          if (broker->broker_tlv_state.remote_rx_data != zpn_fohh_tlv_window_update_batch->rx_data[idx]) {
            broker->broker_tlv_state.remote_rx_data_change_us = now_us;
            broker->broker_tlv_state.remote_rx_data = zpn_fohh_tlv_window_update_batch->rx_data[idx];
            fohh_connection_set_stats_fohh_remote_rx_data(broker->broker_tlv_state.tlv.conn.f_conn, broker->broker_tlv_state.tx_limit);
          }
          ZPATH_MUTEX_UNLOCK(&(broker->broker_tlv_state.lock), __FILE__, __LINE__);
          zpn_fohh_tlv_unblock_cb(&(broker->broker_tlv_state));
      } else {
          zpn_assistant_mtunnel_window_update(broker,
                                              zpn_fohh_tlv_window_update_batch->tag_id[idx],
                                              zpn_fohh_tlv_window_update_batch->tx_limit[idx],
                                              zpn_fohh_tlv_window_update_batch->rx_data[idx]);
      } // else
    } // for

    return ZPN_RESULT_NO_ERROR;
}

static int
assistant_data_bind_ack_cb(void*                argo_cookie_ptr,
                           void*                argo_structure_cookie_ptr,
                           struct argo_object*  object)
{
    struct zpn_assistant_broker_data *broker = argo_structure_cookie_ptr;
    struct zpn_mtunnel_bind_ack *req;
    int res;

    (void)argo_cookie_ptr;
    global_assistant.num_bind_ack++;

    req = object->base_structure_void;

    if (!req->mtunnel_id) {
        ASSISTANT_LOG(AL_CRITICAL, "bind_ack without mtunnel_id");
        return ZPN_RESULT_ERR;
    }

    if (!req->tag_id) {
        ASSISTANT_LOG(AL_CRITICAL, "bind_ack without tag");
        return ZPN_RESULT_ERR;
    }

    res = zpn_assistant_mtunnel_bind_ack_cb(broker, req->tag_id, req->mtunnel_id, req->error);

    return res;
}


static int
assistant_data_mtunnel_end_cb(void*                 argo_cookie_ptr,
                              void*                 argo_structure_cookie_ptr,
                              struct argo_object*   object)
{
    struct zpn_assistant_broker_data* broker = argo_structure_cookie_ptr;
    struct zpn_mtunnel_end*           req;
    char                              dump[800];

    (void)argo_cookie_ptr;
    global_assistant.num_mtunnel_end++;

    if (zpn_debug_get_from_bit(ASSISTANT_DEBUG_DATA_BIT)) {
        if (argo_object_dump(object, dump, sizeof(dump), NULL, 0) == ARGO_RESULT_NO_ERROR) {
            ASSISTANT_LOG(AL_DEBUG, "Rx: %s", dump);
        }
    }

    req = object->base_structure_void;

    if (!req->mtunnel_id && !req->tag_id) {
        ASSISTANT_LOG(AL_CRITICAL, "mtunnel_end without mtunnel_id or tag_id");
        return ZPN_RESULT_ERR;
    }

    if (assistant_state_is_dev_environment()) {
        __sync_fetch_and_sub_8(&(ut_hook.number_of_end_messages_to_be_dropped), 1);
        if (ut_hook.number_of_end_messages_to_be_dropped >= 0) {
            ASSISTANT_LOG(AL_NOTICE, "%s: dropped dropped per UT drop message config, still (%lld) end messages set to be dropped",
                                      req->mtunnel_id, (long long)ut_hook.number_of_end_messages_to_be_dropped);
            return ZPN_RESULT_NO_ERROR;
        }
    }

    return zpn_assistant_mtunnel_end(broker, req->mtunnel_id, req->tag_id, req->drop_data, req->error);
}


static int
assistant_data_tag_pause_cb(void*               argo_cookie_ptr,
                            void*               argo_structure_cookie_ptr,
                            struct argo_object* object)
{
    struct zpn_assistant_broker_data *broker = argo_structure_cookie_ptr;
    struct zpn_tlv *tlv = assistant_data_get_tlv(broker);
    struct zpn_mtunnel_tag_pause *req;
    char dump[800];

    (void)argo_cookie_ptr;
    global_assistant.num_tag_pause++;

    if (zpn_debug_get_from_bit(ASSISTANT_DEBUG_DATA_BIT)) {
        if (argo_object_dump(object, dump, sizeof(dump), NULL, 0) == ARGO_RESULT_NO_ERROR) {
            ASSISTANT_LOG(AL_DEBUG, "Rx: %s", dump);
        }
    }

    if (tlv->type != zpn_fohh_tlv) {
        /* Only do window update for fohh connection */
        return ZPN_RESULT_NO_ERROR;
    }

    req = object->base_structure_void;

    if (broker->broker_tlv_state.remote_fc_status == flow_ctrl_enabled) {
        /* Ignore pause/resume message if remote is doing flow control */
        return ZPN_RESULT_NO_ERROR;
    }

    if (!req->tag_id) {
        ASSISTANT_LOG(AL_CRITICAL, "mtunnel pause without tag_id - not expected");
        return ZPN_RESULT_ERR;
    }

    zpn_assistant_mtunnel_tag_pause(broker, req->tag_id);
    return ZPN_RESULT_NO_ERROR;
}


static int
assistant_data_tag_resume_cb(void*               argo_cookie_ptr,
                             void*               argo_structure_cookie_ptr,
                             struct argo_object* object)
{
    struct zpn_assistant_broker_data *broker = argo_structure_cookie_ptr;
    struct zpn_tlv *tlv = assistant_data_get_tlv(broker);
    struct zpn_mtunnel_tag_resume *req;
    char dump[800];

    (void)argo_cookie_ptr;
    global_assistant.num_tag_resume++;

    if (zpn_debug_get_from_bit(ASSISTANT_DEBUG_DATA_BIT)) {
        if (argo_object_dump(object, dump, sizeof(dump), NULL, 0) == ARGO_RESULT_NO_ERROR) {
            ASSISTANT_LOG(AL_DEBUG, "Rx: %s", dump);
        }
    }

    if (tlv->type != zpn_fohh_tlv) {
        /* Only do window update for fohh connection */
        return ZPN_RESULT_NO_ERROR;
    }

    req = object->base_structure_void;

    if (broker->broker_tlv_state.remote_fc_status == flow_ctrl_enabled) {
        /* Ignore pause/resume message if remote is doing flow control */
        return ZPN_RESULT_NO_ERROR;
    }

    if (!req->tag_id) {
        ASSISTANT_LOG(AL_CRITICAL, "mtunnel resume without tag_id - not expected");
        return ZPN_RESULT_ERR;
    }

    zpn_assistant_mtunnel_tag_resume(broker, req->tag_id);
    return ZPN_RESULT_NO_ERROR;
}


static int
assistant_data_rx_broker_request(void*               argo_cookie_ptr,
                                 void*               argo_structure_cookie_ptr,
                                 struct argo_object* object)
{
    struct zpn_assistant_broker_data*   broker;
    char                                connection_dbg_str[ASSISTANT_RPC_RX_CONNECTION_DBG_STR_LEN];

    if (assistant_debug_log & ASSISTANT_DEBUG_DATA_BIT) {
        char dump[5000];
        if (argo_object_dump(object, dump, sizeof(dump), NULL, 0) == ARGO_RESULT_NO_ERROR) {
            ASSISTANT_LOG(AL_DEBUG, "Rx: %s", dump);
        }
    }

    broker = argo_structure_cookie_ptr;
    __sync_fetch_and_add_8(&global_assistant.num_brk_req_from_ubrk, 1);
    snprintf(connection_dbg_str, sizeof(connection_dbg_str), "%s %s|%s", "Data",
             broker->is_pbroker ? "PBroker" : "Broker", broker->broker_name);
    assistant_rpc_rx_broker_request(broker->label, connection_dbg_str, object->base_structure_void);
    return ZPN_RESULT_NO_ERROR;
}


static int assistant_data_conn_register_rpc_calls(struct zpn_assistant_broker_data *broker)
{
    struct argo_state *argo;
    struct zpn_tlv *tlv = assistant_data_get_tlv(broker);
    int res;

    if (broker->tlv_type == zpn_fohh_tlv) {
        argo = fohh_argo_get_rx(zpn_mconn_fohh_tlv_get_conn(&(broker->broker_tlv_state)));
    } else {
        struct zpn_zrdt_argo_state *argo_state;

        argo_state = zrdt_get_msg_codec_state(broker->broker_zrdt_tlv_state.msg_stream);
        argo = argo_state->rx_argo;
    }

    /* Register zpn_bind_ack */
    if ((res = argo_register_structure(argo, zpn_mtunnel_bind_ack_description, assistant_data_bind_ack_cb, broker))) {
        ASSISTANT_LOG(AL_ERROR, "Could not register zpn_mtunnel_bind_ack for broker connection %s", zpn_tlv_description(tlv));
        goto done;
    }

    /* Register zpn_mtunnel_end */
    if ((res = argo_register_structure(argo, zpn_mtunnel_end_description, assistant_data_mtunnel_end_cb, broker))) {
        ASSISTANT_LOG(AL_ERROR, "Could not register zpn_mtunnel_end for broker connection %s", zpn_tlv_description(tlv));
        goto done;
    }

    /* Register zpn_mtunnel_tag_pause_request */
    if ((res = argo_register_structure(argo, zpn_mtunnel_tag_pause_description, assistant_data_tag_pause_cb, broker))) {
        ASSISTANT_LOG(AL_ERROR, "Could not register zpn_mtunnel_tag_pause for broker connection %s", zpn_tlv_description(tlv));
        goto done;
    }

    /* Register zpn_mtunnel_tag_resume_request */
    if ((res = argo_register_structure(argo, zpn_mtunnel_tag_resume_description, assistant_data_tag_resume_cb,
                                           broker))) {
        ASSISTANT_LOG(AL_ERROR, "Could not register zpn_mtunnel_tag_resume for broker connection %s", zpn_tlv_description(tlv));
        goto done;
    }

    /* Register zpn_fohh_tlv_window_update */
    if ((res = argo_register_structure(argo, zpn_fohh_tlv_window_update_description, assistant_data_window_update_cb, broker))) {
        ASSISTANT_LOG(AL_ERROR, "Could not register zpn_fohh_tlv_window_update for broker connection %s", zpn_tlv_description(tlv));
        goto done;
    }

    if ((res = argo_register_structure(argo, zpn_fohh_tlv_window_update_batch_description, assistant_data_window_update_batch_cb, broker))) {
        ASSISTANT_LOG(AL_ERROR, "Could not register zpn_fohh_tlv_window_update batch for broker connection %s", zpn_tlv_description(tlv));
        goto done;
    }

    /* Register zpn_broker_request */
    if (0 == broker->is_pbroker) {
        if ((res = argo_register_structure(argo, zpn_broker_request_description, assistant_data_rx_broker_request, broker))) {
            ASSISTANT_LOG(AL_ERROR, "Could not register zpn_broker_request for broker connection %s",
                          zpn_tlv_description(tlv));
            goto done;
        }
    }

    /*  Register combiner rpcs*/
    if ((res = argo_register_structure(argo, zpn_zdx_probe_legs_info_description, zpn_zdx_combiner_add_or_update_monitor_with_leg_report_async, broker))) {
        ASSISTANT_LOG(AL_ERROR, "Could not register zpn_zdx_probe_legs to get zdx reports from broker %s",
                zpn_tlv_description(tlv));
        goto done;
    }

done:
    return res;
}

/*
 * Connect, disconnect, retry events on the fohh pipe
 *
 * Two things to keep in mind when we reach here,
 * 1. data channel to broker is never freed. Only disconnected.
 * 2. data channel to pbroker is deleted & all references to it is freed. This is to make sure that when a fohh to
 * pbroker goes down, all states are cleaned. So that the next incarnation of the fohh will pickup any IP change in
 * pbroker(if any). This will make sure data channel is always picking the IP that control channel using to talk to
 * pbroker.
 */
static int
assistant_data_connection_cb(struct fohh_connection*     connection,
                             enum fohh_connection_state  fstate,
                             void*                       cookie)
{
    struct zpn_assistant_broker_data* broker = cookie;
    int                               res;
    int assistant_group_idx;

    if (!broker) {
        ASSISTANT_ASSERT_SOFT(0, "broker cookie is deleted, we should not reach there");
        res = ZPN_RESULT_NO_ERROR;
        goto verify_failed;
    }

    res = ZPN_RESULT_NO_ERROR;
    if (fstate == fohh_connection_connected) {
        if ((broker->is_pbroker) &&
            (ZPN_RESULT_NO_ERROR != assistant_pbroker_verify_connection_and_act(broker->broker_id, connection) )) {
            broker->fohh_verification_failed = 1;
            goto verify_failed;
        }
#ifdef __linux__
        if (zpn_assistant_global_assistant_get_quickack() ||
            assistant_features_is_quickack_enabled(
                    global_assistant.gid, &global_assistant.grp_gids[0], global_assistant.grp_gids_len)) {
            ASSISTANT_DEBUG_DATA("quickack at assistant connect %ld connection %s", (long) global_assistant.gid,
                                 fohh_description(connection));
            int tmp_val = 1;
            setsockopt(connection->sock, IPPROTO_TCP, TCP_QUICKACK, (void *)&tmp_val, sizeof(tmp_val));
        }

        if (zpn_assistant_global_assistant_get_quickack_read() ||
            assistant_features_is_quickack_read_enabled(
                    global_assistant.gid, &global_assistant.grp_gids[0], global_assistant.grp_gids_len)) {
            ASSISTANT_DEBUG_DATA("quickack read at assistant connect %ld", (long) global_assistant.gid);
            fohh_peer_set_quickack_read_config(connection);
        }
#endif
        if(assistant_features_is_low_write_watermark_enabled(global_assistant.gid)) {
            ASSISTANT_DEBUG_DATA("Assistant setting up low write watermark");
            fohh_peer_set_libevent_low_write_watermark_config(connection);
        }
        assistant_data_flow_control_recover_enable_check(connection);
        if(assistant_fohh_flow_control_enhancement_enabled(global_assistant.customer_gid)) {
            ASSISTANT_DEBUG_DATA("Assistant fohh flow control enhacement enabled %ld", (long)global_assistant.customer_gid);
            fohh_set_fohh_fc_enhacement_config(connection);
        }

        if(assistant_features_batched_mconn_window_updates_enabled(global_assistant.gid, &global_assistant.grp_gids[0],
                                                                   global_assistant.grp_gids_len)) {
            ASSISTANT_DEBUG_DATA("Assistant batched window updates enabled agid: %ld; cgid: %ld",
                               (long) global_assistant.gid, (long) global_assistant.customer_gid);
            for (assistant_group_idx = 0; assistant_group_idx < global_assistant.grp_gids_len; assistant_group_idx++) {
              ASSISTANT_DEBUG_DATA("Assistant batched window updates enabled group index: %d; aggid: %ld",
                                   assistant_group_idx, (long) global_assistant.grp_gids[assistant_group_idx]);
            }
            fohh_set_batched_mconn_window_updates_config(connection);
        }

        if(assistant_features_syn_app_rtt_enabled(
             global_assistant.gid, &global_assistant.grp_gids[0], global_assistant.grp_gids_len)) {
          ASSISTANT_DEBUG_DATA("Assistant syn status msg enabled agid: %ld; cgid: %ld",
                               (long) global_assistant.gid, (long) global_assistant.customer_gid);
          for (assistant_group_idx = 0; assistant_group_idx < global_assistant.grp_gids_len; assistant_group_idx++) {
            ASSISTANT_DEBUG_DATA("Assistant syn status msg enabled group index: %d; aggid: %ld",
                                 assistant_group_idx, (long) global_assistant.grp_gids[assistant_group_idx]);
          }
          fohh_set_syn_status_msg_config(connection);
	    }
        if(assistant_features_pipeline_latency_trace_enabled(global_assistant.gid, &global_assistant.grp_gids[0], global_assistant.grp_gids_len)) {
            ASSISTANT_DEBUG_DATA("Assistant pipeline latency trace enabled agid: %ld; cgid: %ld",
                                 (long) global_assistant.gid, (long) global_assistant.customer_gid);
            for (assistant_group_idx = 0; assistant_group_idx < global_assistant.grp_gids_len; assistant_group_idx++) {
              ASSISTANT_DEBUG_DATA("Assistant pipeline latency trace enabled group index: %d; aggid: %ld",
                                   assistant_group_idx, (long) global_assistant.grp_gids[assistant_group_idx]);
            }
            fohh_set_pipeline_latency_trace_config(connection);
        }
        ASSISTANT_LOG(AL_NOTICE, "Data channel successfully connected to %s(%"PRId64"): %s",
                      broker->is_pbroker ? "Private Broker" : "Broker", broker->broker_id,
                      fohh_description(connection));
        /* Immediately send version string */
        res = fohh_argo_send_version(connection);
        if (res) {
            ASSISTANT_LOG(AL_WARNING, "%s: Could not send version", fohh_description(connection));
        }

        broker->fohh_conn_state = fstate;

        broker->label = assistant_broker_register(broker->hash_entry_key, assistant_data_rpc_tx_broker_request_ack,
                                                  NULL, __FUNCTION__, __LINE__);
        assistant_data_rpc_tx_state_one_broker(broker);
        zpn_assistant_mtunnel_data_connected(broker);

        assistant_data_conn_register_rpc_calls(broker);

        zpn_fohh_tlv_set_conn(&(broker->broker_tlv_state), connection);

        zpn_mconn_fohh_tlv_add_drain_timer(connection, &broker->broker_tlv_state);

        zpn_mconn_fohh_tlv_add_monitor_timer(connection, &(broker->broker_tlv_state),
                                             assistant_data_connection_monitor_cb, FOHH_TIMER_CONN_MONITOR_S, 0);

        /* Send our IP (which is likely private) to our peer */
        zpn_send_zpn_tcp_info_report(connection, fohh_connection_incarnation(connection), connection, zvm_vm_type_to_str_concise(zvm_type_get()), g_asst_runtime_os);

        /* Send initial window update to our peer */
        zpn_send_zpn_fohh_window_update(connection, fohh_connection_incarnation(connection), 0,
                                        flow_control_enabled ? fohh_tlv_window_size : 0, 0);
        broker->broker_tlv_state.last_wnd_update_us = epoch_us();

        zpn_fohh_worker_assistant_connect_data_fohh(fohh_connection_get_thread_id(connection));

        /* Remove the broker fqdn if it is in double hop cache. Reset the backoff to default */
        if(za_lookup_user_broker_name_in_cache(connection->domain_name) != NULL) {
            za_remove_user_broker_name_from_cache(connection->domain_name);
            fohh_set_max_backoff(connection, FOHH_DEFAULT_BACKOFF_MAX_S);
            ASSISTANT_DEBUG_TWO_HOP("TWO_HOP ASST assistant_data_connection_cb: Removed from double hop cache and restored the backoff for broker FQDN: %s", connection->domain_name);
        }
    } else {
        int disconnected = 0;
        if ((fohh_connection_disconnected == fstate) && (fohh_connection_connected == broker->fohh_conn_state)) {
            /* to control the logging noise, we are filtering out retry states */
            ASSISTANT_LOG(AL_NOTICE, "Data channel to broker(%lld:%lld) closed: %s %s",
                          (long long)broker->broker_id, (long long)broker->label, fohh_description(connection), fohh_close_reason(connection));
            disconnected = 1;
        }

        /* Get the tx_data and rx_data */
        __sync_fetch_and_add_8(&stats.to_broker_data_past_fohh, broker->broker_tlv_state.tx_data);
        __sync_fetch_and_add_8(&stats.from_broker_data_past_fohh, broker->broker_tlv_state.rx_data);
        /* Change the back off time from default, if broker fqdn is in double hop cache */
        if(assistant_features_is_databroker_resilience_enabled(global_assistant.gid, &global_assistant.grp_gids[0], global_assistant.grp_gids_len)) {
            if(za_lookup_user_broker_name_in_cache(connection->domain_name) != NULL) {
                if(connection->backoff_max_s != ZPN_DOUBLE_HOP_DATA_BROKER_CONN_MAX_BACKOFF_S) {
                    fohh_set_max_backoff(connection, ZPN_DOUBLE_HOP_DATA_BROKER_CONN_MAX_BACKOFF_S);
                    ASSISTANT_DEBUG_TWO_HOP("TWO_HOP ASST assistant_data_connection_cb: Changed the backoff time to: %d sec for broker FQDN: %s", ZPN_DOUBLE_HOP_DATA_BROKER_CONN_MAX_BACKOFF_S, connection->domain_name);
                }
            }
        }
        zpn_assistant_mtunnel_data_disconnected(broker);

        /* NOTE: do NOT free broker- it gets re-used, as this is a client FOHH conenction! */
        /* But we do need to clear out the fohh_tlv state */
        zpn_fohh_tlv_clear_state(&(broker->broker_tlv_state));

        zpn_fohh_worker_assistant_disconnect_data_fohh(fohh_connection_get_thread_id(connection), fohh_close_reason(connection));
        /*
         * Note that there is a difference between broker & pbroker in terms of retaining the fconn related to data
         * connection.
         * 1. in broker case(connection is based on FQDN), its ok to let the fconn linger around as the same f_conn can
         * be reused when it reconnects. In this case even if the IP changes DNS lookup makes it transparent to us.
         * 2. But in pbroker case(connection is based on IP address), when it reconnects the pbroker's IP could have
         * changed as we support multi homing in pbroker. So we don't want to keep any old states.
         */
        if ((disconnected && broker->is_pbroker) || (disconnected && broker->idle_force_disconnect) || broker->deletion_in_progress ||
            (disconnected && (broker->conn_type == ZPN_ASSISTANT_MTUNNEL_CONTROL_BROKER_PROXY_CONN))) {
            ASSISTANT_DEBUG_TWO_HOP("TWO_HOP ASST assistant_data_connection_cb: calling assistant_data_free broker conn_type: %d, for: %s", broker->conn_type, broker->broker_sni);
            assistant_data_free(broker, connection, NULL, NULL);
            goto broker_freed;
        }
    }
    broker->fohh_conn_state = fstate;
broker_freed:
verify_failed:
    /* When verification failed, act as if nothing happened for now, the next callback will set the right state */
    return res;
}


static int
assistant_data_unblock_cb(struct fohh_connection*      connection,
                          enum fohh_queue_element_type element_type,
                          void*                        cookie)
{
    struct zpn_assistant_broker_data *broker = cookie;

    (void)connection;
    (void)element_type;

    /*
     * comment below code for maintance proof.
     */
    //if (fohh_queue_element_type_data != element_type) {
    //    return ZPN_RESULT_NO_ERROR;
    //}

    zpn_fohh_tlv_unblock_cb(&(broker->broker_tlv_state));

    return FOHH_RESULT_NO_ERROR;
}


static int assistant_new_stream_cb(struct zrdt_conn *conn,
                                   struct zrdt_stream *stream,
                                   uint64_t stream_id,
                                   enum zrdt_stream_status status,
                                   void *cookie)

{
    struct zpn_assistant_broker_data *broker;
    struct zpn_zrdt_tlv *zrdt_tlv;
    int res = ZPN_RESULT_NO_ERROR;

    ASSISTANT_DEBUG_MTUNNEL("New Stream callback. stream_id = %ld, status = %s", (long) stream_id, zrdt_stream_status_string(status));

    broker = zrdt_conn_get_dynamic_cookie(conn);
    if (!broker) {
        ZPN_LOG(AL_ERROR, "No broker for stream?");
        return ZPN_RESULT_ERR;
    }

    zrdt_tlv = (struct zpn_zrdt_tlv *)assistant_data_get_tlv(broker);

    if (stream_id == 0) {
        ZPN_LOG(AL_WARNING, "Assistant initiate connection, so we should not receive new stream on stream_id 0");
    } else {
        struct zpn_mconn_zrdt_tlv *mconn_zrdt_tlv;
        struct zpn_mconn *mconn;
        struct zpn_assistant_mtunnel *mtunnel;

        /* This is regular data stream */

        mconn_zrdt_tlv = zpn_zrdt_tlv_get_local_owner(zrdt_tlv, stream_id);
        if (!mconn_zrdt_tlv) {
            ASSISTANT_DEBUG_MTUNNEL("No local owner for the stream_id = %ld", (long)stream_id);
            return ZPN_RESULT_ERR;
        }

        ASSISTANT_ASSERT_SOFT(!mconn_zrdt_tlv->stream, "Already has stream?");

        mconn = &(mconn_zrdt_tlv->mconn);
        if (!mconn->global_owner) {
            ASSISTANT_DEBUG_MTUNNEL("No global owner for the stream_id = %ld", (long)stream_id);
            return ZPN_RESULT_ERR;
        }

        mtunnel = mconn->global_owner;
        if (zpn_assistant_mtunnel_ip_proto(mtunnel) == IPPROTO_TCP) {
            res = zrdt_stream_set_type(stream, zrdt_stream_reliable_endpoint);
        } else {
            res = zrdt_stream_set_type(stream, zrdt_stream_unreliable_endpoint);

        }

        if (res) {
            ZPN_LOG(AL_ERROR, "Cannot set stream type");
            return ZPN_RESULT_ERR;
        }

        mconn_zrdt_tlv->stream = stream;
    }

    /* This is data stream */
    res = zrdt_stream_assign_callbacks(stream, zpn_zrdt_read_cb, zpn_zrdt_write_cb, zpn_zrdt_event_cb, zrdt_tlv);
    if (res) {
        ZPN_LOG(AL_ERROR, "Cannot set callbacks");
    }

    return res;
}

static void assistant_dtls_session_callback(struct zdtls_session *zd_sess,
                                            void *thread_cookie,
                                            enum zdtls_session_status status,
                                            const char *reason,
                                            void *cookie,
                                            int64_t int_cookie,
                                            void *tlv_cookie)
{
    struct zpn_assistant_broker_data *broker = cookie;
    struct fohh_thread *f_thread = (struct fohh_thread *)thread_cookie;
    enum zdtls_session_status current_status;
    int thread_id = fohh_thread_get_id(f_thread);
    struct zrdt_conn *z_conn;
    int res;

    if (!broker) {
        ASSISTANT_ASSERT_SOFT(0, "broker cookie is deleted, we should not reach there");
        return;
    }

    ASSISTANT_DEBUG_DATA("assistant_dtls_session_callback(), status = %s", zdtls_session_status(status));
    current_status = broker->zdtls_sess_status;
    broker->zdtls_sess_status = status;

    if (status == zdtls_session_open) {
        // dtls session has been established. we send only zrdt packets over this dtls session, for which
        // the mtu should be adjusted to allow for the zrdt headers to be added. Get the mtu of tx interface
        // for the remote IP of this zrdt connection. We expect the user to adjust the interface MTU in cases
        // where an additional encap like IPSec or GRE gets applied in a system downstream from the connector.
        int mtu = assistant_to_broker_dtls_mtu(global_assistant.gid,
                                                &global_assistant.grp_gids[0],
                                                global_assistant.grp_gids_len);

        ASSISTANT_DEBUG_DATA("MTU for zrdt connection over dtls session %s: %d", zdtls_description(zd_sess), mtu);

        res = zrdt_conn_create(&z_conn,
                               mtu,
                               (zrdt_datagram_tx_f *) zdtls_session_transmit,
                               zd_sess,
                               int_cookie,
                               assistant_new_stream_cb,
                               NULL,
                               zdtls_session_get_event_base(zd_sess));
        if (res) {
            ASSISTANT_LOG(AL_INFO, "Could not create new connection for %s(%"PRId64"): %s",
                          broker->is_pbroker ? "Private Broker" : "Broker", broker->broker_id, zdtls_description(zd_sess));
            res = zdtls_session_close(zd_sess);
            if (res) {
                ASSISTANT_LOG(AL_INFO, "Could not handle session_close on session failure for %s(%"PRId64"): %s",
                              broker->is_pbroker ? "Private Broker" : "Broker", broker->broker_id, zdtls_description(zd_sess));
                return;
            }
        } else {

            ASSISTANT_DEBUG_DATA("Created zrdt_conn for %s", zdtls_description(zd_sess));

            if ((broker->is_pbroker) &&
                (ZPN_RESULT_NO_ERROR != assistant_pbroker_verify_connection_and_act_zrdt(broker->broker_id, z_conn))) {
                broker->fohh_verification_failed = 1;
                return;
            }

            ASSISTANT_LOG(AL_NOTICE, "Data channel (DTLS) successfully connected to %s(%"PRId64"): %s",
                          broker->is_pbroker ? "Private Broker" : "Broker", broker->broker_id, zdtls_description(zd_sess));

            /* broker is not reused, so set tlv_incarnation to 0 */
            zpn_zrdt_tlv_init(&(broker->broker_zrdt_tlv_state), z_conn, 0);

            zdtls_session_set_rx_callback(zd_sess, zpn_zrdt_zdtls_data_callback, z_conn, 0);

            zrdt_conn_set_dynamic_cookie(z_conn, broker);

            broker->broker_zrdt_tlv_state.fohh_thread_id = thread_id;
            zrdt_conn_set_thread_id(z_conn, thread_id);

            res = zrdt_stream_create(z_conn,
                                     &(broker->broker_zrdt_tlv_state.msg_stream),
                                     -1,
                                     zrdt_stream_reliable_endpoint,
                                     zpn_zrdt_read_cb,
                                     zpn_zrdt_write_cb,
                                     zpn_zrdt_event_cb,
                                     &(broker->broker_zrdt_tlv_state));
            if (res) {
                ASSISTANT_DEBUG_DATA("Created stream 0 for %s", zdtls_description(zrdt_conn_get_datagram_tx_cookie(z_conn)));
                return;
            }

            zrdt_conn_set_status(z_conn, zrdt_conn_connected);
            zrdt_conn_set_stream_validity_check_callback(z_conn, zpn_assistant_valid_stream_cb);

            /* Initialize Argo state for msg_stream */
            res = zpn_zrdt_init_argo_state(broker->broker_zrdt_tlv_state.msg_stream, argo_serialize_json_no_newline, 1);
            if (res) {
                ZPN_LOG(AL_NOTICE, "Could not initialize argo state for msg_stream");
                return;
            }

            zpn_assistant_mtunnel_data_connected(broker);
            assistant_data_conn_register_rpc_calls(broker);

            zpn_mconn_zrdt_tlv_add_monitor_timer(z_conn, &(broker->broker_zrdt_tlv_state),
                                                 assistant_data_zrdt_connection_monitor_cb, FOHH_TIMER_CONN_MONITOR_S, 0);
            zpn_send_zpn_zrdt_info_report(&(broker->broker_zrdt_tlv_state.tlv), zrdt_conn_incarnation(z_conn), z_conn);
            zpn_fohh_worker_assistant_connect_data_zrdt(zrdt_conn_get_thread_id(z_conn));
        }

    } else if (status == zdtls_session_closing) {
        /* Closing */

    } else {
        int disconnected = 0;

        if (((current_status == zdtls_session_open) || (current_status == zdtls_session_closing)) &&
                (status == zdtls_session_closed)) {
            struct zrdt_conn *zrdt_conn;

            zrdt_conn = zpn_mconn_zrdt_tlv_get_conn(&(broker->broker_zrdt_tlv_state));
            if (zrdt_conn) {
                ASSISTANT_LOG(AL_NOTICE, "%s: Data channel (DTLS) to broker(%lld:%lld) closed: %s",
                              zdtls_description(zrdt_conn_get_datagram_tx_cookie(zrdt_conn)), (long long)broker->broker_id,
                              (long long)broker->label, reason ? reason : "UNKNOWN");
                zpn_fohh_worker_assistant_disconnect_data_zrdt(zrdt_conn_get_thread_id(zrdt_conn));
                /* Get the tx_data and rx_data */
                __sync_fetch_and_add_8(&stats.to_broker_data_past_dtls, broker->broker_zrdt_tlv_state.tx_data);
                __sync_fetch_and_add_8(&stats.from_broker_data_past_dtls, broker->broker_zrdt_tlv_state.rx_data);
                zpn_assistant_mtunnel_data_disconnected(broker);
                zpn_zrdt_tlv_destroy(&(broker->broker_zrdt_tlv_state), reason);
            }
            disconnected = 1;
        } else if ((broker->zdtls_sess_status == zdtls_session_closed) && (status == zdtls_session_closed)) {
            ASSISTANT_DEBUG_DATA("Data channel to broker(%"PRId64") cannot be established", broker->broker_id);
        }

        /* We delete the broker if it is a private broker */
        if ((disconnected && broker->is_pbroker) || broker->deletion_in_progress) {
            struct ztlv_info *info = tlv_cookie;
            struct zrdt_conn *zrdt_conn;

            zrdt_conn = zpn_mconn_zrdt_tlv_get_conn(&(broker->broker_zrdt_tlv_state));

            assistant_data_free(broker, NULL, zrdt_conn, info);

            /* Don't do auto reconnect if we are deleting broker */
            info->auto_reconnect = 0;
        }
    }
}

static int assistant_broker_data_zrdt_conn_create(struct zpn_assistant_broker_data *broker, struct argo_inet *remote_ip, int is_pbroker, char *alt_cloud, int alt_cloud_enabled)
{
    struct sockaddr_storage addr;
    socklen_t addr_len;
    int res = ZPN_RESULT_NO_ERROR;
    char sni_buffer[300];
    char *sni_str;
    char buf[128];
    int thread_id = fohh_worker_pool_get_thread_id(FOHH_WORKER_ZPN_DTLS);

    argo_inet_to_sockaddr(remote_ip, (struct sockaddr *)&addr, &addr_len, htons(ZPN_CONNECTOR_BROKER_DATA_PORT_UDP));

    fohh_sockaddr_storage_to_str(&(addr), buf, 128);

    sni_str = assistant_data_get_sni(broker->broker_id, (const char*)broker->broker_name, strlen(broker->broker_name), sni_buffer, sizeof(sni_buffer), is_pbroker, alt_cloud, alt_cloud_enabled);

    ASSISTANT_DEBUG_DATA("ZRDT Data Remote Address = %s, sni = %s\n", buf, sni_str);

    res = ztlv_dtls_connect(fohh_get_thread_event_base(thread_id),
                            &addr,
                            sni_str,
                            is_pbroker ? global_assistant.assistant_to_private_cloud_ctx_dtls : global_assistant.assistant_to_public_cloud_ctx_dtls,
                            assistant_dtls_session_callback,
                            broker,
                            1,     // Used to indicate it is a client.
                            fohh_get_thread(thread_id),
                            is_pbroker ? 0 : 1);

    /* Add periodic logging timer */
    if (!broker->periodic_logging_timer) {
        broker->periodic_logging_timer = event_new(fohh_get_thread_event_base(thread_id), -1, EV_PERSIST, assistant_data_log, broker);
        if (broker->periodic_logging_timer) {
            struct timeval log_time_interval;

            log_time_interval.tv_sec = ASSISTANT_DATA_CONNECTION_LOG_FREQUENCY_US / 1000000;
            log_time_interval.tv_usec = ASSISTANT_DATA_CONNECTION_LOG_FREQUENCY_US % 1000000;
            if (event_add(broker->periodic_logging_timer, &log_time_interval)) {
                ASSISTANT_LOG(AL_CRITICAL, "Could not add logging event for data broker %ld. Leaked f_conn", (long) broker->broker_id);
            }
        } else {
            ASSISTANT_LOG(AL_CRITICAL, "Could not create logging event for data broker %ld. Leaked f_conn", (long) broker->broker_id);
        }
    }

    return res;
}

static int assistant_broker_data_resolver_callback(const char *domain_name,
                                                   struct argo_inet *ips,
                                                   size_t ips_count,
                                                   void *callback_void,
                                                   int64_t callback_int)
{
    struct zpn_assistant_broker_data *broker = callback_void;
    int is_pbroker = callback_int;
    char pbroker_remote_ip_addr[ASSISTANT_UTIL_DOMAIN_MAX_LEN] = {0};
    char alt_cloud[256] = {0};
    int alt_cloud_enabled = 0;

    ASSISTANT_DEBUG_DATA("zpn_assistant_broker_data_resolver_callback(), domain_name = %s", domain_name);

    pthread_mutex_lock(&(broker->lock));

    if (broker->resolver_called_back) {

        /* Resolver has called back already, zrdt conn already created */

        pthread_mutex_unlock(&(broker->lock));
        return ZPN_RESULT_NO_ERROR;
    }
    broker->resolver_called_back = 1;
    pthread_mutex_unlock(&(broker->lock));

    if (is_pbroker) {
        if (assistant_pbroker_control_get_connected_active_ip_and_alt_cloud(broker->broker_id, pbroker_remote_ip_addr, sizeof(pbroker_remote_ip_addr), alt_cloud, sizeof(alt_cloud), &alt_cloud_enabled)) {
            return ZPN_RESULT_ERR;
        }
    }

    return assistant_broker_data_zrdt_conn_create(broker, ips, is_pbroker, alt_cloud, alt_cloud_enabled);
}

static void assistant_data_connection_init(struct zpn_assistant_broker_data*    broker,
                                           int64_t                              broker_id,
                                           int64_t                              end_broker_id,
                                           const char*                          broker_name,
                                           char*                                broker_sni,
                                           int                                  is_pbroker,
                                           enum zpn_tlv_type                    tlv_type)
{
    broker->mtunnels_by_tag = argo_hash_alloc(7, 1);
    broker->lock = (pthread_mutex_t) PTHREAD_MUTEX_INITIALIZER;
    broker->broker_id = broker_id;
    broker->broker_name = ZPN_STRDUP(broker_name, strlen(broker_name));
    broker->broker_sni = ZPN_STRDUP(broker_sni, strlen(broker_sni));
    broker->tlv_type = tlv_type;
    broker->is_pbroker = is_pbroker;
    broker->fohh_verification_failed = 0;
    broker->hash_entry_key = ASST_MALLOC(sizeof(*broker->hash_entry_key));
    broker->hash_entry_key->broker_id = broker_id;
    broker->hash_entry_key->end_broker_id = end_broker_id;
    broker->hash_entry_key->tlv_type = tlv_type;
    TAILQ_INIT(&(broker->mtunnel_list));
}

static void
assistant_remove_data_broker(struct zpn_assistant_broker_data *broker)
{
    pthread_mutex_lock(&(state.brokers_lock));
    pthread_mutex_lock(&(broker->lock));

    if (0 == broker->deletion_in_progress) {
        zhash_table_remove(state.brokers, broker->hash_entry_key, sizeof(*(broker->hash_entry_key)), broker);
        broker->deletion_in_progress = 1;
    } else {
        ASSISTANT_LOG(AL_NOTICE, "deletion already in progress for broker:%p", broker);
    }

    pthread_mutex_unlock(&(broker->lock));
    pthread_mutex_unlock(&(state.brokers_lock));
}

struct zpn_assistant_broker_data *
assistant_data_conn_lookup(int64_t broker_id, int64_t g_bfw, int dtls_enabled, int conn_type)
{
    struct zpn_assistant_broker_data*   broker = NULL;
    struct zpn_assistant_broker_data_id broker_conn_id;

    switch (conn_type) {
        case ZPN_ASSISTANT_MTUNNEL_CONTROL_BROKER_PROXY_CONN:
            broker_conn_id.broker_id = g_bfw;
            broker_conn_id.end_broker_id = broker_id;
            break;
        case ZPN_ASSISTANT_MTUNNEL_DATA_BROKER_CONN:
            broker_conn_id.broker_id = broker_id;
            broker_conn_id.end_broker_id = broker_id;
            break;
        default:
            ASSISTANT_LOG(AL_NOTICE, "assistant_data_conn_lookup: Unknown connection type");
            return NULL;
            break;
    }

    if (dtls_enabled) {
        broker_conn_id.tlv_type = zpn_zrdt_tlv;
    } else {
        broker_conn_id.tlv_type = zpn_fohh_tlv;
    }

    pthread_mutex_lock(&(state.brokers_lock));
    broker = zhash_table_lookup(state.brokers, &broker_conn_id, sizeof(broker_conn_id), NULL);
    pthread_mutex_unlock(&(state.brokers_lock));

    return broker;
}

struct zpn_assistant_broker_data *
assistant_data_lookup_or_create(int64_t     broker_id,
                                const char* broker_name,
                                int         is_pbroker,
                                int         dtls_enabled,
                                int         allow_all_xport,
                                char        *alt_cloud,
                                int         alt_cloud_enabled,
                                int64_t     g_bfw,
                                const char *bfw_name,
                                int         conn_type)
{
    struct zpn_assistant_broker_data*   broker_zrdt = NULL;
    struct zpn_assistant_broker_data*   broker = NULL;
    char                                stats_name[256];
    int                                 res;
    struct fohh_connection*             f_conn = NULL;
    char                                sni_buffer[300];
    char*                               sni_str;
    struct zpn_assistant_broker_data_id id;
    struct timeval                      log_time_interval;

    const char *conn_to_broker;

    switch (conn_type) {
        case ZPN_ASSISTANT_MTUNNEL_CONTROL_BROKER_PROXY_CONN: /* sni has g_brk, connection to g_bfw */
            sni_str = assistant_data_get_sni(broker_id, broker_name, strlen(broker_name), sni_buffer, sizeof(sni_buffer), is_pbroker, alt_cloud, alt_cloud_enabled);
            snprintf(stats_name, sizeof(stats_name), "brk-data_%s", broker_name);
            id.broker_id = g_bfw;
            id.end_broker_id = broker_id;
            conn_to_broker = bfw_name;
            break;
        case ZPN_ASSISTANT_MTUNNEL_DATA_BROKER_CONN:          /* sni has g_brk, connection to g_brk */
            sni_str = assistant_data_get_sni(broker_id, broker_name, strlen(broker_name), sni_buffer, sizeof(sni_buffer), is_pbroker, alt_cloud, alt_cloud_enabled);
            snprintf(stats_name, sizeof(stats_name), "brk-data_%s", broker_name);
            id.broker_id = broker_id;
            id.end_broker_id = broker_id;
            conn_to_broker = broker_name;
            break;
        default:
            ASSISTANT_LOG(AL_NOTICE, "assistant_data_lookup_or_create: Unknown connection type");
            return NULL;
            break;
    }

    if (dtls_enabled) {
        struct argo_inet remote_ip;

        id.tlv_type = zpn_zrdt_tlv;

        pthread_mutex_lock(&(state.brokers_lock));
        broker_zrdt = zhash_table_lookup(state.brokers, &id, sizeof(id), NULL);
        pthread_mutex_unlock(&(state.brokers_lock));

        if (broker_zrdt && strcmp(broker_zrdt->broker_name, conn_to_broker) != 0) {
            //Broker FQDN changed for the specified broker id.
            // update the broker name for zrdt
            ASSISTANT_LOG(AL_NOTICE, "broker name changed from %s to %s for broker id:%"PRId64" closing DTLS conn", broker_zrdt->broker_name, conn_to_broker, id.broker_id);
            fohh_resolve_remove(broker_zrdt->broker_name);
            assistant_remove_data_broker(broker_zrdt);
            zdtls_client_disconnect(zrdt_conn_get_datagram_tx_cookie(broker_zrdt->broker_zrdt_tlv_state.tlv.conn.z_conn));
            broker_zrdt = NULL;
        }

        if (broker_zrdt && broker_zrdt->broker_sni && global_assistant.alt_cloud_enabled && (strcmp(broker_zrdt->broker_sni, sni_str) != 0)) {
            /* Broker SNI changed for the specified broker id.
             * When feature is flipped to enabled on connector, but broker is always enabled, we want the connector
             * to be able to recover itself and reconnect to broker via alt cloud sni
             */
            ASSISTANT_LOG(AL_NOTICE, "broker sni changed from %s to %s for broker id:%"PRId64" closing DTLS conn", broker_zrdt->broker_sni, sni_str, id.broker_id);
            fohh_resolve_remove(broker_zrdt->broker_name);
            assistant_remove_data_broker(broker_zrdt);
            zdtls_client_disconnect(zrdt_conn_get_datagram_tx_cookie(broker_zrdt->broker_zrdt_tlv_state.tlv.conn.z_conn));
            broker_zrdt = NULL;
        }

        if (!broker_zrdt) {

            /* Need to allocate a new data broker */
            broker_zrdt = ASST_CALLOC(sizeof(*broker_zrdt));
            if (broker_zrdt) {
                assistant_data_connection_init(broker_zrdt, id.broker_id, id.end_broker_id, conn_to_broker, sni_str, is_pbroker, zpn_zrdt_tlv);

                pthread_mutex_lock(&(state.brokers_lock));
                res = zhash_table_store(state.brokers, &id, sizeof(id), 0, broker_zrdt);
                pthread_mutex_unlock(&(state.brokers_lock));
                if (res == ZPN_RESULT_NO_ERROR) {
                    broker_zrdt->zdtls_sess_status = zdtls_session_closed;

                    if (argo_string_to_inet(conn_to_broker, &remote_ip) == ARGO_RESULT_NO_ERROR) {
                        struct sockaddr_storage addr;
                        socklen_t addr_len;
                        char buf[128];

                        argo_inet_to_sockaddr(&remote_ip, (struct sockaddr *)&addr, &addr_len, htons(ZPN_CONNECTOR_BROKER_DATA_PORT_UDP));
                        fohh_sockaddr_storage_to_str(&(addr), buf, 128);
                        ASSISTANT_DEBUG_DATA("Create new DTLS data connection to %s broker %s", is_pbroker ? "private" : "public", buf);
                        assistant_broker_data_zrdt_conn_create(broker_zrdt, &remote_ip, is_pbroker, alt_cloud, alt_cloud_enabled);
                    } else {
                        ASSISTANT_DEBUG_DATA("Create new DTLS data connection to %s broker %s", is_pbroker ? "private" : "public", conn_to_broker);
                        fohh_resolve_start(conn_to_broker, FOHH_MIN_DNS_FREQUENCY, NULL, NULL, 0);
                        fohh_resolve_start(conn_to_broker, FOHH_MIN_DNS_FREQUENCY, assistant_broker_data_resolver_callback, broker_zrdt, is_pbroker);
                    }
                } else {
                    ASSISTANT_LOG(AL_CRITICAL, "Could not store back into hash table broker %ld. Leaked f_conn", (long) id.broker_id);
                    assistant_data_broker_destroy(broker_zrdt);
                }
            }

            /* We created DTLS connection, but it is not ready yet, so let's not use it for this mtunnel */
        } else {
            if (broker_zrdt->zdtls_sess_status == zdtls_session_open) {
                if (allow_all_xport) {
                    ASSISTANT_DEBUG_DATA("ZRDT data connection is ready and mtunnel is allowed for all xport, use it");
                    return broker_zrdt;
                } else {
                    ASSISTANT_DEBUG_DATA("ZRDT data connection is ready but mtunnel is not allowed for all xport");
                }
            } else {
                ASSISTANT_DEBUG_DATA("ZDLS session is not open, don't use it");
            }
        }
    } else {
        ASSISTANT_DEBUG_DATA("Zrdt is not enabled");
    }

    /* DTLS/ZRDT connection is not ready, so try the FOHH connection */
    if (!allow_all_xport || dtls_enabled) {
        ASSISTANT_DEBUG_DATA("Multi xport is not eanbled or ZRDT data connection is not ready, try FOHH connection");
    }

    id.tlv_type = zpn_fohh_tlv;

    ASSISTANT_DEBUG_TWO_HOP("TWO_HOP ASST assistant_data_lookup_or_create: Set id.tlv_type to zpn_fohh_tlv for conn_type: %d, sni_str: %s", conn_type, sni_str);
    pthread_mutex_lock(&(state.brokers_lock));
    broker = zhash_table_lookup(state.brokers, &id, sizeof(id), NULL);
    pthread_mutex_unlock(&(state.brokers_lock));

    if (broker && strcmp(broker->broker_name, conn_to_broker) != 0) {
        /* Broker FQDN changed for the specified broker id.
         * cleanup the broker data connection so that a new data
         * connection gets established
         */
        ASSISTANT_LOG(AL_NOTICE, "broker name changed from %s to %s for broker id:%"PRId64" freeing FOHH conn", broker->broker_name, conn_to_broker, id.broker_id);
        fohh_resolve_remove(broker->broker_name);
        assistant_data_free(broker, NULL, NULL, NULL);
        broker = NULL;
    }

    if (broker && broker->broker_sni && global_assistant.alt_cloud_enabled && (strcmp(broker->broker_sni, sni_str) != 0)) {
        /* Broker SNI changed for the specified broker id.
         * When feature is flipped to enabled on connector, but broker is always enabled, we want the connector
         * to be able to recover itself and reconnect to broker via alt cloud sni
         */
        ASSISTANT_LOG(AL_NOTICE, "broker sni changed from %s to %s for broker id:%"PRId64" freeing FOHH conn", broker->broker_sni, sni_str, id.broker_id);
        fohh_resolve_remove(broker->broker_name);
        assistant_data_free(broker, NULL, NULL, NULL);
        broker = NULL;
    }

    if (!broker) {
        /* Need to allocate a new data broker */
        broker = ASST_CALLOC(sizeof(*broker));
        if (!broker) return NULL;

        assistant_data_connection_init(broker, id.broker_id, id.end_broker_id, conn_to_broker, sni_str, is_pbroker, zpn_fohh_tlv);

        ASSISTANT_DEBUG_TWO_HOP("TWO_HOP ASST assistant_data_lookup_or_create: calling fohh_client_create to connect to: %s with sni_str: %s", conn_to_broker, sni_str);
        f_conn = fohh_client_create(FOHH_WORKER_ZPN_ADATA,
                                stats_name,
                                argo_serialize_binary,
                                fohh_connection_style_argo_tlv,
                                0,
                                broker,
                                assistant_data_connection_cb,
                                zpn_fohh_tlv_data_callback,
                                assistant_data_unblock_cb,
                                NULL,
                                conn_to_broker,
                                sni_str,
                                NULL,
                                htons(ZPN_ASSISTANT_BROKER_PORT),
                                is_pbroker ? global_assistant.assistant_to_private_cloud_ctx : global_assistant.assistant_to_public_cloud_ctx,
                                1,
                                ZPN_ASSISTANT_BROKER_RX_TIMEOUT_S);
        if (!f_conn) {
            ASSISTANT_LOG(AL_WARNING, "Could not create client f_conn for broker %ld", (long) id.broker_id);
            assistant_data_broker_destroy(broker);
            return NULL;
        }
        fohh_set_sticky(f_conn, 1);

        /*
         * Set status interval address, which has the interval in seconds for sending fohh_status_request messages by connected broker.
         * Default status interval is 1 second for sending fohh_status_request messages.
         */
        fohh_set_status_interval(f_conn, &(global_assistant.data_status_interval));

        broker->fohh_conn_state = f_conn->state;
        fohh_suppress_connection_event_logs(f_conn);

        /* broker is not reused, so set tlv_incarnation to 0 */
        res = zpn_fohh_tlv_init(&(broker->broker_tlv_state), f_conn, 0);
        if (res) {
            ASSISTANT_LOG(AL_CRITICAL, "Could not init tlv: %s", zpn_result_string(res));
            fohh_connection_release(f_conn);
            assistant_data_broker_destroy(broker);
            return NULL;
        }

        log_time_interval.tv_sec = ASSISTANT_DATA_CONNECTION_LOG_FREQUENCY_US / 1000000;
        log_time_interval.tv_usec = ASSISTANT_DATA_CONNECTION_LOG_FREQUENCY_US % 1000000;
        broker->periodic_logging_timer = event_new(fohh_connection_event_base(f_conn), -1, EV_PERSIST, assistant_data_log,
                                                   broker);

        if (NULL == broker->periodic_logging_timer) {
            ASSISTANT_LOG(AL_CRITICAL, "Could not create logging event for data broker %ld. Leaked f_conn", (long) id.broker_id);
            fohh_connection_release(f_conn);
            assistant_data_broker_destroy(broker);
            return NULL;
        }

        if (event_add(broker->periodic_logging_timer, &log_time_interval)) {
            ASSISTANT_LOG(AL_CRITICAL, "Could not add logging event for data broker %ld. Leaked f_conn", (long) id.broker_id);
            fohh_connection_release(f_conn);
            assistant_data_broker_destroy(broker);
            return NULL;
        }

        pthread_mutex_lock(&(state.brokers_lock));
        res = zhash_table_store(state.brokers, &id, sizeof(id), 0, broker);
        pthread_mutex_unlock(&(state.brokers_lock));
        if (res) {
            ASSISTANT_LOG(AL_CRITICAL, "Could not store back into hash table broker %ld. Leaked f_conn", (long) id.broker_id);
            fohh_connection_release(f_conn);
            assistant_data_broker_destroy(broker);
            return NULL;
        }

        broker->conn_type = conn_type;
        if(conn_type == ZPN_ASSISTANT_MTUNNEL_CONTROL_BROKER_PROXY_CONN) {
            ASSISTANT_DEBUG_TWO_HOP("TWO_HOP ASST assistant_data_lookup_or_create: connecton type is broker proxy. incremented num_doublehop_conn. sni: %s",sni_str);
            __sync_fetch_and_add_8(&global_assistant.num_doublehop_conn, 1);
        }

        fohh_enable_direct_write(f_conn);
        fohh_history_enable(f_conn);
    }
    ASSISTANT_DEBUG_DATA("Created data connection to broker(%s) sni(%s)", conn_to_broker, sni_str);

    /* Turn off autotune between broker and connector */
    // fohh_set_autotune(f_conn, 1, 1.0, 0.9, 5, 32*1024);
    return broker;
}

/*
 * This is called twice for a broker. Once when the fohh is disconnected and again when it is deleted.
 * When it is disconnected(i.e on first call), we do all the cleanup but not free broker object as fohh is still
 * holding the pointer as cookie. When we hit here the second time, we are ready to get rid of the object completely.
 *
 * Note that the first thing that we do is to remove it from the state.brokers hash table. This way, this broker
 * which is in deletion phase will not be searchable to others.
 */
static void
assistant_data_free(struct zpn_assistant_broker_data* broker,
                    struct fohh_connection* f_conn,
                    struct zrdt_conn* z_conn,
                    struct ztlv_info *info)
{
    pthread_mutex_lock(&(state.brokers_lock));
    pthread_mutex_lock(&(broker->lock));

    if (0 == broker->deletion_in_progress) {
        zhash_table_remove(state.brokers, broker->hash_entry_key, sizeof(*(broker->hash_entry_key)), broker);
        broker->deletion_in_progress = 1;
        if(broker->conn_type == ZPN_ASSISTANT_MTUNNEL_CONTROL_BROKER_PROXY_CONN) {
            __sync_fetch_and_add_8(&global_assistant.num_doublehop_proxy_conn_free, 1);
        }
    }

    if (broker->tlv_type == zpn_fohh_tlv) {
        if (zpn_mconn_fohh_tlv_get_conn(&(broker->broker_tlv_state))) {
            struct fohh_connection *f_conn = zpn_mconn_fohh_tlv_get_conn(&(broker->broker_tlv_state));
            fohh_connection_delete_async(f_conn,
                                         f_conn->incarnation,
                                         FOHH_CLOSE_REASON_AST_PBRK_DATA_DOWN);
            zpn_fohh_tlv_destroy(&broker->broker_tlv_state, MT_CLOSED_TLS_CONN_GONE_AST_CLOSED);
            argo_hash_free(broker->mtunnels_by_tag);
            broker->mtunnels_by_tag = NULL;
            event_free(broker->periodic_logging_timer);
            broker->periodic_logging_timer = NULL;
            goto done;
        }
    } else {
        if (zpn_mconn_zrdt_tlv_get_conn(&(broker->broker_zrdt_tlv_state))) {
            zpn_zrdt_tlv_destroy(&(broker->broker_zrdt_tlv_state), MT_CLOSED_DTLS_CONN_GONE_AST_CLOSED);
        }

        argo_hash_free(broker->mtunnels_by_tag);
        event_free(broker->periodic_logging_timer);
        broker->periodic_logging_timer = NULL;
    }
    assistant_data_broker_destroy(broker);
    broker = NULL;

    if (f_conn) {
        fohh_connection_set_dynamic_cookie(f_conn, NULL);
        fohh_connection_set_cookie(f_conn, NULL);
    }

    if (z_conn) zrdt_conn_set_dynamic_cookie(z_conn, NULL);
    if (info) zpn_tlv_set_ztlv_zdtls_connect_info_callback_cookie(&(info->dtls_conn_info), NULL);

done:
    if (broker) pthread_mutex_unlock(&(broker->lock));
    pthread_mutex_unlock(&(state.brokers_lock));
}


/*
 * curl -s '127.0.0.1:9000/assistant/data/ut/setup_connection?broker_gid=217246660303023534&hostname=************&is_pbroker=1'
 */
static int
assistant_data_ut_setup_connection(struct zpath_debug_state*  request_state,
                                   const char **              query_values,
                                   int                        query_value_count,
                                   void*                      cookie)
{
    struct zpn_assistant_broker_data* broker;
    int64_t                           broker_gid;
    const char*                       hostname;
    int                               is_pbroker;

    (void)query_value_count;
    (void)cookie;
    if (!query_values[0] || !query_values[1] || !query_values[2]) {
        ZDP("Missing argument: broker_gid and/or hostname and/or is_pbroker\n");
        goto done;
    }
    broker_gid = strtoll(query_values[0], NULL, 10);
    hostname = query_values[1];
    is_pbroker= (int)strtol(query_values[2], NULL, 10);

    broker = assistant_data_lookup_or_create(broker_gid, hostname, is_pbroker, 0, 0, NULL, 0, 0, NULL, ZPN_ASSISTANT_MTUNNEL_DATA_BROKER_CONN);
    if (!broker) {
        ZDP("Could not create broker");
        goto done;
    }

done:
    return ZPATH_RESULT_NO_ERROR;
}


static int
assistant_data_dump_connections_internal(void*  cookie,
                                         void*  object,
                                         void*  key,
                                         size_t key_len)
{
    struct zpn_assistant_broker_data* broker = (struct zpn_assistant_broker_data *)object;
    struct zpath_debug_state*         request_state = (struct zpath_debug_state *)cookie;
    char                              uptime_str[FOHH_MAX_UPTIME_BUF_LEN];
    char                              max_rtt_us_str[FOHH_RTT_SINCE_LAST_QUERY_MSG_STR_MAX];

    if (broker->tlv_type == zpn_fohh_tlv) {
        fohh_connection_max_rtt_to_string(broker->broker_tlv_state.tlv.conn.f_conn, max_rtt_us_str, sizeof(max_rtt_us_str));
    } else {
        struct zpn_zrdt_tlv  *zrdt_tlv;
        struct zrdt_conn_stats conn_stats;

        zrdt_tlv = &(broker->broker_zrdt_tlv_state);
        zrdt_get_conn_stats(zpn_mconn_zrdt_tlv_get_conn(zrdt_tlv), &conn_stats);

        snprintf(max_rtt_us_str, sizeof(max_rtt_us_str), ", RTT (app): %"PRIu64" us", conn_stats.max_rtt_us);
    }

    ZDP("broker GID         : %"PRId64"\n", broker->broker_id);
    ZDP("Is Pbroker         : %d\n", broker->is_pbroker);

    if (broker->tlv_type == zpn_fohh_tlv) {
        struct fohh_connection *f_conn = zpn_mconn_fohh_tlv_get_conn(&(broker->broker_tlv_state));

        ZDP("Fohh verify failed : %d\n", broker->fohh_verification_failed);
        ZDP("fohh               : %s ", fohh_description(f_conn));
        ZDP("state(%s %s) ", fohh_connection_state_strings[broker->fohh_conn_state],
            (fohh_get_state(f_conn) == fohh_connection_connected) ? "" : fohh_close_reason(f_conn));
        ZDP("uptime(%s%s)\n", zpn_tlv_get_uptime_str(&(broker->broker_tlv_state.tlv), uptime_str, sizeof(uptime_str)),
            (fohh_get_state(f_conn) == fohh_connection_connected)? max_rtt_us_str: "");
    } else {
        struct zrdt_conn *z_conn = zpn_mconn_zrdt_tlv_get_conn(&(broker->broker_zrdt_tlv_state));
        enum zrdt_conn_status status = z_conn ? zrdt_conn_get_status(z_conn) : zrdt_conn_disconnected;

        ZDP("zrdt               : %s ", zpn_tlv_description(&(broker->broker_zrdt_tlv_state.tlv)));
        ZDP("state(%s ) ", zrdt_conn_status_string(status));
        ZDP("uptime(%s%s)\n", zpn_tlv_get_uptime_str(&(broker->broker_zrdt_tlv_state.tlv), uptime_str, sizeof(uptime_str)),
            max_rtt_us_str);
    }

    return ZPATH_RESULT_NO_ERROR;
}

/*
 * curl -s '127.0.0.1:9000/assistant/data/dump_connections
 */
static int
assistant_data_dump_connections(struct zpath_debug_state*  request_state,
                                const char **              query_values,
                                int                        query_value_count,
                                void*                      cookie)
{
    int64_t walk_key = 0;

    (void)query_values;
    (void)query_value_count;
    (void)cookie;
    if (!state.brokers) {
        goto done;
    }

    pthread_mutex_lock(&(state.brokers_lock));
    zhash_table_walk(state.brokers, &walk_key, assistant_data_dump_connections_internal, (void *)request_state);
    pthread_mutex_unlock(&(state.brokers_lock));

done:
    return ZPATH_RESULT_NO_ERROR;
}


/*
 * curl -s '127.0.0.1:9000/assistant/data/dump_connection?broker_gid=72057594037939477'
 */
static int
assistant_data_dump_connection(struct zpath_debug_state*  request_state,
                               const char **              query_values,
                               int                        query_value_count,
                               void*                      cookie)
{
    struct zpn_assistant_broker_data* broker;
    int64_t                           broker_gid;
    char*                             history_str;
    int                               has_fohh = 0;
    int                               has_zrdt = 0;

    (void)query_value_count;
    (void)cookie;
    broker = NULL;
    if (!query_values[0]) {
        ZDP("Missing argument: broker_gid\n");
        goto done;
    }
    broker_gid = strtoll(query_values[0], NULL, 10);

    broker = assistant_data_get_locked_internal(broker_gid, broker_gid, zpn_fohh_tlv);
    if (broker) {
        struct fohh_connection *f_conn = zpn_mconn_fohh_tlv_get_conn(&(broker->broker_tlv_state));

        history_str = ASST_MALLOC(sizeof(char) * FOHH_HISTORY_STR_MIN_LEN);
        fohh_history_get_str(f_conn, history_str, FOHH_HISTORY_STR_MIN_LEN);
        ZDP("fohh                  : %s ", fohh_description(f_conn));
        ZDP("state : %s\n", fohh_connection_state_strings[broker->fohh_conn_state]);
        ZDP("bytes in input buffer : %zd\n", f_conn->io_bufferevent ? evbuffer_get_length(bufferevent_get_input(f_conn->io_bufferevent)) : -1);
        ZDP("bytes in output buffer: %zd\n", f_conn->io_bufferevent ? evbuffer_get_length(bufferevent_get_output(f_conn->io_bufferevent)): -1);
        ZDP("%s", history_str);
        ASST_FREE(history_str);
        has_fohh = 1;
        pthread_mutex_unlock(&(broker->lock));
        broker = NULL;
    }

    broker = assistant_data_get_locked_internal(broker_gid, broker_gid, zpn_zrdt_tlv);
    if (broker) {

        struct zrdt_conn *z_conn = zpn_mconn_zrdt_tlv_get_conn(&(broker->broker_zrdt_tlv_state));
        enum zrdt_conn_status status = z_conn ? zrdt_conn_get_status(z_conn) : zrdt_conn_disconnected;

        ZDP("zrdt                  : %s ", zpn_tlv_description(&(broker->broker_zrdt_tlv_state.tlv)));
        ZDP("state : %s\n", zrdt_conn_status_string(status));
        has_zrdt = 1;
        pthread_mutex_unlock(&(broker->lock));
        broker = NULL;
    }

    if (!has_fohh && !has_zrdt) {
        ZDP("No data connection to broker GID=%"PRId64, broker_gid);
    }

done:
    return ZPATH_RESULT_NO_ERROR;
}


static int
assistant_data_log_status_internal_aggregate_stats(void*  cookie,
                                                   void*  object,
                                                   void*  key,
                                                   size_t key_len)
{
    struct zpn_assistant_broker_data *broker = (struct zpn_assistant_broker_data *)object;
    struct fohh_connection *f_conn = zpn_mconn_fohh_tlv_get_conn(&(broker->broker_tlv_state));
    struct zrdt_conn *z_conn = zpn_mconn_zrdt_tlv_get_conn(&(broker->broker_zrdt_tlv_state));

    (void) cookie;
    if (broker->is_pbroker) {
        if (broker->tlv_type == zpn_fohh_tlv) {
            stats.data_conn_to_pbroker_fohh++;
            if (f_conn) {
                if (fohh_connection_connected != fohh_get_state(f_conn)) {
                    stats.backed_off_data_conn_to_pbroker_fohh++;
                }
                stats.to_pbroker_data_active_fohh += broker->broker_tlv_state.tx_data;
                stats.from_pbroker_data_active_fohh += broker->broker_tlv_state.rx_data;
            }
        } else {
            stats.data_conn_to_pbroker_dtls++;
            if (z_conn) {
                if (zrdt_conn_connected != zrdt_conn_get_status(z_conn)) {
                    stats.backed_off_data_conn_to_pbroker_dtls++;
                }
                stats.to_pbroker_data_active_dtls += broker->broker_zrdt_tlv_state.tx_data;
                stats.from_pbroker_data_active_dtls += broker->broker_zrdt_tlv_state.rx_data;
            }
        }
    } else {
        if (broker->tlv_type == zpn_fohh_tlv) {
            stats.data_conn_to_broker_fohh++;
            if (f_conn) {
                if (fohh_connection_connected != fohh_get_state(f_conn)) {
                    stats.backed_off_data_conn_to_broker_fohh++;
                }
                stats.to_broker_data_active_fohh += broker->broker_tlv_state.tx_data;
                stats.from_broker_data_active_fohh += broker->broker_tlv_state.rx_data;
            }
        } else {
            stats.data_conn_to_broker_dtls++;
            if (z_conn) {
                if (zrdt_conn_connected != zrdt_conn_get_status(z_conn)) {
                    stats.backed_off_data_conn_to_broker_dtls++;
                }
                stats.to_broker_data_active_dtls += broker->broker_zrdt_tlv_state.tx_data;
                stats.from_broker_data_active_dtls += broker->broker_zrdt_tlv_state.rx_data;
            }
        }
    }

    return ZPN_RESULT_NO_ERROR;
}


/*
 * broker->broker_tlv_state.f_conn will be NULL when the broker is disconnected. On a disconnect, we don't delete the
 * broker object, just clear out the f_conn and keep that object. This is true only for public broker.
 */
static void
assistant_data_log(evutil_socket_t  sock,
                   short            flags,
                   void*            cookie)
{
    struct zpn_assistant_broker_data*   broker;
    char                                log_str[4000] = { '\0' };
    char*                               start;
    char*                               end;
    char                                uptime_str[FOHH_MAX_UPTIME_BUF_LEN];
    char                                max_rtt_us_str[FOHH_RTT_SINCE_LAST_QUERY_MSG_STR_MAX];

    (void)sock;
    (void)flags;
    broker = cookie;
    pthread_mutex_lock(&(state.brokers_lock));
    pthread_mutex_lock(&(broker->lock));

    start = log_str;
    end = log_str + sizeof(log_str);

    if (broker->tlv_type == zpn_fohh_tlv) {
        struct zpn_fohh_tlv*                fohh_tlv;

        if (NULL == zpn_mconn_fohh_tlv_get_conn(&(broker->broker_tlv_state))) {
            goto done;
        }

        fohh_tlv = &broker->broker_tlv_state;
        struct fohh_connection *f_conn = zpn_mconn_fohh_tlv_get_conn(fohh_tlv);
        struct fohh_connection_stats *fconn_stats = &f_conn->stats;

        if (!assistant_cfg_override_is_idle_data_conn_timeout_config_disabled()) {
            int64_t idle_connection_timeout_s = assistant_cfg_override_get_idle_data_connection_timeout_s();
            int64_t idle_connection_timeout_us = SECOND_TO_US(idle_connection_timeout_s);
            int is_mtunnel_list_empty = TAILQ_EMPTY(&(broker->mtunnel_list));
            int is_last_bytes_rx_us_timeout = ((fohh_tlv->rx_data_us) && ((epoch_us() - fohh_tlv->rx_data_us) >= idle_connection_timeout_us))?1:0;
            int is_last_bytes_tx_us_timeout = ((fohh_tlv->tx_data_us) && ((epoch_us() - fohh_tlv->tx_data_us) >=  idle_connection_timeout_us))?1:0;
            int is_broker_fohh_tlv_connected = (broker->fohh_conn_state == fohh_connection_connected)?1:0;

            if (is_mtunnel_list_empty && is_last_bytes_rx_us_timeout && is_last_bytes_tx_us_timeout && is_broker_fohh_tlv_connected) {
                ASSISTANT_LOG(AL_ERROR, "Kill connection to %s, as it is idle for more than %ld seconds",
                              fohh_description(zpn_mconn_fohh_tlv_get_conn(fohh_tlv)), (long)idle_connection_timeout_s);
                /* Kill the connection */
                broker->idle_force_disconnect = 1;
                pthread_mutex_unlock(&(broker->lock));
                pthread_mutex_unlock(&(state.brokers_lock));
                fohh_connection_disconnect(zpn_mconn_fohh_tlv_get_conn(fohh_tlv), FOHH_CLOSE_DATA_CONN_IDLE_TIMEOUT);
                return;
            }
        }

        fohh_connection_max_rtt_to_string(fohh_tlv->tlv.conn.f_conn, max_rtt_us_str, sizeof(max_rtt_us_str));
        fohh_connection_reset_max_rtt(fohh_tlv->tlv.conn.f_conn);

        if (0 == broker->is_pbroker) {
            start += sxprintf(start, end, "Broker data connection (FOHH), %s",
                          fohh_description(zpn_mconn_fohh_tlv_get_conn(&(broker->broker_tlv_state))));
        } else {
            char pbroker_name[ASSISTANT_CFG_PBROKER_NAME_MAX_LEN];
            assistant_cfg_pbroker_get_name(broker->broker_id, pbroker_name, sizeof(pbroker_name), NULL, 0, NULL);
            start += sxprintf(start, end, "Private Broker data connection (FOHH), %s:%s", pbroker_name,
                          fohh_description(zpn_mconn_fohh_tlv_get_conn(&(broker->broker_tlv_state))));
        }
        ZPATH_MUTEX_LOCK(&(fohh_tlv->lock), __FILE__, __LINE__);
        sxprintf(start, end, ", uptime %s%s, thread_id %d, active mtunnels %" PRId64 ", "
                 "total mtunnels %" PRId64 ", to broker %" PRId64 " bytes, from broker %" PRId64 " bytes. Detail[rx %ld, "
                 "peer_tx %ld, tx %ld, tx_drop %ld, tx_limit %ld, tx_limit_update %ld, r_rx %ld, r_tx_limit %ld, "
                 "tx_wnd_update %ld, rx_wnd_update %ld, tx_blocked %ld, enq %ld, deq %ld, data_write_blocked %ld, "
                 "data_write_blocked_fohh %ld, data_write_blocked_evbuf %ld, fohh_fc_blocked %ld, fohh_fc_blocked_time %ld, fohh_fc_block_max_time %ld, "
                 "fohh_fc_block_tot_time %ld, window_update_delta_time %ld, max_window_update_delta_time %ld, "
                 "hop_latency %ld, max_hop_latency %ld, fohh_pipeline_latency %lu, fohh_pipeline_latency_max %lu, "
                 "rx_udp_data_dropped_frame_error = %ld, rx_udp_data_dropped_tx_buf_full = %ld, rx_icmp_error_data_dropped = %ld, rx_icmp6_error_data_dropped = %ld, "
                 "bufferevent_in_len = %"PRIu64", bufferevent_in_len_max = %"PRIu64", tlv_read_buf_len = %"PRIu64", tlv_read_buf_len_max = %"PRIu64", "
                 "bufferevent_out_len = %"PRIu64", bufferevent_out_len_max = %"PRIu64", raw_tlv_buffer_len = %"PRIu64", raw_tlv_buffer_len_max = %"PRIu64", "
                 "raw_tlv_buffer_enq = %"PRIu64", raw_tlv_buffer_deq = %"PRIu64"]",
                 zpn_tlv_get_uptime_str(&(broker->broker_tlv_state.tlv), uptime_str, sizeof(uptime_str)),
                 (fohh_get_state(zpn_mconn_fohh_tlv_get_conn(&(broker->broker_tlv_state))) == fohh_connection_connected)? max_rtt_us_str: "",
                 fohh_connection_get_thread_id(fohh_tlv->tlv.conn.f_conn),
                 broker->mtunnels_by_tag->element_count, broker->stats.num_mtunnel, broker->broker_tlv_state.tx_data,
                 broker->broker_tlv_state.rx_data, (long)fohh_tlv->rx_data, (long)fohh_tlv->peer_tx_data,
                 (long)fohh_tlv->tx_data, (long)fohh_tlv->tx_data_drop, (long)fohh_tlv->tx_limit,
                 (long)fohh_tlv->tx_limit_update_us, (long)fohh_tlv->remote_rx_data, (long)fohh_tlv->remote_tx_limit,
                 (long)fohh_tlv->remote_rx_data_change_us, (long)fohh_tlv->last_wnd_update_us,
                 (long)fohh_tlv->fc_blocked_timestamp, (long)fohh_tlv->enq_bytes, (long)fohh_tlv->deq_bytes, (long)fohh_tlv->fohh_data_write_blocked,
                 (long)fohh_tlv->fohh_data_write_fohh_blocked, (long)fohh_tlv->fohh_data_write_evbuf_blocked,
                 (long)fohh_tlv->fohh_fc_blocked, (long)fohh_tlv->fohh_fc_blocked_time,
                 (long)fohh_tlv->max_fohh_fc_blocked_time,
                 (long)fohh_tlv->tot_fohh_fc_blocked_time,
                 (long)fohh_tlv->window_update_delta_time,
                 (long)fohh_tlv->max_window_update_delta_time,
                 (long)fohh_tlv->hop_latency,
                 (long)fohh_tlv->max_hop_latency,
                 (unsigned long)fohh_tlv->pipeline_latency[latency_inspection_stage_last],
                 (unsigned long)fohh_tlv->pipeline_latency_max,
                 (long)fohh_tlv->rx_udp_data_dropped_frame_error, (long)fohh_tlv->rx_udp_data_dropped_tx_buf_full,
                 (long)fohh_tlv->rx_icmp_error_data_dropped, (long)fohh_tlv->rx_icmp6_error_data_dropped,
                 fconn_stats->bufferevent_in_len, fconn_stats->bufferevent_in_len_max, fconn_stats->tlv_read_buf_len,
                 fconn_stats->tlv_read_buf_len_max, fconn_stats->bufferevent_out_len, fconn_stats->bufferevent_out_len_max,
                 fconn_stats->raw_tlv_buffer_len, fconn_stats->raw_tlv_buffer_len_max, fconn_stats->raw_tlv_buffer_enq,
                 fconn_stats->raw_tlv_buffer_deq);
        ZPATH_MUTEX_UNLOCK(&(fohh_tlv->lock), __FILE__, __LINE__);
    } else {
        struct zpn_zrdt_tlv  *zrdt_tlv;
        struct zrdt_conn_stats conn_stats;

        if (NULL == zpn_mconn_zrdt_tlv_get_conn(&(broker->broker_zrdt_tlv_state))) {
            goto done;
        }

        zrdt_tlv = &(broker->broker_zrdt_tlv_state);
        zrdt_get_conn_stats(zpn_mconn_zrdt_tlv_get_conn(zrdt_tlv), &conn_stats);

        snprintf(max_rtt_us_str, sizeof(max_rtt_us_str), ", rtt: %"PRIu64" us", conn_stats.max_rtt_us);
        if (0 == broker->is_pbroker) {
            start += sxprintf(start, end, "Broker data connection (DTLS), %s", zpn_tlv_description(&(zrdt_tlv->tlv)));
        } else {
            char pbroker_name[ASSISTANT_CFG_PBROKER_NAME_MAX_LEN];
            assistant_cfg_pbroker_get_name(broker->broker_id, pbroker_name, sizeof(pbroker_name), NULL, 0, NULL);
            start += sxprintf(start, end, "Private Broker data connection (DTLS), %s:%s", pbroker_name, zpn_tlv_description(&(zrdt_tlv->tlv)));
        }

        sxprintf(start, end, ", uptime %s%s, active_mtunnels %" PRId64 ", "
                 "total mtunnels %" PRId64 ", to broker %" PRId64 " bytes, from broker %" PRId64 " bytes. Detail[rx_pkt %ld, "
                 "tx_pkt %ld, lloss %ld, rloss = %ld]",
                 zpn_tlv_get_uptime_str(&(zrdt_tlv->tlv), uptime_str, sizeof(uptime_str)), max_rtt_us_str,
                 broker->mtunnels_by_tag->element_count, broker->stats.num_mtunnel,
                 zrdt_tlv->tx_data, zrdt_tlv->rx_data, (long)conn_stats.packets_recv, (long)conn_stats.packets_sent,
                 (long)conn_stats.local_loss, (long)conn_stats.peer_loss);
    }
    ASSISTANT_LOG(AL_NOTICE, "%s", log_str);

done:
    pthread_mutex_unlock(&(broker->lock));
    pthread_mutex_unlock(&(state.brokers_lock));
}


static int
assistant_data_log_status_update_stats()
{
    int64_t walk_key;

    if (!state.brokers) {
        return ZPN_RESULT_ERR;
    }
    pthread_mutex_lock(&(state.brokers_lock));

    stats.data_conn_to_broker_fohh = 0;
    stats.data_conn_to_broker_dtls = 0;
    stats.backed_off_data_conn_to_broker_fohh = 0;
    stats.backed_off_data_conn_to_broker_dtls = 0;
    stats.to_broker_data_active_fohh = 0;
    stats.to_broker_data_active_dtls = 0;
    stats.from_broker_data_active_fohh = 0;
    stats.from_broker_data_active_dtls = 0;
    stats.data_conn_to_pbroker_fohh = 0;
    stats.data_conn_to_pbroker_dtls = 0;
    stats.backed_off_data_conn_to_pbroker_fohh = 0;
    stats.backed_off_data_conn_to_pbroker_dtls = 0;
    stats.to_pbroker_data_active_fohh = 0;
    stats.to_pbroker_data_active_dtls = 0;
    stats.from_pbroker_data_active_fohh = 0;
    stats.from_pbroker_data_active_dtls = 0;

    walk_key = 0;
    zhash_table_walk(state.brokers, &walk_key, assistant_data_log_status_internal_aggregate_stats, NULL);
    stats.stats_aggregated_cloud_time_us = assistant_state_get_current_time_cloud_us();
    pthread_mutex_unlock(&(state.brokers_lock));

    return ZPN_RESULT_NO_ERROR;
}

void assistant_check_enhanced_buffer()
{
    int enhanced = 0;
    int64_t fohh_window = 0;
    int64_t fohh_mconn_window = 0;
    int64_t fohh_watermark = 0;
    int64_t fohh_mconn_watermark = 0;
    if (assistant_features_is_app_buffer_tune_enabled(global_assistant.gid, &global_assistant.grp_gids[0],
                                                      global_assistant.grp_gids_len)) {
        fohh_window = assistant_config_get_fohh_window(global_assistant.gid, &global_assistant.grp_gids[0],
                                                       global_assistant.grp_gids_len);
        fohh_mconn_window = assistant_config_get_fohh_mconn_window(global_assistant.gid, &global_assistant.grp_gids[0],
                                                                   global_assistant.grp_gids_len);
        fohh_watermark = assistant_config_get_fohh_watermark(global_assistant.gid, &global_assistant.grp_gids[0],
                                                             global_assistant.grp_gids_len);
        fohh_mconn_watermark = assistant_config_get_fohh_mconn_watermark(global_assistant.gid, &global_assistant.grp_gids[0],
                                                                         global_assistant.grp_gids_len);
        enhanced = 1;
        zpn_mconn_fohh_tlv_app_buffer_tune_enable(enhanced, fohh_window, fohh_mconn_window, fohh_watermark, fohh_mconn_watermark);
    } else {
        zpn_mconn_fohh_tlv_app_buffer_tune_enable(enhanced, fohh_window, fohh_mconn_window, fohh_watermark, fohh_mconn_watermark);
    }
}

void
assistant_data_log_status()
{
    int res;

    res = assistant_data_log_status_update_stats();
    if (res) {
        return;
    }
    ZPN_LOG(AL_NOTICE, "Broker data connection (total|active|backoff) fohh (%"PRId64"|%"PRId64"|%"PRId64") dtls (%"PRId64"|%"PRId64"|%"PRId64")",
            stats.data_conn_to_broker_fohh, (stats.data_conn_to_broker_fohh - stats.backed_off_data_conn_to_broker_fohh), stats.backed_off_data_conn_to_broker_fohh,
            stats.data_conn_to_broker_dtls, (stats.data_conn_to_broker_dtls- stats.backed_off_data_conn_to_broker_dtls), stats.backed_off_data_conn_to_broker_dtls);
    ZPN_LOG(AL_NOTICE, "Broker data transfer (to broker|from broker) fohh(%"PRId64"|%"PRId64") dtls(%"PRId64"|%"PRId64")",
            (stats.to_broker_data_active_fohh + stats.to_broker_data_past_fohh), (stats.from_broker_data_active_fohh + stats.from_broker_data_past_fohh),
            (stats.to_broker_data_active_dtls + stats.to_broker_data_past_dtls), (stats.from_broker_data_active_dtls + stats.from_broker_data_past_dtls));
    if (assistant_state_is_pbroker_environment()) {
        ZPN_LOG(AL_NOTICE, "Private Broker data connection (total|active|backoff) fohh (%"PRId64"|%"PRId64"|%"PRId64") dtls (%"PRId64"|%"PRId64"|%"PRId64")",
                stats.data_conn_to_pbroker_fohh, (stats.data_conn_to_pbroker_fohh - stats.backed_off_data_conn_to_pbroker_fohh), stats.backed_off_data_conn_to_pbroker_fohh,
                stats.data_conn_to_pbroker_dtls, (stats.data_conn_to_pbroker_dtls - stats.backed_off_data_conn_to_pbroker_dtls), stats.backed_off_data_conn_to_pbroker_dtls);
        ZPN_LOG(AL_NOTICE, "Private broker data transfer (to broker|from broker) fohh (%"PRId64"|%"PRId64")  dtls (%"PRId64"|%"PRId64")",
                (stats.to_pbroker_data_active_fohh + stats.to_pbroker_data_past_fohh), (stats.from_pbroker_data_active_fohh + stats.from_pbroker_data_past_fohh),
                (stats.to_pbroker_data_active_dtls + stats.to_pbroker_data_past_dtls), (stats.from_pbroker_data_active_dtls + stats.from_pbroker_data_past_dtls));
    }

    assistant_data_mtunnel_log_status();
    assistant_check_enhanced_buffer();
}


static int
assistant_data_stats_dump(struct zpath_debug_state*  request_state,
                          const char**               query_values,
                          int                        query_value_count,
                          void*                      cookie)
{
    char        jsonout[10000];

    (void)query_values;
    (void)query_value_count;
    (void)cookie;

    if (ARGO_RESULT_NO_ERROR == argo_structure_dump(assistant_data_stats_description,
                                                    &stats, jsonout, sizeof(jsonout), NULL, 1)) {
        ZDP("%s\n", jsonout);
    }

    return ZPATH_RESULT_NO_ERROR;
}

/*
 * Q:why slow free of key?
 * A:Helps to operate without lockless. Somebody can hold this pointer, but still not be able to do something as
 * the internal elements are reset and the parent object itself is not there at the time somebody comes back to this
 * file with the key (i.e as cookie).
 */
static void assistant_data_broker_destroy(struct zpn_assistant_broker_data *broker)
{
    if (broker->broker_name) ASST_FREE(broker->broker_name);
    if (broker->broker_sni) {
        ASST_FREE(broker->broker_sni);
        broker->broker_sni = NULL;
    }
    if (broker->hash_entry_key) ASST_FREE_SLOW(broker->hash_entry_key);
    if (broker->conn_type == ZPN_ASSISTANT_MTUNNEL_CONTROL_BROKER_PROXY_CONN) {
        __sync_fetch_and_sub_8(&global_assistant.num_doublehop_conn, 1);
    }
    ASST_FREE(broker);
}

/*
 * Stats upload callback to fill mtunnel related info. This data will be old by upto 1mins.
 */
int
assistant_data_stats_fill(void*     cookie,
                          int       counter,
                          void*     structure_data)
{
    struct zpn_assistant_data_stats*    out_data;
    int64_t                             delta_s;

    (void)counter;
    (void)cookie;

    out_data = (struct zpn_assistant_data_stats *)structure_data;

    out_data->cloud_time_us = stats.stats_aggregated_cloud_time_us;
    delta_s = 0;
    if (exported_stats.cloud_time_us) {
        delta_s = (out_data->cloud_time_us - exported_stats.cloud_time_us) / (1000 * 1000);
    }
    out_data->active_conn_to_broker_fohh = (stats.data_conn_to_broker_fohh - stats.backed_off_data_conn_to_broker_fohh);
    out_data->active_conn_to_broker_dtls = (stats.data_conn_to_broker_dtls - stats.backed_off_data_conn_to_broker_dtls);
    out_data->backed_off_conn_to_broker_fohh = stats.backed_off_data_conn_to_broker_fohh;
    out_data->backed_off_conn_to_broker_dtls = stats.backed_off_data_conn_to_broker_dtls;
    out_data->to_broker_bytes_fohh = (stats.to_broker_data_active_fohh + stats.to_broker_data_past_fohh);
    out_data->to_broker_bytes_dtls = (stats.to_broker_data_active_dtls + stats.to_broker_data_past_dtls);
    out_data->from_broker_bytes_fohh = (stats.from_broker_data_active_fohh + stats.from_broker_data_past_fohh);
    out_data->from_broker_bytes_dtls = (stats.from_broker_data_active_dtls + stats.from_broker_data_past_dtls);
    out_data->active_conn_to_pbroker_fohh = (stats.data_conn_to_pbroker_fohh - stats.backed_off_data_conn_to_pbroker_fohh);
    out_data->active_conn_to_pbroker_dtls = (stats.data_conn_to_pbroker_dtls - stats.backed_off_data_conn_to_pbroker_dtls);
    out_data->backed_off_conn_to_pbroker_fohh = stats.backed_off_data_conn_to_pbroker_fohh;
    out_data->backed_off_conn_to_pbroker_dtls = stats.backed_off_data_conn_to_pbroker_dtls;
    out_data->to_pbroker_bytes_fohh = (stats.to_pbroker_data_active_fohh + stats.to_pbroker_data_past_fohh);
    out_data->to_pbroker_bytes_dtls = (stats.to_pbroker_data_active_dtls + stats.to_pbroker_data_past_dtls);
    out_data->from_pbroker_bytes_fohh = (stats.from_pbroker_data_active_fohh + stats.from_pbroker_data_past_fohh);
    out_data->from_pbroker_bytes_dtls = (stats.from_pbroker_data_active_dtls + stats.from_pbroker_data_past_dtls);

    if (delta_s) {
        out_data->to_broker_bytes_Bps_fohh = (out_data->to_broker_bytes_fohh - exported_stats.to_broker_bytes_fohh)/delta_s;
        out_data->to_broker_bytes_Bps_dtls = (out_data->to_broker_bytes_dtls - exported_stats.to_broker_bytes_dtls)/delta_s;
        out_data->from_broker_bytes_Bps_fohh = (out_data->from_broker_bytes_fohh - exported_stats.from_broker_bytes_fohh)/delta_s;
        out_data->from_broker_bytes_Bps_dtls = (out_data->from_broker_bytes_dtls - exported_stats.from_broker_bytes_dtls)/delta_s;
        out_data->to_pbroker_bytes_Bps_fohh = (out_data->to_pbroker_bytes_fohh - exported_stats.to_pbroker_bytes_fohh)/delta_s;
        out_data->to_pbroker_bytes_Bps_dtls = (out_data->to_pbroker_bytes_dtls - exported_stats.to_pbroker_bytes_dtls)/delta_s;
        out_data->from_pbroker_bytes_Bps_fohh = (out_data->from_pbroker_bytes_fohh - exported_stats.from_pbroker_bytes_fohh)/delta_s;
        out_data->from_pbroker_bytes_Bps_dtls = (out_data->from_pbroker_bytes_dtls - exported_stats.from_pbroker_bytes_dtls)/delta_s;
        memcpy(&exported_stats, out_data, sizeof(exported_stats));
    } else {
        out_data->to_broker_bytes_Bps_fohh = 0;
        out_data->to_broker_bytes_Bps_dtls = 0;
        out_data->from_broker_bytes_Bps_fohh = 0;
        out_data->from_broker_bytes_Bps_dtls = 0;
        out_data->to_pbroker_bytes_Bps_fohh = 0;
        out_data->to_pbroker_bytes_Bps_dtls = 0;
        out_data->from_pbroker_bytes_Bps_fohh = 0;
        out_data->from_pbroker_bytes_Bps_dtls = 0;
        /* only the first time save the copy, otherwise let the gap grow to help bps calculation */
        if (0 == exported_stats.cloud_time_us) {
            memcpy(&exported_stats, out_data, sizeof(exported_stats));
        }
    }

    return ZPATH_RESULT_NO_ERROR;
}

/*
 * Fill assistant data stats into zpn_assistant_comprehensive_stats, which will be consumed by GUI/LSS
 */
int
assistant_data_comprehensive_stats_fill(void* structure_data)
{
    int res;
    struct zpn_assistant_comprehensive_stats* out_data;

    out_data = (struct zpn_assistant_comprehensive_stats*)structure_data;

    res = assistant_data_log_status_update_stats();

    if (res) {
        return res;
    }

    out_data->active_conn_to_broker = (stats.data_conn_to_broker_fohh - stats.backed_off_data_conn_to_broker_fohh)
                                    + (stats.data_conn_to_broker_dtls - stats.backed_off_data_conn_to_broker_dtls);
    out_data->backed_off_conn_to_broker = stats.backed_off_data_conn_to_broker_fohh
                                        + stats.backed_off_data_conn_to_broker_dtls;
    out_data->active_conn_to_pbroker = (stats.data_conn_to_pbroker_fohh - stats.backed_off_data_conn_to_pbroker_fohh)
                                     + (stats.data_conn_to_pbroker_dtls - stats.backed_off_data_conn_to_pbroker_dtls);
    out_data->backed_off_conn_to_pbroker = stats.backed_off_data_conn_to_pbroker_fohh
                                         + stats.backed_off_data_conn_to_pbroker_dtls;

    out_data->to_broker_bytes = (stats.to_broker_data_active_fohh + stats.to_broker_data_past_fohh)
                              + (stats.to_broker_data_active_dtls + stats.to_broker_data_past_dtls);
    out_data->from_broker_bytes = (stats.from_broker_data_active_fohh + stats.from_broker_data_past_fohh)
                                + (stats.from_broker_data_active_dtls + stats.from_broker_data_past_dtls);
    out_data->to_pbroker_bytes = (stats.to_pbroker_data_active_fohh + stats.to_pbroker_data_past_fohh)
                               + (stats.to_pbroker_data_active_dtls + stats.to_pbroker_data_past_dtls);
    out_data->from_pbroker_bytes = (stats.from_pbroker_data_active_fohh + stats.from_pbroker_data_past_fohh)
                                 + (stats.from_pbroker_data_active_dtls + stats.from_pbroker_data_past_dtls);

    out_data->to_broker_bytes_delta = out_data->to_broker_bytes - exported_comprehensive_stats.to_broker_bytes;
    out_data->from_broker_bytes_delta = out_data->from_broker_bytes - exported_comprehensive_stats.from_broker_bytes;
    out_data->to_pbroker_bytes_delta = out_data->to_pbroker_bytes - exported_comprehensive_stats.to_pbroker_bytes;
    out_data->from_pbroker_bytes_delta = out_data->from_pbroker_bytes - exported_comprehensive_stats.from_pbroker_bytes;
    memcpy(&exported_comprehensive_stats, out_data, sizeof(exported_comprehensive_stats));

    return ZPATH_RESULT_NO_ERROR;
}

void
assistant_data_iterate_brokers_and_do_callback(zhash_table_walk_f cb,
                                               void*              cookie)
{
    int64_t walk_key = 0;

    if (!state.brokers) {
        goto done;
    }

    pthread_mutex_lock(&(state.brokers_lock));
    zhash_table_walk(state.brokers, &walk_key, cb, NULL);
    pthread_mutex_unlock(&(state.brokers_lock));

done:
    return;
}

int
assistant_data_rpc_tx_bind(struct zpn_assistant_broker_data* broker,
                           char*                             mtunnel_id,
                           int64_t                           server_rtt_us,
                           int64_t                           g_bfw,
                           int64_t                           bfw_us,
                           int64_t                           g_aps,
                           struct argo_inet                  brk_req_server_inet,
                           struct argo_inet                  server_inet,
                           uint16_t                          server_port_he,
                           struct argo_inet                  asst_inet,
                           uint16_t                          asst_port_he,
                           int64_t                           brk_req_dsp_tx_us,
                           int64_t                           ast_tx_us,
                           int64_t                           brk_req_ast_rx_us,
                           int64_t                           g_app,
                           int64_t                           g_app_grp,
                           int64_t                           g_ast_grp,
                           int64_t                           g_srv_grp,
                           int64_t                           g_dsp,
                           uint64_t                          path_decision,
                           int8_t                            dsp_bypassed,
                           uint8_t                           insp_status,
                           uint64_t                          ssl_err)
{
    int res;

    if (ut_hook.number_of_bind_messages_to_be_dropped) {
        __sync_sub_and_fetch_8(&ut_hook.number_of_bind_messages_to_be_dropped, 1);
        ASSISTANT_LOG(AL_NOTICE, "%s: dropped dropped per UT drop message config, still (%lld) bind messages set to be dropped",
                      mtunnel_id, (long long)ut_hook.number_of_bind_messages_to_be_dropped);
        res = ZPN_RESULT_ERR;
        goto handle_status;
    }

    res = zpn_send_zpn_mtunnel_bind(assistant_data_get_tlv(broker), 0, mtunnel_id, server_rtt_us,
                                    g_bfw, bfw_us, g_aps, brk_req_server_inet, server_inet, server_port_he, asst_inet,
                                    asst_port_he, brk_req_dsp_tx_us, ast_tx_us, brk_req_ast_rx_us, g_app, g_app_grp,
                                    g_ast_grp, g_srv_grp, g_dsp, path_decision, dsp_bypassed, insp_status, ssl_err);

handle_status:
    if (res) {
        __sync_add_and_fetch_8(&global_assistant.num_bind_fails, 1);
        ASSISTANT_LOG(AL_ERROR, "%s: Send mtunnel bind msg to data broker - failed(%s)", mtunnel_id,
                      zpath_result_string(res));
        return res;
    } else {
        __sync_add_and_fetch_8(&global_assistant.num_bind, 1);
        ASSISTANT_DEBUG_DATA("%s: Sent mtunnel bind msg to data broker", mtunnel_id);
    }

    return ZPATH_RESULT_NO_ERROR;
}


int
assistant_data_rpc_tx_state_one_broker(struct zpn_assistant_broker_data*   broker)
{
    struct zpn_tlv *tlv;

    if (!broker) {
        ASSISTANT_DEBUG_DATA("No broker to send asst_state");
        return ZPATH_RESULT_NO_ERROR;
    }

    if (((broker->tlv_type == zpn_fohh_tlv) && (broker->fohh_conn_state != fohh_connection_connected)) ||
        ((broker->tlv_type == zpn_zrdt_tlv) && (broker->zdtls_sess_status != zdtls_session_open))) {
        ASSISTANT_DEBUG_DATA("Underlying data connection to broker %s is not up", broker->broker_name);
        return ZPATH_RESULT_NO_ERROR;
    }

    tlv = assistant_data_get_tlv(broker);

    assistant_state_send_zpn_asst_state(tlv, zpn_tlv_incarnation(tlv));
    __sync_add_and_fetch_8(&global_assistant.num_state_tx, 1);

    return ZPATH_RESULT_NO_ERROR;
}


static int
assistant_data_rpc_tx_state_one_broker_from_hash_walk(void*  cookie,
                                                      void*  object,
                                                      void*  key,
                                                      size_t key_len)
{
    struct zpn_assistant_broker_data* broker = (struct zpn_assistant_broker_data *)object;
    assistant_data_rpc_tx_state_one_broker(broker);
    return ZPATH_RESULT_NO_ERROR;
}


void
assistant_data_rpc_tx_state()
{
    assistant_data_iterate_brokers_and_do_callback(assistant_data_rpc_tx_state_one_broker_from_hash_walk, NULL);
}


int
assistant_data_rpc_tx_broker_request_ack(void*                          cookie,
                                         struct zpn_broker_request_ack* ack)
{
    int                                  res;
    struct zpn_assistant_broker_data*    broker;
    struct zpn_tlv*                      connection;
    struct zpn_assistant_broker_data_id* hash_entry_key;

    broker = NULL;
    connection = NULL;
    res = ZPN_RESULT_ERR;
    hash_entry_key = cookie;
    if (NULL == hash_entry_key) {
        ASSISTANT_ASSERT_SOFT(0, "hash entry key is null when trying to send broker request ack to data broker");
        goto done;
    }

    if (NULL == ack) {
        ASSISTANT_ASSERT_SOFT(0, "broker request ack buffer is invalid when trying to send broker request ack to data broker");
        goto done;
    }

    broker = assistant_data_get_locked(hash_entry_key->broker_id, hash_entry_key->end_broker_id);
    if (NULL == broker) {
        __sync_add_and_fetch_8(&stats.tx_broker_request_ack_fail_no_broker, 1);
        goto done;
    }

    connection = assistant_data_get_tlv(broker);
    if (connection) {
        __sync_add_and_fetch_8(&stats.tx_broker_request_ack_fail_no_connection, 1);
        res = zpn_send_zpn_broker_request_ack_struct_on_zpn_tlv(connection, 0, ack);
    }

done:
    if (ZPN_RESULT_NO_ERROR == res) {
        ASSISTANT_LOG(AL_NOTICE, "%s, domain:%s port:%d ip_protocol:%s: sent broker request ack(%s) to data broker(%s)", ack->mtunnel_id,
                      ack->domain, ack->s_port, get_assistant_ip_proto_string(ack->ip_protocol), ack->error, connection ? zpn_tlv_description(connection) : " ");
        __sync_add_and_fetch_8(&global_assistant.num_brk_req_ack_to_ubrk, 1);
    } else {
        if (ack) {
            ASSISTANT_LOG(AL_NOTICE, "%s, domain:%s port:%d ip_protocol:%s: Could not send broker request ack(%s) to control broker(%s)", ack->mtunnel_id,
                                     ack->domain, ack->s_port, get_assistant_ip_proto_string(ack->ip_protocol), ack->error, connection ? zpn_tlv_description(connection) : " ");
        }
        __sync_add_and_fetch_8(&global_assistant.num_brk_req_ack_to_ubrk_fails, 1);
    }
    if (broker) pthread_mutex_unlock(&(broker->lock));
    return res;
}

int
assistant_data_mconn_stats(struct zpn_assistant_broker_data* broker,
                                   struct zpn_mtunnel_data_mconn_stats* data)
{
    int res = ZPATH_RESULT_NO_ERROR;
    struct fohh_connection *f_conn = NULL;

    if (broker->tlv_type == zpn_fohh_tlv) {
        f_conn = zpn_mconn_fohh_tlv_get_conn(&(broker->broker_tlv_state));
    }
    if (!f_conn) {
        ASSISTANT_LOG(AL_ERROR, "track perf assistant_data_mconn_stats track perf error no f_conn");
        return ZPATH_RESULT_ERR;
    }

    res = zpn_send_zpn_mtunnel_data_mconn_stats(f_conn, fohh_connection_incarnation(f_conn), data);

    return res;
}
