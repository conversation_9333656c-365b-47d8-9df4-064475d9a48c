/*
 * zpn_brokerd.c. Copyright (C) 2014 Zscaler, Inc. All Rights Reserved.
 *
 */

#include <stdio.h>
#include <stdarg.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <pthread.h>
#include <sys/time.h>
#include <ctype.h>
#include <errno.h>
#include <fcntl.h>

#include "avl/avl.h"

#include "argo/argo.h"
#include "fohh/fohh.h"
#include "fohh/fohh_http.h"
#include "wally/wally.h"
#include "wally/wally_postgres.h"
#include "wally/wally_fohh_client.h"
#include "wally/wally_fohh_server.h"
#include "zpath_misc/zpath_misc.h"
#include "zsdtls/zdtls.h"
#include "zrdt/zrdt.h"
#include "zcrypto_lib/zcrypto_lib.h"

#include "zpath_lib/zpath_local.h"
#include "zpath_lib/zpath_debug.h"
#include "zpath_lib/zpath_app.h"
#include "zpath_lib/zpath_customer.h"
#include "zpath_lib/zpath_domain_lookup.h"
#include "zpath_lib/zpath_et_customer_userdb.h"
#include "zpath_lib/zpath_et_zone.h"
#include "zpath_lib/zpath_et_userdb.h"
#include "zpath_lib/zpath_et_customer_zone.h"
#include "zpath_lib/zpath_et_service_endpoint.h"
#include "zpath_lib/zpath_et_wally_userdb.h"
#include "zpath_lib/zpath_geoip.h"
#include "zpath_lib/zpath_constellation.h"
#include "zpath_lib/et_geoip_override.h"
#include "zpath_lib/zpath_sync_pause.h"

#include "zthread/zthread.h"
#include "zpn/zpn_lib.h"
#include "zpn/zpn_broker.h"
#include "zpn/zpn_application.h"
#include "zpn/zpn_application_domain.h"
#include "zpn/zpn_broker_dispatch_pool.h"

#include "zevent/zevent.h"
#include "zcdns/zcdns_libevent.h"
#include "zpn/zpn_broker_common.h"
#include "object_store/object_store.h"
#include "object_store/object_store_rpc.h"
#include "zpath_misc/zpath_version.h"
#include "zpath_lib/sanitizer_config.h"
#include "zhealth/zhealth.h"
#include "zhealth/zhealth_probe_lib.h"
#include "zhealth/zhealth_probe_lib_thread.h"
#include "zhealth/zhealth_probe_udp.h"
#include "zpn_zdx/zpn_zdx_mtr.h"
#include "zpn_zdx/zpn_zdx_lib.h"
#include "zpn_pcap/zpn_pcap_lib.h"
#include "zpn_pcap/zpn_pcap.h"
#include "zpn/zpn_broker_maintenance.h"
#include "zhealth/zhealth.h"
#include "zhealth/zhealth_probe_lib.h"
#include "zhealth/zhealth_probe_lib_thread.h"
#include "zhealth/zhealth_probe_udp.h"
#include "zpn_zdx/zpn_zdx_mtr.h"
#include "zpn_zdx/zpn_zdx_probes.h"
#include "zpn_assistantd/zpn_assistant_private.h"
#include "zpn_zdx/zpn_zdx_lib.h"
#include "zpn_zdx/zpn_zdx_feature.h"
#include "zpn_zdx/zpn_zdx_rate_limit_config.h"
#include "zpn_zdx/zpn_zdx_webprobe_rate_limit.h"
#include "admin_probe/zpn_command_probe.h"
#include "admin_probe/admin_probe_rpc.h"
#include "zpn/zpn_siem.h"
#include "zpn_event/zpn_event.h"
#include "zpn_event/zpn_event_config_override.h"
#include "zpn/zpn_region_restriction.h"

#include "zpn_waf/zpn_public_certificate.h"
#include "zpn_waf/zpn_inspection_profile_to_control.h"
#include "zpn_waf/zpn_inspection_prof_to_zsdefined_ctrl.h"
#include "zpn_waf/zpn_inspection_config_data.h"
#include "zpn_waf/zpn_inspection_zsdefined_control.h"
#include "zpn/zpn_inspection_application.h"
#include "zpn_waf/zpn_inspection_profile.h"

#include "zpn_waf/zpn_public_certificate.h"
#include "zpn_waf/zpn_inspection_profile_to_control.h"
#include "zpn_waf/zpn_inspection_config_data.h"
#include "zpn/zpn_inspection_application.h"
#include "zpn_waf/zpn_inspection_profile.h"
#include "zpn_zdx/zpn_zdx_webprobe_lib.h"
#include "zpn_event/zpn_event_stats.h"
#include "zpn/zpn_machine_table.h"
#include "zpn/zpn_broker_load.h"
#include "zpn/zpn_broker_drain.h"
#include "zpn/zpn_broker_pm.h"
#include "zpn/zpn_client_setting.h"

int app_personality = 0;

int config_daemon = 0;
int config_autotune = 0;
int thread_count = 4;
int dsp_channel_count = 2;
int fully_load = 0; // default behavior changed to not fully_load
int disable_zpm_conn = 0; // set it through command line, indicates broker does not want to connect to process monitor
int debuglog = 0;
int itasca_logs_port_he = 0;
int use_sqlt = 0;
int deferred_init_sec = 0;
extern int fohh_tlv_window_size;
extern int fohh_tlv_mconn_window_size;
int ot_mode = 0;

const char *dbhost = "localhost";
const char *local_file = NULL;
extern char *geoip_db_file;
extern char *geoip_isp_db_file;
static char *stackpath = NULL;
extern int32_t zevent_use_libevent_priority;

struct event_base *broker_misc_event_base;
struct zevent_base *broker_misc_zevent_base = NULL;

#define APP_ROLE "zpn_brokerd"

void usage(const char *argv0, const char *format, ...)
    __attribute__((format(printf, 2, 3)));
void usage(const char *argv0, const char *format, ...)
{
    va_list list;

    fprintf(stdout, "Error: ");
	va_start(list, format);
	vfprintf(stdout, format, list);

    fprintf(stdout, "%s: Usage:\n", argv0);
    fprintf(stdout,
            "  -instance NUMBER  : Specify which instance ID this\n"
            "                      daemon will run as (default 0).\n"
            "  -local_file FILE  : Specify that config should come\n"
            "                      from FILE rather than local_db\n"
            "  -quiet            : Operate without FOHH status messages\n"
            "  -version          : Display version and exit\n"
            "  -debuglog         : Specify in order to send debug messages to syslog/stderr\n"
            "  -disable_heartbeat_monitor: Opt : Disable thread-hang detection\n"
            "  -dbhost NAME      : Specify DB host to connect to. Default 'localhost'\n"
            "  -geoip FILE       : Specify GeoIP DB file name\n"
            "  -geoipisp FILE    : Specify GeoIP ISP DB file name\n"
            "  -threads COUNT    : Specify number of worker threads. Default 4\n"
            "  -listen_threads COUNT : Specify the number of threads used for listening for connections. Default 1\n"
            "                      If this matches a pool exactly, then the listen socket/thread will be consistent\n"
            "  -daemon           : Run as daemon\n"
            "  -no_flow_control  : Disable flow control on FOHH\n"
            "  -fohh_win         : FOHH connection window size\n"
            "  -fohh_mconn_win   : FOHH TLV mconn window size\n"
            "  -stackpath PATH   : Write cores to path specified. No trailing slash please\n"
            "  -printf           : Use printf logging (Can be VERY verbose/slow)\n"
            "  -printf2 [1,2,3]  : Use printf logging. 1 - normal 1,2 - normal 2 3 - color\n"
            "  -c_disp GID,NAME  : Map customer GID == GID to use dispatcher pool NAME. i.e:\n"
            "                      -c_disp 144115231025528832,cp1\n"
            "                      CAN be specified multiple times\n"
            "                      custom pools MUST begin with 'c'\n"
            "  -itasca_logs PORT : Override HTTP POST logging, and send logs using argo encoding to port PORT for service endpoints\n"
            "  -autotune         : Turn on autotuning of socket buffers for clients connecting to this broker\n"
            "  -sqlite           : Use SQLite for client or not. (if not will use postgres).\n"
            "                      This flag on apply id remotehost is not NULL.\n"
            "  -fohh-pool=NAME=N,M     : Opt : Create worker pool named 'NAME' comprising threads N through M (inclusive)\n"
            "  -dsp_channels COUNT     : Specify number of dispatcher channels. Default 2\n"
            "  --print-core FILE : Opt : Read FILE and print stack\n"
            "  --print-core-force FILE : Opt : Read FILE and print stack, without checking app name/version\n"
            "  -neteventlog      : Specify in order to send eventlogs to eventlog-pg-ccf.[cloud]\n"
            "  -maxlogmb MB      : Specify maximum mb to use for argo logging.\n"
            "  -argo_mem_threshold: Specify memory threshold for argo logging in percent (default %d)\n"
            "  -deferred-init-sec : Opt : Specify deferred initialization delay in seconds.\n"
            "  -pin_disp_name DISP :  Dev/QA only. Pins the best dispatcher to DISP. DISP must be part of the pools\n"
            "  -ot_mode :  Start broker in OT mode. This will initialize OT related tables\n"
            "  -fully_load [0, 1]: Specify whether broker should fully_load certain tables (default 1).\n"
            "  -registration_wally: Specify whether broker should connect to registration wallys (default do not connect).\n"
            "  -disable_zpm_conn: Pass this flag to inform broker to not connect to process monitor.\n"
            "  -exportable_core_dumps: Pass this flag to enable exportable core dumps using packet buffer pools\n"
            "  -app_threads_count COUNT: Specify the number of app threads. Default %d",
           ARGO_MEM_THRESHOLD_DEFAULT,
           BROKER_APP_CALC_THREADS_DEFAULT
           );
    zpath_app_logging_usage_print();
    exit(1);
}

/*
 * DO NOT DELETE
 * This timer keeps the misc thread alive with heartbeats
 */
static void broker_once_per_second_timer(evutil_socket_t sock, int16_t flags, void *cookie)
{
    struct zthread_info *zthread_arg = cookie;

    zthread_heartbeat(zthread_arg);

    /* Not much to do yet */
    return;
}

static void *broker_misc_thread(struct zthread_info *zthread_arg, void *cookie)
{
    struct timeval tv;
    struct event *ev;

    broker_misc_zevent_base = zevent_attach(broker_misc_event_base);
    if (!broker_misc_zevent_base) {
        ZPN_LOG(AL_CRITICAL, "Could not attach to create zevent_base for broker_misc_thread");
        goto fail;
    }
    zpn_broker_dispatch_brk_misc_zevent_base_set(broker_misc_zevent_base);

    ev = event_new(broker_misc_event_base, -1, EV_PERSIST, broker_once_per_second_timer, zthread_arg);
    if (!ev) {
        ZPN_LOG(AL_CRITICAL, "Could not make event");
        goto fail;
    }

    /* every so often... */
    tv.tv_usec = 0;
    tv.tv_sec = 1;
    if (event_add(ev, &tv)) {
        ZPN_LOG(AL_CRITICAL, "Could not event_add");
        goto fail;
    }

    zevent_base_dispatch(broker_misc_event_base);

    ZPN_LOG(AL_CRITICAL, "Not reachable");
 fail:
    /* Should watchdog... */
    while(1) {
        sleep(1);
    }
    return NULL;
}


static void log_f(int priority, const char *format, va_list list)
{
    char dump[2000];
    vsnprintf(dump, sizeof(dump), format, list);
    if (priority < argo_log_priority_notice) {
        ZPN_LOG(priority, "%s", dump);
    } else {
        ZPN_DEBUG_HEALTH("%s", dump);
    }
}

static void customer_row_cb(struct zpath_customer *customer)
{
    int res;
    /* Might have been removed */
    res = zpn_broker_dispatch_pool_remove_custom_customer(customer->gid);
    if (res == ZPATH_RESULT_NO_ERROR) {
        ZPN_LOG(AL_NOTICE, "Removing custom pool for %ld->%s, customer name = %s", (long)customer->gid,
                customer->location ? customer->location : "NULL",
                customer->name ? customer->name : "NULL");
    } else {
        ZPN_DEBUG_DISPATCHER("Removing custom pool for %ld->%s, customer name = %s, result = %s", (long)customer->gid,
                             customer->location ? customer->location : "NULL",
                             customer->name ? customer->name : "NULL",
                             zpn_result_string(res));
    }

    if (customer->location && strlen(customer->location)) {
        if (customer->deleted) {
            /* Do nothing */
        } else {
            ZPN_LOG(AL_NOTICE, "Adding custom pool for %ld->%s, customer name = %s", (long)customer->gid, customer->location, customer->name);
            zpn_broker_dispatch_pool_add_custom_customer(customer->gid, customer->location);
        }
    }
}

/* Broker zdx combiner send callback */
static int zpn_broker_probe_combiner_send_cb(char *mtunnel_id,
                                             uint64_t mtunnel_id_hash,
                                             int64_t incarnation,
                                             void *argo_structure_cookie_ptr, //tlv
                                             struct zpn_zdx_probe_legs_info *object) //argo object
{
    int res = ZDX_RESULT_NO_ERROR;
    struct zpn_tlv *cached_tlv = (struct zpn_tlv *)argo_structure_cookie_ptr;

    res = zpn_broker_pb_send_leg_report_to_combiner(mtunnel_id, mtunnel_id_hash, incarnation, object, cached_tlv);
    if (res) {
        ZDX_LOG(AL_INFO, "%s: Unable to send leg report to combiner, ret: %s", mtunnel_id, zpath_result_string(res));
    }

    return ZDX_RESULT_NO_ERROR;
}

int sub_main(int argc, char *argv[])
{
    int instance_id = 0;
    int result;
    int i;
    struct zpn_broker *broker;
    struct zpn_common_broker_cfg public_broker = { 0 };
    pthread_t thread;
    int res;
    int listen_threads = 0;
    int app_threads_count = 0;

    zpn_broker_update_bootup_stage_stats(ZPN_BROKER_INITIAL_SETUP_STAGE_0, STAGE_START);

    zthread_init(APP_ROLE, ZPATH_VERSION, "unknown", NULL, NULL);
    zthread_do_stack_dump(&argc, argv);

    zpath_customer_set_row_cb(customer_row_cb);

    res = fohh_worker_pool_parse_args(&argc, argv);
    if (res) {
        fprintf(stderr, "Could not parse worker pool args: %s\n", fohh_result_strings[res]);
        exit(1);
    }
    if (zpath_app_logging_parse_args(&argc, argv)) {
        exit(1);
    }
    for (i = 1; i < argc; i++) {
        /* Test for all one-word arguments. */
        if (strcmp(argv[i], "-daemon") == 0) {
            config_daemon = 1;
        } else if (strcmp(argv[i], "-quiet") == 0) {
            fohh_set_quiet(1);
        } else if (strcmp(argv[i], "-version") == 0) {
            fprintf(stdout, "%s\n", ZPATH_VERSION);
            exit(0);
        } else if (strcmp(argv[i], "-neteventlog") == 0) {
            app_personality |= ZPATH_APP_PERSONALITY_SEND_EVENTLOG_TO_NETWORK;
        } else if (strcmp(argv[i], "-printf") == 0) {
            argo_log_use_printf(1);
        } else if (strcmp(argv[i], "-autotune") == 0) {
            config_autotune = 1;
        } else if (strcmp(argv[i], "-debuglog") == 0) {
            debuglog = 1;
        } else if (strcmp(argv[i], "-disable_heartbeat_monitor") == 0) {
            zthread_disable_heartbeat_monitor();
        } else if (strcmp(argv[i], "-no_flow_control") == 0) {
            flow_control_enabled = 0;
        } else if (strcmp(argv[i],"-ot_mode") == 0) {
            ot_mode = 1;
        } else if (strcmp(argv[i], "-sqlite") == 0) {
            use_sqlt = 1;
        } else if (strcmp(argv[i], "-registration_wally") == 0) {
            is_static_wally_enabled  = 1;
        } else if (strcmp(argv[i], "-soft_assert_argo_reg") == 0) {
            /* Hidden command for dev/qa only (ET-31797)
             * When turned on, not allowing argo structure registration
             * after broker started listening for clients.
             * This allows us to catch some tricky initialization sequence
             * bugs, and identify argo structures that pass through
             * broker, but unknown to broker.
             */
            zpn_broker_soft_assert_argo_reg_post_listen(1);
        } else if (strcmp(argv[i], "-no_openssl_err_override") == 0) {
            g_fohh_openssl_err_override = 0;
        } else if (strcmp(argv[i], "-disable_siem_logging") == 0) {
            disable_siem_logging_global = 1;
        } else if (strcmp(argv[i], "-exportable_core_dumps") == 0) {
            zpath_dont_dump_enable_command_line();
        } else if (strcmp(argv[i], "-disable_zpm_conn") == 0) {
            disable_zpm_conn = 1;
        } else {
            /* Test for all two-word arguments. */
            if ((i + 1) >= argc) {
                /* There is not a pair of words... */
                usage(argv[0], "Improper argument- may be missing second field: %s\n", argv[i]);
                /* Exits */
            }
            if (strcmp(argv[i], "-instance") == 0) {
                instance_id = strtoul(argv[i+1], NULL, 0);
                i++;
            } else if (strcmp(argv[i], "-fully_load") == 0) {
                i++;
                fully_load = strtoul(argv[i], NULL, 0);
            } else if (strcmp(argv[i], "-stackpath") == 0) {
                i++;
                stackpath = argv[i];
            } else if (strcmp(argv[i], "-argo_mem_threshold") == 0) {
                i++;
                int val = atoi(argv[i]);
                if (val > 0 && val <= 100)
                    zpath_app_set_specific_argo_mem_threshold(val);
                else
                    usage(argv[0], "Improper value to -argo_mem_threshold");
            } else if (strcmp(argv[i], "-maxlogmb") == 0) {
                i++;
                zpath_app_set_specific_max_logging_mb(atoi(argv[i]));
            } else if (strcmp(argv[i], "-pin_disp_name") == 0) {
                /* for testing, find specific dispatcher by name across both pools, custom pools not included
                * the dispacther must be in the pools
                * use curl 127.0.0.1:8000/dispatcher/pool/dump
                * to determine the which disaptchers are available
                */
                i++;
                pin_test_dispatcher_name((const char* )argv[i]);
            } else if (strcmp(argv[i], "-c_disp") == 0) {
                i++;
                int64_t gid = strtoll(argv[i], NULL, 0);
                if (!ZPATH_GID_GET_SHARD(gid)) {
                    usage(argv[0], "Invalid customer gid = %ld for -c_disp %s\n", (long) gid, argv[i]);
                    /* Exits */
                }
                char *pool_name = strstr(argv[i], ",");
                if(pool_name) {
                    pool_name++;
                    if (!strlen(pool_name)) {
                        usage(argv[0], "Invalid zero length pool name. Try again. -c_disp %s\n", argv[i]);
                        /* exits */
                    }
                    res = zpn_broker_dispatch_pool_add_custom_customer(gid, pool_name);
                    if (res) {
                        usage(argv[0], "Invalid something: %s, when doing -c_disp %s\n", zpn_result_string(res), argv[i]);
                    }
                }
            } else if (strcmp(argv[i], "-local_file") == 0) {
                i++;
                local_file = argv[i];
            } else if (strcmp(argv[i], "-dbhost") == 0) {
                i++;
                dbhost = argv[i];
            } else if (strcmp(argv[i], "-geoip") == 0) {
                i++;
                geoip_db_file = argv[i];
            } else if (strcmp(argv[i], "-geoipisp") == 0) {
                i++;
                geoip_isp_db_file = argv[i];
            } else if (strcmp(argv[i], "-threads") == 0) {
                i++;
                thread_count = atoi(argv[i]);
                if (thread_count <= 0) {
                    usage(argv[0], "Invalid thread count: %s", argv[i]);
                }
            } else if (strcmp(argv[i], "-app_threads_count") == 0) {
                i++;
                app_threads_count = atoi(argv[i]);
                if (app_threads_count <= 0) {
                    usage(argv[0], "Invalid app threads count: %s", argv[i]);
                }
                zpn_broker_app_calc_threads_count_update(app_threads_count);
            } else if (strcmp(argv[i], "-disable_libevent_priority") == 0) {
                zevent_use_libevent_priority = 0;
            } else if (strcmp(argv[i], "-listen_threads") == 0) {
                i++;
                listen_threads = atoi(argv[i]);
                if (listen_threads <= 0) {
                    usage(argv[0], "Invalid listen count: %s", argv[i]);
                }
            } else if (strcmp(argv[i], "-dsp_channels") == 0) {
                i++;
                dsp_channel_count = atoi(argv[i]);
                if (dsp_channel_count < 1 || dsp_channel_count > MAX_CHANNELS_PER_DISPATCHER) {
                    usage(argv[0], "Invalid dispatcher channel count: %s (valid range 1-%d)", argv[i],
                          MAX_CHANNELS_PER_DISPATCHER);
                }
            } else if (strcmp(argv[i], "-fohh_win") == 0) {
                i++;
                fohh_tlv_window_size = atoi(argv[i]);
            } else if (strcmp(argv[i], "-itasca_logs") == 0) {
                i++;
                itasca_logs_port_he = atoi(argv[i]);
            } else if (strcmp(argv[i], "-fohh_mconn_win") == 0) {
                i++;
                fohh_tlv_mconn_window_size = atoi(argv[i]);
            } else if (strcmp(argv[i], "-deferred-init-sec") == 0) {
                i++;
                deferred_init_sec = atoi(argv[i]);
            } else if (strcmp(argv[i], "-printf2") == 0) {
                i++;
                argo_log_use_printf(atoi(argv[i]));
            } else {
                usage(argv[0], "Unrecognized argument: %s\n", argv[i]);
                /* Exits */
            }
        }
    }

    if (geoip_db_file == NULL) {
        //usage(argv[0], "Need Geo IP database file\n");
        fprintf(stdout, "!!!! Need Geo IP database file !!!!\n");
    }

	if (geoip_isp_db_file == NULL) {
        fprintf(stdout, "!!!! Need Geo IP ISP database file !!!!\n");
    }

    if (fohh_tlv_mconn_window_size >= fohh_tlv_window_size) {
        usage(argv[0], "fohh_win should be greater than fohh_mconn_win\n");
    }

    if (config_daemon) {
        int fd;
        fprintf(stderr, "Daemonizing\n");
        /* Take off... */
        switch (fork()) {
		case 0:
			break;
		case -1:
            ZPN_LOG(AL_ERROR, "fork failed: %s", strerror(errno));
            return ZPN_RESULT_ERR;
		default:
			/* exit interactive session */
			exit(0);
        }
        if(setsid() == -1) {
            ZPN_LOG(AL_ERROR, "setsid failed: %s", strerror(errno));
            return ZPN_RESULT_ERR;
        }
        if ((fd = open("/dev/null", O_RDWR, 0)) != -1) {
            (void)dup2(fd, STDIN_FILENO);
            (void)dup2(fd, STDOUT_FILENO);
            (void)dup2(fd, STDERR_FILENO);
            if (fd > 2)
                (void)close(fd);
        }
    }
    zpn_debug_set(ZPN_DEBUG_BROKER_IDX);
    zpn_debug_set(ZPN_DEBUG_LEARN_IDX);
    zpn_debug_set(ZPN_DEBUG_BROKER_ASSISTANT_IDX);
    zpn_debug_set(ZPN_DEBUG_IDP_IDX);
    zpn_debug_set(ZPN_DEBUG_TIMER_IDX);
    zpn_debug_set(ZPN_DEBUG_APPLICATION_GROUP_IDX);
    zpn_debug_set(ZPN_DEBUG_PRIVATE_BROKER_IDX);
    zpn_debug_set(ZPN_DEBUG_MACHINE_IDX);
    zpn_debug_set(ZPN_DEBUG_SVCP_IDX);
    zpn_debug_set(ZPN_DEBUG_NP_IDX);
    zdtls_debug = (
                   //ZDTLS_DEBUG_SETUP_BIT |
                   //ZDTLS_DEBUG_SESSION_BIT |
                   //ZDTLS_DEBUG_BIO_BIT |
                   //ZDTLS_DEBUG_PACKET_BIT |
                   0);

    zrdt_debug = (
                   //ZRDT_DEBUG_BIT |
                   //ZRDT_DEBUG_PACKET_BIT |
                   //ZRDT_DEBUG_STREAM_BIT |
                   //ZRDT_DEBUG_CAPTURE_BIT |
                   //ZRDT_DEBUG_PING_BIT |
                   //ZRDT_DEBUG_CONN_BIT |
                   //ZRDT_DEBUG_STATS_BIT |
                   0);

    if (debuglog) {
        zpath_debug |=
                    (ZPATH_DEBUG_CLOUD_CONFIG_BIT) |
                    0;
    }

    if (itasca_logs_port_he) {
        update_itasca_logs_port(itasca_logs_port_he);
    }

    zpn_machine_fully_load = fully_load ? 1 : 0;

    fohh_set_state_stats_logging(1);
    fohh_set_aggregated_stats_logging(1);
    fohh_set_connection_aggregated_hop_latency_stats(1);

    fohh_set_connection_aggregated_pipeline_latency_stats(1);

    /* Setup termination handlers */
    zpath_lib_termination_handler_init(zpn_broker_lib_exit_cb);
    zthread_termination_handler_init(zpn_broker_heartbeat_exit_cb);

    set_wally_app_state(wally_state_tables_loading);
    zpn_broker_update_bootup_stage_stats(ZPN_BROKER_INITIAL_SETUP_STAGE_0, STAGE_END);

    zpn_broker_update_bootup_stage_stats(ZPN_BROKER_APP_INIT_STAGE_1, STAGE_START);
    /* If we are replicating the global DB, then we pay attention to
     * remote_host configuration for this DB when we initialize the
     * global DB. */
    struct zpath_app_init_params app_params;
    zpath_app_init_params_default(&app_params);
    app_params.instance_id = instance_id;
    app_params.zpath_local_config_file = local_file;
    app_params.local_db_hostname = dbhost;
    app_params.role_name = "zpn_brokerd";
    app_params.fohh_thread_count = thread_count;
    app_params.fohh_watchdog_s = ZPN_MAX_HEARTBEAT_TIMEOUT;
    app_params.personality = app_personality;
    app_params.use_sqlt = use_sqlt;
    app_params.is_endpoint = 0;     // broker is not an endpoint (leaf node of wally distribution)
    app_params.debuglog = debuglog;

    result = zpath_app_init(&app_params);
    if (result) {
        ZPN_LOG(AL_ERROR, "ERROR: Could not intialize\n");
        sleep(1);
        exit(1);
    }
    zpn_broker_update_bootup_stage_stats(ZPN_BROKER_APP_INIT_STAGE_1, STAGE_END);

    result = zpath_debug_add_read_command("Dump broker bootup statistics",
                                        "/broker/bootup_stats",
                                        zpn_broker_dump_bootup_stats,
                                        NULL,
                                        NULL);
    if (result) {
        ZPN_LOG(AL_ERROR, "Could not set up broker bootup statistics");
        return result;
    }

    zpn_broker_update_bootup_stage_stats(ZPN_BROKER_APP_INIT_STAGE_1, STAGE_END);

    zpn_broker_update_bootup_stage_stats(ZPN_BROKER_ZTHREAD_INIT_STAGE_2, STAGE_START);
    g_zpn_broker_zpm_boot_arg_flag = ((!disable_zpm_conn) && (!ot_mode));
    ZPN_LOG(AL_ERROR, "g_zpn_broker_zpm_boot_arg_flag: %d", g_zpn_broker_zpm_boot_arg_flag);

    /* alt-cloud conn snis update cb */
    zpath_instance_set_update_conn_snis_f(zpn_broker_alt_cloud_sni_update);

    result = fohh_set_generic_server_listen_count(listen_threads);
    if (result) {
        ZPN_LOG(AL_ERROR, "ERROR: Could not set listen count\n");
        sleep(1);
        exit(1);
    }


    zthread_init(APP_ROLE, ZPATH_VERSION, ZPATH_LOCAL_FULL_NAME,
                    stackpath, NULL);

    zpn_event_collection = zpath_event_collection;

    ZPN_LOG(AL_NOTICE, "Broker version: %s", ZPATH_VERSION);


    broker_zcdns = zcdns_libevent_create(fohh_get_thread_event_base(0),
                                  1,
                                  NULL,
                                  "/etc/resolv.conf",
                                  "/etc/hosts",
                                  log_f,
                                  NULL);

    zpn_broker_update_bootup_stage_stats(ZPN_BROKER_ZTHREAD_INIT_STAGE_2, STAGE_END);

    zpn_broker_update_bootup_stage_stats(ZPN_BROKER_DB_INIT_STAGE_3, STAGE_START);
    /* For blockd service, we need the following tables:
     *
     * zpath_ip_entity.
     *
     * all shard tables, for shard access.
     */
    ZPN_LOG(AL_NOTICE, "Initializing shard access...");
    result = zpath_app_shard_pre_init();
    if (result) {
        ZPN_LOG(AL_ERROR, "Shard access initialization failed");
        return result;
    }

    result = zpath_app_init_shards(1, use_sqlt);
    if (result != ZPATH_RESULT_NO_ERROR) {
        ZPN_LOG(AL_ERROR, "Could not initialize shards");
        return result;
    }

    result = zpath_app_init_np_shards(use_sqlt, 0/*sync=0*/);
    if (result != ZPATH_RESULT_NO_ERROR) {
        ZPN_LOG(AL_ERROR, "Could not initialize NP shards");
        return result;
    }

    if (is_static_wally_enabled) {
        result = zpath_app_init_shards_static(1, use_sqlt);
        if (result != ZPATH_RESULT_NO_ERROR) {
            ZPN_LOG(AL_ERROR, "Could not initialize shards for static table");
            return result;
        }
    }

    zpn_broker_set_autotune(config_autotune);

    /* Initialize our library before other scim components */
    ZPN_LOG(AL_NOTICE, "Initializing et_wally_userdb library...");
    result = zpath_et_wally_userdb_init(dbhost, NULL, NULL, 1, use_sqlt, 0, 0, 0, NULL, NULL, NULL);
    if (result) {
        ZPN_LOG(AL_ERROR, "Could not initialize wally userdb library: %s", zpn_result_string(result));
        return result;
    }
    zpn_broker_update_bootup_stage_stats(ZPN_BROKER_DB_INIT_STAGE_3, STAGE_END);

    zpn_broker_update_bootup_stage_stats(ZPN_BROKER_PRE_INIT_FEATURES_STAGE_4, STAGE_START);
    broker_misc_event_base = event_base_new();
    if (!broker_misc_event_base) {
        ZPN_LOG(AL_ERROR, "Could not init broker_misc_event_base");
        return -1;
    }

    result = zthread_create(&thread,
                            broker_misc_thread,
                            NULL,
                            "broker_misc_thread",
                            60,
                            16*1024*1024,
                            60*1000*1000,
                            NULL);
    if (result) {
        ZPN_LOG(AL_ERROR, "Could not create broker_misc_thread");
        return result;
    }

    result = object_store_rpc_init();
    if (result) {
        ZPN_LOG(AL_ERROR, "Could not init object_store rpcs: %s", zpn_result_string(result));
        return result;
    }

    result = object_store_init(broker_misc_event_base, ostore_role_object_store);
    if (result) {
        ZPN_LOG(AL_ERROR, "Could not init object_store: %s", zpn_result_string(result));
        return -1;
    }

    result = zcrypto_lib_init_all_svc(broker_misc_event_base);
    if (result) {
        ZPN_LOG(AL_ERROR, "Could not init zcrypto library table: %s", zpn_result_string(result));
        return -1;
    }

    ZPN_LOG(AL_NOTICE, "Initializing zpath_constellation...");
    result = zpath_constellation_init(zpath_global_wally,
                                      zpath_global_slave_db,
                                      zpath_global_remote_db);
    if (result) {
        ZPN_LOG(AL_ERROR, "zpath_constellation failed: %s", zpath_result_string(result));
        return result;
    }

    ZPN_LOG(AL_NOTICE, "Initializing zpath_constellation_instance...");
    result = zpath_constellation_instance_init(zpath_global_wally,
                                               zpath_global_slave_db,
                                               zpath_global_remote_db);
    if (result) {
        ZPN_LOG(AL_ERROR, "zpath_constellation_instance failed: %s", zpath_result_string(result));
        return result;
    }

    ZPN_LOG(AL_NOTICE, "Initializing zpath_customer_to_constellation...");
    result = zpath_customer_to_constellation_init(zpath_global_wally,
                                                  zpath_global_slave_db,
                                                  zpath_global_remote_db);
    if (result) {
        ZPN_LOG(AL_ERROR, "zpath_customer_to_constellation failed: %s", zpath_result_string(result));
        return result;
    }

    zdtls_init(zpn_event_collection);
    zrdt_init(zpn_event_collection, thread_count);
    zrdt_set_cloud_environment(zpn_broker_is_dev_environment());

    result = zpn_zdx_lib_init(zpath_event_collection);
    if (result) {
        ZPN_LOG(AL_CRITICAL, "zdx probe thread initialization failed");
        return result;
    }

    result = zpn_pcap_lib_init(zpath_event_collection);
    if (result) {
        ZPN_LOG(AL_CRITICAL, "zpn pcap lib initialization failed");
        return result;
    }

    result = zpn_pcap_init();
    if (result) {
        ZPN_LOG(AL_CRITICAL, "zpn pcap initialization failed");
        return result;
    }

    ZPN_LOG(AL_NOTICE, "Initializing zpn_brokerd...");

    public_broker.instance_type = ZPN_INSTANCE_TYPE_PUBLIC_BROKER;

    result = zpn_region_restriction_init(zpn_event_collection, zpn_region_restriction_broker_mode);
    if (result) {
        ZPN_LOG(AL_CRITICAL, "Could not init zpn_region_restriction %s", zpath_result_string(result));
        return ZPN_RESULT_ERR;
    }
    zpn_broker_update_bootup_stage_stats(ZPN_BROKER_PRE_INIT_FEATURES_STAGE_4, STAGE_END);

    zpn_broker_update_bootup_stage_stats(ZPN_BROKER_CORE_INIT_STAGE_5, STAGE_START);
    if (deferred_init_sec > 0) {
        broker = zpn_broker_init_standard(thread_count, broker_zcdns, &public_broker, "/zpath/log", dsp_channel_count, itasca_logs_port_he);
    } else {
        broker = zpn_broker_init(thread_count, broker_zcdns, &public_broker, "/zpath/log", dsp_channel_count, itasca_logs_port_he);
    }

    if (!broker) {
        ZPN_LOG(AL_CRITICAL, "broker initialization failed!");
        return ZPN_RESULT_ERR;
    }
    zpn_broker_update_bootup_stage_stats(ZPN_BROKER_CORE_INIT_STAGE_5, STAGE_END);

    zpn_broker_update_bootup_stage_stats(ZPN_BROKER_POST_INIT_FEATURES_STAGE_6, STAGE_START);
    ZPN_DEBUG_STARTUP("Initializing zpn_inspection_config_data...");
    result = zpn_inspection_config_data_init(zpath_global_wally,
                                          zpath_global_slave_db,
                                          zpath_global_remote_db,
                                          1,   // Fetch row synchronous: will start fetching rows before proceeding
                                          1);  // Tenant is a broker
    if (result) {
        ZPN_LOG(AL_ERROR, "zpn_inspection_config_data... failed: %s", zpath_result_string(result));
        return result;
    }

    ZPN_DEBUG_STARTUP("Initializing zpn_inspection_zsdefined_control...");
    result = zpn_inspection_zsdefined_control_init(zpath_global_wally,
                                          zpath_global_slave_db,
                                          zpath_global_remote_db,
                                          1,   // Fetch row synchronous: will start fetching rows before proceeding
                                          1);  // Tenant is a broker
    if (result) {
        ZPN_LOG(AL_ERROR, "zpn_inspection_zsdefined_control... failed: %s", zpath_result_string(result));
        return result;
    }

    /* public broker WAF inspection tables init */
    result = zpn_inspection_application_init(0, 0, 0, 0, NULL);
    if (result) {
        ZPN_LOG(AL_ERROR, "Could not init zpn_inspection_application table");
        return result;
    }

    result = zpn_public_certificate_init(0, 0, 0, 0);
    if (result) {
        ZPN_LOG(AL_ERROR, "Could not init zpn_public_cert table");
        return result;
    }

    result = zpn_client_setting_init(0, 0, 0, 0);
    if (result) {
        ZPN_LOG(AL_ERROR, "Could not init zpn_client_setting table");
        return result;
    }

    result = zpn_inspection_profile_init(0, 0, 0 /* no fully load */, 0);
    if (result) {
        ZPN_LOG(AL_ERROR, "Could not init zpn_inspection_profile table");
        return result;
    }

    result = zpn_inspection_profile_to_control_init(0, 0, 0 /* no fully load */, 0);
    if (result) {
        ZPN_LOG(AL_ERROR, "Could not init zpn_inspection_profile_to_control table");
        return result;
    }

    result = zpn_inspection_prof_to_zsdefined_ctrl_init(0, 0, 0, 0);
    if (result) {
        ZPN_LOG(AL_ERROR, "Could not init zpn_inspection_prof_to_zsdefined_ctrl table");
        return result;
    }

    result = zpn_broker_maintenance_init();
    if (result) {
        ZPN_LOG(AL_CRITICAL, "broker maintenance initializaiton failed %s", zpath_result_string(result));
        return result;
    }

    result = zpn_zdx_features_init(zpn_zdx_system_type_broker, &zpn_broker_load_get_system_mem_usage, ZPN_BROKER_GET_GID(), ZPN_BROKER_GET_GROUP_GID(), config_component_broker);
    if (result) {
        ZPN_LOG(AL_CRITICAL, "zdx probe feature init failed");
        return result;
    }

    result = zpn_zdx_rate_limit_init(zpn_zdx_system_type_broker, ZPN_BROKER_GET_GID(), ZPN_BROKER_GET_GROUP_GID(), config_component_broker);
    if (result) {
        ZPN_LOG(AL_CRITICAL, "zdx rate limit init failed");
        return result;
    }

    //TODO(blewis) should these inits be part of 'zdx init'
    result = zhealth_probe_lib_thread_init(NULL);
    if (result) {
        ZPN_LOG(AL_CRITICAL, "zdx probe lib thread initialization failed");
        return result;
    }

    result = zhealth_probe_lib_init(NULL, zpath_event_collection, zpn_zdx_zhealth_probe_lib_config_get_socket_engine_cb, zhealth_system_type_broker, ZPN_BROKER_GET_GID(), ZPN_BROKER_GET_GROUP_GID(), 0);
    if (result) {
        ZPN_LOG(AL_CRITICAL, "zdx probe lib initialization failed");
        return result;
    }

    result = zhealth_probe_udp_init(0, zhealth_system_type_broker);
    if (result) {
        ZPN_LOG(AL_CRITICAL, "zdx probe udp initialization failed");
        return result;
    }

    result = zpn_zdx_mtr_init(NULL, zpath_event_collection);
    if (result) {
        ZPN_LOG(AL_CRITICAL, "zdx mtr init failed");
        return result;
    }

    result = zpn_zdx_probes_init(zpn_broker_probe_combiner_send_cb, NULL);
    if (result) {
        ZPN_LOG(AL_ERROR, "Unable to init zpn_zdx_probes");
        return result;
    }

    res = zpn_zdx_webprobe_lib_init(zpath_event_collection);
    if (res) {
        ZPN_LOG(AL_ERROR, "Unable to init the webprobe rate limit lib");
        return res;
    }

    res = zpn_zdx_webprobe_rate_limit_init();
    if (res) {
        ZPN_LOG(AL_ERROR, "Unable to init the webprobe rate limit");
        return res;
    }

    res = zpn_command_probe_init(NULL, 0, 0 /* no fully load */, 0);
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not init zpn_command_probe");
        return res;
    }

    res = admin_probe_rpc_init();
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not init admin_probe_rpc");
        return res;
    }

    result = zpath_debug_add_flag_ext(zpn_debug_cnxt(),
                                      zpn_debug_catch_cnxt(),
                                      zpn_debug_cnxt_cnt(),
                                      "zpn",
                                      zpn_debug_names);
    if (result) {
        ZPN_LOG(AL_ERROR, "zpath_debug_add_flag failed: %s",
                zpath_result_string(result));
        return result;
    }

    result = zpath_debug_add_flag(&zrdt_debug,
                                  zrdt_debug_catch_defaults,
                                  "zrdt",
                                  zrdt_debug_names);
    if (result) {
        ZPN_LOG(AL_ERROR, "zpath_debug_add_flag failed: %s",
                zpath_result_string(result));
        return result;
    }

    result = zpath_debug_add_flag(&zdtls_debug,
                                  zdtls_debug_catch_defaults,
                                  "zdtls",
                                  zdtls_debug_names);
    if (result) {
        ZPN_LOG(AL_ERROR, "zpath_debug_add_flag failed: %s",
                zpath_result_string(result));
        return result;
    }

    result = zpath_debug_add_flag(&zhealth_debug, zhealth_debug_catch, "zhealth", zhealth_debug_names);
    if (result) {
        ZPN_LOG(AL_ERROR, "Could not initialize zhealth debug flags: %s", zpath_result_string(result));
            return result;
    }

    result = zpath_debug_add_flag(&argo_debug,
                                  argo_debug_catch_defaults,
                                  "argo",
                                  argo_debug_names);
    if (result) {
        ZPN_LOG(AL_ERROR, "zpath_debug_add_flag failed: %s", zpath_result_string(result));
        return result;
    }

    result = zpn_event_init(zpn_broker_is_dev_environment(), 0);
    if (result) {
        ZPN_LOG(AL_ERROR, "zpn_event_init failed: %s", zpath_result_string(result));
        return result;
    }
    result = zpn_event_broker_override_desc_init();
    if (result) {
        ZPN_LOG(AL_ERROR,"Could not register zpn_event overrides: %s", zpath_result_string(result));
        return result;
    }

    result = zpn_event_broker_stats_init();
    if (result) {
        ZPN_LOG(AL_ERROR, "zpn_event_broker_stats_init failed: %s", zpath_result_string(result));
        return result;
    }

    ZPN_LOG(AL_NOTICE, "SSL memory allocator %s", is_openssl_allocator() ? "set" : "not set");
    ZPN_LOG(AL_NOTICE, "Initialization Complete");

    if (deferred_init_sec > 0) {
        ZPN_LOG(AL_NOTICE, "Executing deferred initialization in %d seconds.", deferred_init_sec);
        sleep(deferred_init_sec);
        result = zpn_broker_init_deferred();
        if (result) {
            ZPN_LOG(AL_ERROR, "Deferred initialization failed: %s", zpath_result_string(result));
            return result;
        }
        ZPN_LOG(AL_NOTICE, "Deferred Initialization Complete");
    }

    result = zpath_debug_mem_stats_init(zpath_service_broker);
    if(result) {
        ZPN_LOG(AL_ERROR, "Initializing memory allocator stats failed: %s", zpath_result_string(result));
        return result;
    }

    result = argo_mem_stats_init(zpath_service_broker);
    if(result) {
        ZPN_LOG(AL_ERROR, "Initializing argo memory allocator stats failed: %s", zpath_result_string(result));
        return result;
    }
    zpn_broker_update_bootup_stage_stats(ZPN_BROKER_POST_INIT_FEATURES_STAGE_6, STAGE_END);

    zpn_broker_update_bootup_stage_stats(ZPN_BROKER_NETWORK_INIT_STAGE_7, STAGE_START);
    result = zpn_broker_assistant_listen_socket();
    if (result) {
        ZPN_LOG(AL_ERROR, "Could not listen for assistants: %s", zpath_result_string(result));
        return result;
    }

    /* Validate all broker tables are registered with sync pause infrastructure */
    zpath_sync_pause_validate_broker_tables(zpath_global_wally);

    if (deferred_init_sec > 0) {
        result = zpn_broker_client_listen_socket(0);
    } else {
       result = zpn_broker_client_listen_socket(1);
    }
    if (result) {
        ZPN_LOG(AL_ERROR, "Could not listen for clients: %s", zpath_result_string(result));
        return result;
    }

    result = zpn_broker_socket_listen_client_init();
    if (result) {
        ZPN_LOG(AL_ERROR, "Could not attach ip to 443/8443. Unable to init server listen for client.");
        return result;
    }

    /* Initialize customer connection drain infra and callbacks */
    ZPN_LOG(AL_NOTICE, "drain_conn: Initializing customer connection drain infra...");
    result = zpn_broker_drain_init();
    if (result) {
        ZPN_LOG(AL_ERROR, "drain_conn: Could not init broker drain infra: %s", zpn_result_string(result));
        return result;
    }
    set_wally_app_state(wally_state_tables_loaded);

    zpath_registration_completed();
    zpn_broker_load_notify_status_change(RUNNING);
    set_wally_app_state(wally_state_app_running);
    zpn_broker_update_bootup_stage_stats(ZPN_BROKER_NETWORK_INIT_STAGE_7, STAGE_END);

    while(1) sleep(1);

    return 0;
}


int main(int argc, char *argv[]) {
    int result;
    result = sub_main(argc, argv);
    sleep(1);
    return result;
}
