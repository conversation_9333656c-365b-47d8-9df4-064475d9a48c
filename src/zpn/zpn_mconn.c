/*
 * zpn_mconn.c. Copyright (C) 2014 Zscaler Inc. All Rights Reserved
 */


#include "zpn/zpn_lib.h"
#include "zpn/zpn_mconn.h"
#include "zpn/zpn_mconn_udp_util.h"
#include "zpn/zpn_mconn_icmp_util.h"


/*
 * Send data to client...
 *
 * Returns:
 *
 * ZPN_RESULT_SUCCESS: The data has been transmitted. (In socket
 *   buffers, whatever)
 *
 * ZPN_RESULT_ASYNCHRONOUS: The data has been buffered, but the
 *   transmitter better stop transmitting to us, lest there be crazy
 *   buffer bloat.
 *
 * ZPN_RESULT_*: Some error has occurred, and communication with the
 *   client will probably never recover.
 *
 * LOCKING: None required, HOWEVER, it is up to the producer of this
 *   data to ensure it arrives at these calls in order.
 */

int zpn_mconn_init(struct zpn_mconn *mconn, void *self, enum zpn_mconn_type type)
{
    mconn->self = self;
    mconn->to_client_paused = 0;
    mconn->to_client_paused_count = 0;
    mconn->from_client_paused_count = 0;
    mconn->to_client_resume_count = 0;
    mconn->from_client_resume_count = 0;
    mconn->to_client_pause_time_max_us = 0;
    mconn->to_client_pause_time_max_epoch_us = 0;
    mconn->to_client_pause_time_total_us = 0;
    mconn->to_client_pause_timed_out_count = 0;
    mconn->bytes_to_client = 0;
    mconn->bytes_to_peer = 0;
    mconn->bytes_to_peer_attempt = 0;
    mconn->icmp_time_exceeded_drops = 0;
    mconn->icmp_pkt_frag_drops = 0;
    mconn->icmp_malformed_pkt_drops = 0;
    mconn->icmp_zero_len_pkt_drops = 0;
    mconn->icmp_internal_err_drops = 0;
    mconn->icmp_access_err_drops = 0;
    mconn->icmp_timeout_failure_drops = 0;
    mconn->icmp_rate_limit_exceeded_err_drops = 0;
    mconn->icmp_tx_packets = 0;
    mconn->icmp_rx_packets = 0;
    mconn->icmpv6_time_exceeded_drops = 0;
    mconn->icmpv6_malformed_pkt_drops = 0;
    mconn->icmpv6_internal_err_drops = 0;
    mconn->icmpv6_access_err_drops = 0;
    mconn->icmpv6_timeout_failure_drops = 0;
    mconn->icmpv6_rate_limit_exceeded_err_drops = 0;
    mconn->icmpv6_tx_packets = 0;
    mconn->icmpv6_rx_packets = 0;
    mconn->type = type;
    return ZPN_RESULT_NO_ERROR;
}

int zpn_mconn_add_global_owner(struct zpn_mconn *mconn,
                               int is_owner_lock_held,
                               void *global_owner,
                               void *global_owner_key,
                               size_t global_owner_key_length,
                               const struct zpn_mconn_global_owner_calls *calls)
{
    int ret = ZPN_RESULT_NO_ERROR;

    if (!mconn) {
        ZPN_LOG(AL_CRITICAL, "No mconn");
        return ZPN_RESULT_ERR;
    }

    mconn->global_owner_calls = calls;

    if (!is_owner_lock_held) {
        (mconn->global_owner_calls->lock)(mconn,
                                          mconn->self,
                                          global_owner,
                                          global_owner_key,
                                          global_owner_key_length);
    }

    if (mconn->global_owner) {
        ZPN_LOG(AL_CRITICAL, "Attempting to add another global owner");
        ret = ZPN_RESULT_ERR;
    } else {
        ret = (mconn->global_owner_calls->bind)(mconn,
                                                mconn->self,
                                                global_owner,
                                                global_owner_key,
                                                global_owner_key_length,
                                                &(mconn->global_owner_incarnation));
        if (ret == ZPN_RESULT_NO_ERROR) {
            mconn->global_owner = global_owner;
            mconn->global_owner_key = global_owner_key;
            mconn->global_owner_key_length = global_owner_key_length;
        } else {
            ZPN_LOG(AL_CRITICAL, "Could not bind global owner?");
        }
    }

    if (!is_owner_lock_held) {
        (mconn->global_owner_calls->unlock)(mconn,
                                            mconn->self,
                                            global_owner,
                                            global_owner_key,
                                            global_owner_key_length);
    }

    return ret;
}



static int zpn_mconn_remove_global_owner(struct zpn_mconn *mconn,
                                         int is_owner_lock_held,
                                         const char *err)
{
    int ret = ZPN_RESULT_NO_ERROR;

    void *global_owner;

    if (!mconn) {
        ZPN_LOG(AL_CRITICAL, "No mconn");
        return ZPN_RESULT_ERR;
    }

    if (!(global_owner = mconn->global_owner)) {
        /* Normal- basically a verification of removal... */
        return ZPN_RESULT_NO_ERROR;
    }

    if (!is_owner_lock_held) {
        (mconn->global_owner_calls->lock)(mconn,
                                          mconn->self,
                                          global_owner,
                                          mconn->global_owner_key,
                                          mconn->global_owner_key_length);
    }

    if (!mconn->global_owner) {
        /* Small race condition- someone removed global_owner while we
         * were locking... */
    } else {
        global_owner = mconn->global_owner;
        ret = (mconn->global_owner_calls->unbind)(mconn,
                                                  mconn->self,
                                                  mconn->global_owner,
                                                  mconn->global_owner_key,
                                                  mconn->global_owner_key_length,
                                                  mconn->global_owner_incarnation,
                                                  0,
                                                  0,
                                                  err);
        if (ret == ZPN_RESULT_NO_ERROR) {
            mconn->global_owner = NULL;
            /* We leave key/length/incarnation intact for unlocking,
             * below. */
        }
    }

    if (!is_owner_lock_held) {
        (mconn->global_owner_calls->unlock)(mconn,
                                            mconn->self,
                                            global_owner,
                                            mconn->global_owner_key,
                                            mconn->global_owner_key_length);
    }

    return ret;
}



int zpn_mconn_add_local_owner(struct zpn_mconn *mconn,
                              int is_owner_lock_held,
                              void *local_owner,
                              void *local_owner_key,
                              size_t local_owner_key_length,
                              const struct zpn_mconn_local_owner_calls *calls)
{
    int ret = ZPN_RESULT_NO_ERROR;

    if (!mconn) {
        ZPN_LOG(AL_CRITICAL, "No mconn");
        return ZPN_RESULT_ERR;
    }

    mconn->local_owner_calls = calls;

    if (!is_owner_lock_held) {
        (mconn->local_owner_calls->lock)(mconn,
                                         mconn->self,
                                         local_owner,
                                         local_owner_key,
                                         local_owner_key_length);
    }

    if (mconn->local_owner) {
        ZPN_LOG(AL_CRITICAL, "Attempting to add another local owner");
        ret = ZPN_RESULT_ERR;
    } else {
        ret = (mconn->local_owner_calls->bind)(mconn,
                                               mconn->self,
                                               local_owner,
                                               local_owner_key,
                                               local_owner_key_length,
                                               &(mconn->local_owner_incarnation));
        if (ret == ZPN_RESULT_NO_ERROR) {
            mconn->local_owner = local_owner;
            mconn->local_owner_key = local_owner_key;
            mconn->local_owner_key_length = local_owner_key_length;
        } else {
            if (!is_owner_lock_held) {
                (mconn->local_owner_calls->unlock)(mconn,
                                                   mconn->self,
                                                   local_owner,
                                                   local_owner_key,
                                                   local_owner_key_length);
            }

            return ret;
        }
    }

    if (!is_owner_lock_held) {
        (mconn->local_owner_calls->unlock)(mconn,
                                           mconn->self,
                                           local_owner,
                                           local_owner_key,
                                           local_owner_key_length);
    }

    if (zpn_mconn_transmit_buffer_exists(mconn)) {
        struct zpn_mconn *peer;

        peer = mconn->peer;

        ret = zpn_client_drain_tx_data(mconn);
        if (ret == ZPN_RESULT_ASYNCHRONOUS) {
            if (peer) {
                ret = zpn_mconn_pause_client(peer, 1);
                if (ret == ZPN_RESULT_NO_ERROR) {
                    /* All is well... */
                } else if (ret == ZPN_RESULT_WOULD_BLOCK) {
                    /* Full block... */
                    ret = ZPN_RESULT_NO_ERROR;
                } else {
                    /* Some error... */
                    ZPN_LOG(AL_ERROR, "Could not pause client: %s", zpn_result_string(ret));
                }
            }
        } else if (ret != ZPN_RESULT_NO_ERROR) {
            ZPN_LOG(AL_ERROR, "Tx to client returned %s", zpn_result_string(ret));
        } else {
            /* NO_ERROR */
        }
    }

#if 0
    if (!is_owner_lock_held) {
        (mconn->local_owner_calls->unlock)(mconn,
                                           mconn->self,
                                           local_owner,
                                           local_owner_key,
                                           local_owner_key_length);
    }
#endif

    return ret;
}

int zpn_mconn_remove_local_owner(struct zpn_mconn *mconn,
                                 int is_owner_lock_held,
                                 int drop_buffered_data,
                                 int dont_propagate_unbind,
                                 const char *err)
{
    int ret = ZPN_RESULT_NO_ERROR;
    void *local_owner;

    if (!mconn) {
        ZPN_LOG(AL_CRITICAL, "No mconn");
        return ZPN_RESULT_ERR;
    }

    if (!(local_owner = mconn->local_owner)) {
        /* Normal- basically verification of removal. */
        ZPN_DEBUG_MCONN("%p: Already disconnected from local owner", mconn);
        return ZPN_RESULT_NO_ERROR;
    } else {
        ZPN_DEBUG_MCONN("%p: Disconnecting from local owner", mconn);
    }

    if (!mconn->local_owner) {
        /* Small race condition- local_owner may have gone away while
         * we grabbed lock. That's fine. */
    } else {
        local_owner = mconn->local_owner;
        if (zpn_mconn_get_transmit_buffer_len(mconn) &&
            !drop_buffered_data) {
            /* We have queued data, but we want to dequeue it before
             * destroying this mconn. */
            mconn->client_needs_to_disconnect_local_owner = 1;
        } else {

            if (!is_owner_lock_held) {
                (mconn->local_owner_calls->lock)(mconn,
                                                 mconn->self,
                                                 local_owner,
                                                 NULL,
                                                 0);
            }

            ret = (mconn->local_owner_calls->unbind)(mconn,
                                                     mconn->self,
                                                     local_owner,
                                                     mconn->local_owner_key,
                                                     mconn->local_owner_key_length,
                                                     mconn->local_owner_incarnation,
                                                     drop_buffered_data,
                                                     dont_propagate_unbind,
                                                     err);
            if (ret == ZPN_RESULT_NO_ERROR) {
                mconn->local_owner = NULL;
                /* Leave key/length/incarn intact for lock manipulation */
            }

            if (!is_owner_lock_held) {
                (mconn->local_owner_calls->unlock)(mconn,
                                                   mconn->self,
                                                   local_owner,
                                                   NULL,
                                                   0);
            }
        }
    }

    return ret;
}

/*
 * Locking: no need to lock since it is only called during initialization
 */
int zpn_mconn_connect_peer(struct zpn_mconn *mconn, struct zpn_mconn *peer_mconn)
{
    ZPN_DEBUG_MCONN("Linked mconns as peers: %p <--> %p", mconn, peer_mconn);

    /* Remember, it is the OWNER that is locking the mconn's state-
     * even for peers. (peers own each other) */
    mconn->peer = peer_mconn;
    peer_mconn->peer = mconn;

    /* Now need to copy client_rx_data to client transmit buffer if we have any */
    if (mconn->client_rx_data) {
        ZPN_DEBUG_MCONN("%p has client_rx_data: %p", mconn, mconn->client_rx_data);

        mconn->bytes_to_peer += evbuffer_get_length(mconn->client_rx_data);
        mconn->bytes_to_peer_attempt += evbuffer_get_length(mconn->client_rx_data);

        if (!zpn_mconn_transmit_buffer_exists(peer_mconn)) {
            zpn_mconn_create_transmit_buffer(peer_mconn);
            if (peer_mconn->setup_pipeline_cb) {
                peer_mconn->setup_pipeline_cb(peer_mconn, peer_mconn->pipeline_cookie);
            }
        }

        if (zpn_mconn_transmit_buffer_exists(peer_mconn)) {
            zpn_mconn_add_to_transmit_buffer(peer_mconn, mconn->client_rx_data, evbuffer_get_length(mconn->client_rx_data));
        }

        if (mconn->rx_fin_pending) {
            ZPN_DEBUG_MCONN("%p 1, Setting peer mconn %p to forward mtunnel end, bytes %ld", mconn, peer_mconn, (long)evbuffer_get_length(mconn->client_rx_data));
            peer_mconn->client_needs_to_forward = 1;
        }

        evbuffer_free(mconn->client_rx_data);
        mconn->client_rx_data = NULL;
    }

    if (peer_mconn->client_rx_data) {
        ZPN_DEBUG_MCONN("%p has client_rx_data: %p", peer_mconn, peer_mconn->client_rx_data);

        peer_mconn->bytes_to_peer += evbuffer_get_length(peer_mconn->client_rx_data);
        peer_mconn->bytes_to_peer_attempt += evbuffer_get_length(peer_mconn->client_rx_data);

        if (!zpn_mconn_transmit_buffer_exists(mconn)) {
            zpn_mconn_create_transmit_buffer(mconn);
            if (mconn->setup_pipeline_cb) {
                mconn->setup_pipeline_cb(mconn, mconn->pipeline_cookie);
            }
        }

        if (zpn_mconn_transmit_buffer_exists(mconn)) {
            zpn_mconn_add_to_transmit_buffer(mconn, peer_mconn->client_rx_data, evbuffer_get_length(peer_mconn->client_rx_data));
        }

        if (peer_mconn->rx_fin_pending) {
            ZPN_DEBUG_MCONN("%p 2, Setting peer mconn %p to forward mtunnel end, bytes %ld", peer_mconn, mconn, (long)evbuffer_get_length(peer_mconn->client_rx_data));
            mconn->client_needs_to_forward = 1;
        }

        evbuffer_free(peer_mconn->client_rx_data);
        peer_mconn->client_rx_data = NULL;
    }

    /*
     * icmp_access_type is first assigned in mtunnel_locked_policy_check() for client mconn.
     * the peer mconn has to get such info as well for a mtunnel request.
     */
    if ((mconn->type == mconn_fohh_tlv_c) || mconn->type == mconn_zrdt_tlv_c) {
        mconn->peer->icmp_access_type = mconn->icmp_access_type;
    }

    return ZPN_RESULT_NO_ERROR;
}

/*
 * Can only be called when no longer connected to peer.
 *
 * Locking: same lock as zpn_mconn_terminate() since we are called from that.
 */
static int zpn_mconn_terminate_solo(struct zpn_mconn *mconn, int drop_buffered_data, int dont_propagate_unbind, const char *err)
{
    int ret = ZPN_RESULT_NO_ERROR;
    int res;

    res = zpn_mconn_remove_local_owner(mconn, 0, drop_buffered_data, dont_propagate_unbind, err);
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not remove local owner: %s", zpn_result_string(res));
        ret = res;
    }

    if (mconn->local_owner) {
        /* The above was supposed to remove local owner... */
        ZPN_LOG(AL_ERROR, "Mconn should have lost local owner");
    } else {
        res = zpn_mconn_remove_global_owner(mconn, 1, err);
        if (res) {
            ZPN_LOG(AL_ERROR, "Could not remove global owner: %s", zpn_result_string(res));
            ret = res;
        }
    }

    if ((mconn->global_owner == NULL) &&
        (mconn->local_owner == NULL) &&
        (!zpn_mconn_transmit_buffer_exists(mconn))) {
        /* Really there isn't anything to free- it's up to the global
         * owner to recognize everything is gone. */
        //ZPN_LOG(AL_CRITICAL, "Implement me: Slow free mconn");
    }

    mconn->terminated = 1;
    if (zpn_mconn_transmit_buffer_exists(mconn)) {
        zpn_mconn_free_transmit_buffer(mconn);
    }

    return ret;
}

static void zpn_mconn_terminate_drop_mconn_data(struct zpn_mconn *mconn, int64_t * uncounted_dropped_bytes)
{
    if (mconn) {
        zpn_mconn_discard_client_data(mconn, uncounted_dropped_bytes);
        mconn->fin_rcvd = 1;
        if (!mconn->fin_rcvd_us) mconn->fin_rcvd_us = epoch_us();
    }
}

/*
 * We always propagate unbinding to our peer. But sometimes we don't
 * propagate it back the way it came.
 *
 * Locking: It is assumed we have the lock for mtunnel to which this mconn belongs
 */
int zpn_mconn_terminate(struct zpn_mconn *mconn,
                        int drop_buffered_data,
                        int dont_propagate_unbind,
                        const char *reason,
                        int64_t *uncounted_dropped_bytes)
{
    struct zpn_mconn *peer = NULL;

    int ret = ZPN_RESULT_NO_ERROR;
    int res;

    ZPN_DEBUG_MCONN("Terminate mconn %p, type = %s, drop data = %d", mconn, zpn_mconn_type_str(mconn->type), drop_buffered_data);

    if (drop_buffered_data) {
        if (mconn) {
            peer = mconn->peer;
            if (peer) {
                zpn_mconn_terminate_drop_mconn_data(peer, NULL);
            }

            zpn_mconn_terminate_drop_mconn_data(mconn, uncounted_dropped_bytes);
        }
    }

    if (mconn) {
        peer = mconn->peer;
        if (peer) {
            peer->peer = NULL;
            mconn->peer = NULL;

            ret = zpn_mconn_terminate_solo(peer, drop_buffered_data, 0, reason);
            if (ret) {
                ZPN_LOG(AL_ERROR, "terminate_peer_solo returned %s", zpn_result_string(ret));
            }
        } else {
            zpn_mconn_stats_update(mconn, mconn_terminate_no_peer);
        }

        res = zpn_mconn_terminate_solo(mconn, drop_buffered_data, dont_propagate_unbind, reason);
        if (res) {
            ret = res;
            ZPN_LOG(AL_ERROR, "terminate_mconn_solo returned %s", zpn_result_string(ret));
        }
    } else {
        ZPN_LOG(AL_ERROR, "NULL mconn");
        ret = ZPN_RESULT_ERR;
    }
    return ret;
}

/*
 * This function dequeues data onto an evbuffer managed by mconn and send the data out through the client
 * of this mconn.
 *
 * Locking: it is assumed we have the lock of mtunnel to which mconn belongs
 */
static int zpn_mconn_dequeue_data(struct zpn_mconn *mconn, int need_to_lock)
{
    int ret = ZPN_RESULT_NO_ERROR;
    int tx_len;

    ZPN_DEBUG_MCONN("%p: Dequeue data and send to client, client transmit buffer: %p", mconn, zpn_mconn_get_transmit_buffer(mconn));

    if (mconn->to_client_paused) {
        int64_t pause_elapsed_time_us = epoch_us() - mconn->pause_start_us;
        if (pause_elapsed_time_us < ZPN_MCONN_PAUSE_TIMEOUT) {
            ZPN_DEBUG_MCONN("%p: Paused", mconn);
            /* We are blocked by the client, bail */
            return ZPN_RESULT_NO_ERROR;
        } else {
            /* Pause has timed out, so we resume transmit */
            mconn->to_client_paused = 0;
            mconn->from_client_pause_time_total_us += pause_elapsed_time_us;
            if (mconn->from_client_pause_time_max_us < pause_elapsed_time_us) {
                mconn->from_client_pause_time_max_us = pause_elapsed_time_us;
                mconn->from_client_pause_time_max_epoch_us = epoch_us();
            }
            mconn->to_client_pause_timed_out_count++;
        }
    }

    if (!zpn_mconn_transmit_buffer_exists(mconn)) {
        /* Nothing to send */
        ZPN_DEBUG_MCONN("%p: dequeue, no client transmit buffer", mconn);
        return ZPN_RESULT_NO_ERROR;
    }

    if (!mconn->local_owner) {
        /* Cannot send */
        ZPN_DEBUG_MCONN("%p: dequeue, no local owner", mconn);
        return ZPN_RESULT_NO_ERROR;
    }

    tx_len = zpn_mconn_get_transmit_buffer_len(mconn);
    if (tx_len == 0) {
        ZPN_DEBUG_MCONN("%p: nothing to transmit, returning", mconn);
        return ZPN_RESULT_NO_ERROR;
    }

    ret = (mconn->local_owner_calls->transmit)(mconn,
                                               mconn->self,
                                               mconn->local_owner,
                                               mconn->local_owner_key,
                                               mconn->local_owner_key_length,
                                               mconn->local_owner_incarnation,
                                               mconn->fohh_thread_id,
                                               zpn_mconn_get_transmit_buffer(mconn),
                                               tx_len);
    if (ret == ZPN_RESULT_NO_ERROR) {
        if (!mconn->tx_data_us_b) {
            mconn->tx_data_us_b = epoch_us();
        }
        mconn->tx_data_us = epoch_us();
        /* Do nothing */
    } else if (ret == ZPN_RESULT_WOULD_BLOCK) {
        ZPN_DEBUG_MCONN("%s: %p: Blocked", __func__, mconn);
        ret = ZPN_RESULT_NO_ERROR;
    } else {
        ZPN_LOG(AL_ERROR, "%s: %p: Failed to send err = %s", __func__, mconn, zpn_result_string(ret));
        /* Let higher layer terminate on recognition of error. */
    }

    if (!zpn_mconn_get_transmit_buffer_len(mconn)) {
        zpn_mconn_free_transmit_buffer(mconn);
    }

    return ret;
}

/*
 * Send data to client of this mconn. If buf is NULL, it means we just drain what's on client transmit buffer
 *
 * Here mconn is the egress mconn of the data flow.
 *
 * Locking: It is assumed we have the lock to mtunnel to which this mconn belongs
 */
int zpn_mconn_send_data_to_client(struct zpn_mconn *mconn, struct evbuffer *buf, size_t buf_len, int need_to_lock, int64_t *droplen)
{
    int res = ZPN_RESULT_NO_ERROR;
    int res2 = ZPN_RESULT_NO_ERROR;
    size_t enq_len = 0;
    ssize_t drop_len = 0;

    ZPN_DEBUG_MCONN("%p (%s): zpn_mconn_send_data_to_client - Queueing %ld bytes to be sent, client transmit buffer: %p",
                    mconn, zpn_mconn_type_str(mconn->type), (long)buf_len, zpn_mconn_get_transmit_buffer(mconn));

#if 0  /* drop the data at the egress */
    /* This should not happen, but in case the impossible happened, we just need to drop the data */
    if ((buf_len > 0) && mconn->fin_sent) {
        ZPN_LOG(AL_NOTICE, "Already sent fin, so drop the data");
        return ZPN_RESULT_NO_ERROR;
    }
#endif

    /* Make sure we have mconn->client transmit buffer to queue the data to */
    if (!zpn_mconn_transmit_buffer_exists(mconn)) {
        if (!zpn_mconn_create_transmit_buffer(mconn)) {
            ZPN_LOG(AL_ERROR, "%p: Could not allocate client tx data", mconn);
            return ZPN_RESULT_NO_MEMORY;
        }
        if (mconn->setup_pipeline_cb) {
             mconn->setup_pipeline_cb(mconn, mconn->pipeline_cookie);
        }
        ZPN_DEBUG_MCONN("%p: Allocated client transmit buffer: %p", mconn, zpn_mconn_get_transmit_buffer(mconn));
    }

    if (mconn->drop_udp_framed_data) {
        int framing_error = 0;
        enq_len = buf_len;
        drop_len = udp_evbuffer_append_or_drop(&(mconn->udp_state), zpn_mconn_get_transmit_buffer(mconn), buf, ZPN_MCONN_MAX_CLIENT_TX_DATA, &framing_error);
        if (drop_len)
          ZPN_DEBUG_MCONN("mconn: %p; UDP drop bytes: %zd; udp_buf_len after drop: %zu; sport: %u; dport: %u; len_from_header: %u; tx_buf_len: %zu",
                          mconn, drop_len, evbuffer_get_length(buf),
                          ntohs(mconn->udp_state.header.shorts[0]),
                          ntohs(mconn->udp_state.header.shorts[1]),
                          ntohs(mconn->udp_state.header.shorts[2]),
                          zpn_mconn_get_transmit_buffer_len(mconn));
        if (droplen)
          *droplen = (int64_t)drop_len;
        enq_len -= drop_len;
        mconn->bytes_dropped_udp += drop_len;

        if (mconn->peer) {
          mconn->peer->bytes_to_peer += enq_len;
          if (drop_len)
            zpn_mconn_client_window_update(mconn->peer, 0, (int)drop_len, 0);
        }
        mconn->bytes_from_peer += enq_len;

        /* UDP NEVER BLOCKS! */
        if (framing_error) {
            res = ZPN_RESULT_BAD_DATA;
        } else {
            res = ZPN_RESULT_NO_ERROR;
        }

        ZPN_DEBUG_MCONN("%p: drop_udp_framed_data: peer: %p, drop_len: %ld, enq_len: %ld",
                        mconn, mconn->peer, (long)drop_len, (long)enq_len);
    } else {


        /* Now try to queue the data on to client transmit buffer */
        enq_len = zpn_mconn_add_to_transmit_buffer(mconn, buf, buf_len);
        size_t qlen = evbuffer_get_length(buf);
        ZPN_DEBUG_MCONN("%p: zpn_mconn_send_data_to_client - (after remove) %ld bytes to be sent, client transmit buffer: %p, enq_len: %ld transmit buffer len=%lu",
                        mconn, (long) qlen, zpn_mconn_get_transmit_buffer(mconn), (long)enq_len, zpn_mconn_get_transmit_buffer_len(mconn));

        if (mconn->peer) mconn->peer->bytes_to_peer += enq_len;
        mconn->bytes_from_peer += enq_len;
        zpn_mconn_tx_buf_len_stats(mconn, qlen);

        if (enq_len != buf_len) {
            /* We are blocked */
            ZPN_LOG(AL_ERROR, "%p: Could not add buffer ... buf has %ld, buf_len %ld",
                    mconn,
                    (long) evbuffer_get_length(buf),
                    (long) buf_len);
            res = ZPN_RESULT_ASYNCHRONOUS;
        } else if (zpn_mconn_get_transmit_buffer_len(mconn) > ZPN_MCONN_MAX_CLIENT_TX_DATA) {
            ZPN_DEBUG_MCONN("%p: client transmit buffer length exceed max", mconn);
            res = ZPN_RESULT_ASYNCHRONOUS;
        } else {
            /* Everything is fine */
        }
    }

    if (!mconn->from_peer_data_us_b) {
        mconn->from_peer_data_us_b = epoch_us();
    }
    mconn->from_peer_data_us = epoch_us();


    ZPN_DEBUG_MCONN("%p: zpn_mconn_send_data_to_client, client transmit buffer: %p - calling zpn_mconn_dequeue_data", mconn, zpn_mconn_get_transmit_buffer(mconn));

    res2 = zpn_mconn_dequeue_data(mconn, 0);
    ZPN_DEBUG_MCONN("%p: zpn_mconn_dequeue_data, res: %s", mconn, zpn_result_string(res2));

    return res;
}

/*
 * Locking: It is assumed we have the lock to mtunnel to which this mconn belongs
 */
int zpn_mconn_pause_client(struct zpn_mconn *mconn, int need_to_lock)
{
    int res;
    struct zpn_mconn *outer_mconn;

    if (!mconn->global_owner_calls) {
        return ZPN_RESULT_NO_ERROR;
    }

    outer_mconn = (mconn->global_owner_calls->outer_mconn)(mconn,
                                                           mconn->self,
                                                           mconn->global_owner,
                                                           mconn->global_owner_key,
                                                           mconn->global_owner_key_length);
    if (!outer_mconn) {
        return ZPN_RESULT_NO_ERROR;
    }

    if (!outer_mconn->local_owner) {
        return ZPN_RESULT_NO_ERROR;
    }

    if (outer_mconn->client_paused) {
        /* It's common to try re-pause a client, because they can be
         * sending us a large stream of data, asynchronously */
        outer_mconn->client_needs_to_pause = 0;
        return ZPN_RESULT_NO_ERROR;
    }

    ZPN_DEBUG_MCONN("%p:  zpn_mconn_pause_client", mconn);

    res = (outer_mconn->local_owner_calls->pause)(outer_mconn,
                                                  outer_mconn->self,
                                                  outer_mconn->local_owner,
                                                  outer_mconn->local_owner_key,
                                                  outer_mconn->local_owner_key_length,
                                                  outer_mconn->local_owner_incarnation,
                                                  outer_mconn->fohh_thread_id);
    if (res == ZPN_RESULT_NO_ERROR) {
        outer_mconn->client_paused = 1;
        outer_mconn->client_needs_to_pause = 0;
        outer_mconn->client_needs_to_resume = 0;
    } else if (res == ZPN_RESULT_WOULD_BLOCK) {
        outer_mconn->client_paused = 0;
        outer_mconn->client_needs_to_pause = 1;
        outer_mconn->client_needs_to_resume = 0;
    } else {
        ZPN_LOG(AL_ERROR, "mconn pause returned %s", zpn_result_string(res));
        return res;
    }
    return ZPN_RESULT_NO_ERROR;
}


int zpn_mconn_resume_client(struct zpn_mconn *mconn, int need_to_lock)
{
    int res;
    struct zpn_mconn *outer_mconn;

    if (!mconn->global_owner_calls) {
        return ZPN_RESULT_NO_ERROR;
    }

    outer_mconn = (mconn->global_owner_calls->outer_mconn)(mconn,
                                                           mconn->self,
                                                           mconn->global_owner,
                                                           mconn->global_owner_key,
                                                           mconn->global_owner_key_length);
    if (!outer_mconn) {
        return ZPN_RESULT_NO_ERROR;
    }

    if (!outer_mconn->local_owner) {
        return ZPN_RESULT_NO_ERROR;
    }

    if (!outer_mconn->client_paused) {
        outer_mconn->client_needs_to_resume = 0;
        return ZPN_RESULT_NO_ERROR;
    }

    ZPN_DEBUG_MCONN("%p:  zpn_mconn_resume_client", mconn);

    res = (outer_mconn->local_owner_calls->resume)(outer_mconn,
                                                   outer_mconn->self,
                                                   outer_mconn->local_owner,
                                                   outer_mconn->local_owner_key,
                                                   outer_mconn->local_owner_key_length,
                                                   outer_mconn->local_owner_incarnation,
                                                   outer_mconn->fohh_thread_id);
    if (res == ZPN_RESULT_NO_ERROR) {
        outer_mconn->client_paused = 0;
        outer_mconn->client_needs_to_pause = 0;
        outer_mconn->client_needs_to_resume = 0;
    } else if (res == ZPN_RESULT_WOULD_BLOCK) {
        outer_mconn->client_paused = 1;
        outer_mconn->client_needs_to_pause = 0;
        outer_mconn->client_needs_to_resume = 1;
    } else {
        ZPN_LOG(AL_ERROR, "mconn resume returned %s", zpn_result_string(res));
        return res;
    }
    return ZPN_RESULT_NO_ERROR;
}

/*
 * Locking: It is assumed we have the lock to mtunnel to which this mconn belongs
 */
int zpn_mconn_disable_read_client(struct zpn_mconn *mconn, int need_to_lock)
{
    int res;
    struct zpn_mconn *outer_mconn;

    if (!mconn->global_owner_calls) {
        return ZPN_RESULT_NO_ERROR;
    }

    outer_mconn = (mconn->global_owner_calls->outer_mconn)(mconn,
                                                           mconn->self,
                                                           mconn->global_owner,
                                                           mconn->global_owner_key,
                                                           mconn->global_owner_key_length);
    if (!outer_mconn) {
        return ZPN_RESULT_NO_ERROR;
    }

    if (!outer_mconn->local_owner) {
        return ZPN_RESULT_NO_ERROR;
    }

    if (outer_mconn->disable_read_client_flag) {
        return ZPN_RESULT_NO_ERROR;
    }

    ZPN_DEBUG_MCONN("%p: mconn disable read client", mconn);

    res = (outer_mconn->local_owner_calls->disable_read)(outer_mconn,
                                                  outer_mconn->self,
                                                  outer_mconn->local_owner,
                                                  outer_mconn->local_owner_key,
                                                  outer_mconn->local_owner_key_length,
                                                  outer_mconn->local_owner_incarnation,
                                                  outer_mconn->fohh_thread_id);
    if (res == ZPN_RESULT_NO_ERROR) {
        outer_mconn->disable_read_client_flag = 1;
    } else {
        ZPN_LOG(AL_ERROR, "%p:  mconn disable read client  returned %s", mconn, zpn_result_string(res));
        return res;
    }
    return ZPN_RESULT_NO_ERROR;
}

int zpn_mconn_enable_read_client(struct zpn_mconn *mconn, int need_to_lock)
{
    int res;
    struct zpn_mconn *outer_mconn;

    if (!mconn->global_owner_calls) {
        return ZPN_RESULT_NO_ERROR;
    }

    outer_mconn = (mconn->global_owner_calls->outer_mconn)(mconn,
                                                           mconn->self,
                                                           mconn->global_owner,
                                                           mconn->global_owner_key,
                                                           mconn->global_owner_key_length);
    if (!outer_mconn) {
        return ZPN_RESULT_NO_ERROR;
    }

    if (!outer_mconn->local_owner) {
        return ZPN_RESULT_NO_ERROR;
    }

    if (!outer_mconn->disable_read_client_flag) {
        return ZPN_RESULT_NO_ERROR;
    }

    ZPN_DEBUG_MCONN("%p: mconn enable read client", mconn);

    res = (outer_mconn->local_owner_calls->enable_read)(outer_mconn,
                                                   outer_mconn->self,
                                                   outer_mconn->local_owner,
                                                   outer_mconn->local_owner_key,
                                                   outer_mconn->local_owner_key_length,
                                                   outer_mconn->local_owner_incarnation,
                                                   outer_mconn->fohh_thread_id);
    if (res == ZPN_RESULT_NO_ERROR) {
        outer_mconn->disable_read_client_flag = 0;
    } else {
        ZPN_LOG(AL_ERROR, "%p:  mconn enable read client  returned %s", mconn, zpn_result_string(res));
        return res;
    }
    return ZPN_RESULT_NO_ERROR;
}

int zpn_mconn_client_window_update(struct zpn_mconn *mconn, int needs_to_lock, int total_xmt, int batch_win_upd)
{
    struct zpn_mconn *outer_mconn;

    if (!mconn->global_owner_calls) {
        return ZPN_RESULT_NO_ERROR;
    }

    outer_mconn = (mconn->global_owner_calls->outer_mconn)(mconn,
                                                           mconn->self,
                                                           mconn->global_owner,
                                                           mconn->global_owner_key,
                                                           mconn->global_owner_key_length);
    if (!outer_mconn) {
        ZPN_DEBUG_MCONN("%p(%s) trying to send window update but mtunnel gone, out of sync of %d bytes", mconn, zpn_mconn_type_str(mconn->type), total_xmt);
        return ZPN_RESULT_NO_ERROR;
    }

    if (outer_mconn->local_owner && outer_mconn->local_owner_calls) {
        (outer_mconn->local_owner_calls->window_update)(outer_mconn,
                                                        outer_mconn->self,
                                                        outer_mconn->local_owner,
                                                        outer_mconn->local_owner_key,
                                                        outer_mconn->local_owner_key_length,
                                                        outer_mconn->local_owner_incarnation,
                                                        outer_mconn->fohh_thread_id,
                                                        total_xmt,
                                                        batch_win_upd);
    } else {
        ZPN_DEBUG_MCONN("%p(%s) trying to send window update but local owner gone, out of sync of %d bytes", mconn, zpn_mconn_type_str(mconn->type), total_xmt);
    }


    return ZPN_RESULT_NO_ERROR;
}

/*needs to be called with owner lock held*/
void zpn_mconn_stats_update(struct zpn_mconn *mconn, enum zpn_mconn_stats stats_name)
{
    if (!mconn) {
        ZPN_DEBUG_MCONN("no mconn to update stats");
        return;
    }

    if (mconn->local_owner_calls) {
        mconn->local_owner_calls->stats_update(mconn,
                                               mconn->self,
                                               mconn->local_owner,
                                               mconn->local_owner_key,
                                               mconn->local_owner_key_length,
                                               mconn->local_owner_incarnation,
                                               mconn->fohh_thread_id,
                                               stats_name);
    }
    return;
}

void *zpn_mconn_get_local_owner(struct zpn_mconn *mconn, int64_t *incarnation)
{
    if (incarnation) (*incarnation) = mconn->local_owner_incarnation;
    return mconn->local_owner;
}

static void zpn_mconn_account_icmp_drops(struct zpn_mconn *mconn, size_t buf_len,
                                         int ip_proto, int64_t *dropped_bytes,
                                         enum zpn_mconn_drop_stats *drop_stats_type, int reason)
{
  if (dropped_bytes && drop_stats_type) {
    *dropped_bytes = buf_len;
    if (ip_proto == IPPROTO_ICMP)
      *drop_stats_type = drop_stats_icmp_error;
    else
      *drop_stats_type = drop_stats_icmp6_error;
  }
  zpn_mconn_client_window_update(mconn, 0, (int)buf_len, 0); // already receive context
  ZPN_DEBUG_MCONN("mconn: %p; icmp4/6 drop bytes: %zd; ip_proto: %d; reason: %d",
                  mconn, buf_len, ip_proto, reason);
}

/*
 * Locking: It is assumed we have the lock to mtunnel to which this mconn belongs
 */
int zpn_client_process_rx_data(struct zpn_mconn *mconn, struct evbuffer *buf, size_t buf_len,
                               int64_t *dropped_bytes, enum zpn_mconn_drop_stats *drop_stats_type)
{
    struct zpn_mconn *peer;
    int res = ZPN_RESULT_NO_ERROR;
    int res2 = ZPN_RESULT_NO_ERROR;

    peer = mconn->peer;

    if (peer) {
        ZPN_DEBUG_MCONN("Process rx data on %p(%s), send to %p(%s), buf_len: %ld",
                        mconn, zpn_mconn_type_str(mconn->type),
                        peer, zpn_mconn_type_str(peer->type), (long)buf_len);
    } else {
        ZPN_DEBUG_MCONN("Process rx data on %p(%s), buf_len: %ld, but no peer available",
                        mconn, zpn_mconn_type_str(mconn->type), (long)buf_len);
    }

    if (mconn->fin_rcvd) {
        char *mtunnel_id;

        mtunnel_id = (mconn->global_owner_calls->id)(mconn,
                                                     mconn->self,
                                                     mconn->global_owner,
                                                     mconn->global_owner_key,
                                                     mconn->global_owner_key_length);

        if (buf_len) {
            ZPN_LOG(AL_WARNING, "%s: Data %d bytes received after FIN (mtunnel_end) is received",
                    mtunnel_id ? mtunnel_id : "NULL", (int)buf_len);
        }
    }

    if (!peer) {
        ZPN_DEBUG_MCONN("%p: No peer to send data to", mconn);
        /* Buffer the data before the peer is available */
        if (!mconn->client_rx_data) {
            mconn->client_rx_data = evbuffer_new();
            evbuffer_set_dont_dump(mconn->client_rx_data);
            ZPN_DEBUG_MCONN("%p: Allocate client rx data %p, holding rx until peer avaliable", mconn, mconn->client_rx_data);
        }

        if (mconn->client_rx_data) {
            evbuffer_remove_buffer(buf, mconn->client_rx_data, buf_len);
            zpn_mconn_client_rx_data_len_stats(mconn, buf_len);
            if (evbuffer_get_length(mconn->client_rx_data) > (100*ZPN_MCONN_MAX_CLIENT_TX_DATA)) {
                int64_t uncounted_dropped_bytes = 0;
                ZPN_LOG(AL_WARNING, "%p: client_rx_data length exceed max by 100 times, peer doesn't respond to flow control?", mconn);
                zpn_mconn_terminate(mconn, 1, 0, MT_CLOSED_INTERNAL_ERROR, &uncounted_dropped_bytes);
                if (uncounted_dropped_bytes) {
                    ZPN_LOG(AL_WARNING, "%p: client_rx_data length exceed by 100 times, terminating mconn but uncounted_dropped_bytes %"PRId64" ", mconn, uncounted_dropped_bytes);
                }
            } else if (evbuffer_get_length(mconn->client_rx_data) > ZPN_MCONN_MAX_CLIENT_TX_DATA) {
                //ZPN_DEBUG_MCONN("%p: client_rx_data length exceed max", mconn);
                ZPN_LOG(AL_WARNING, "%p: client_rx_data length exceed max: %ld > %ld",
                        mconn,
                        (long) evbuffer_get_length(mconn->client_rx_data),
                        (long) ZPN_MCONN_MAX_CLIENT_TX_DATA);
                return ZPN_RESULT_ASYNCHRONOUS;
            }
        }

        return ZPN_RESULT_NO_ERROR;
    }

    if (!mconn->rx_data_us_b) {
        mconn->rx_data_us_b = epoch_us();
    }
    mconn->rx_data_us = epoch_us();

    zpn_mconn_track(mconn);

    int ip_proto = (mconn->global_owner_calls->ip_proto)(mconn,
                                                         mconn->self,
                                                         mconn->global_owner,
                                                         mconn->global_owner_key,
                                                         mconn->global_owner_key_length);
    if (ip_proto == IPPROTO_ICMP) {
        res2 = zpn_mconn_icmp_pkt_handler(mconn, buf);
        if (res2 == ZPN_MCONN_ICMP_PKT_TIME_EXCEEDED_DROP) {
            evbuffer_drain(buf, buf_len);
            mconn->icmp_time_exceeded_drops++;
            res = ZPN_RESULT_NO_ERROR;
            goto handle_icmp_drops;
        } else if (res2 == ZPN_MCONN_ICMP_PKT_FRAG_DROP) {
            evbuffer_drain(buf, buf_len);
            mconn->icmp_pkt_frag_drops++;
            res = ZPN_RESULT_NO_ERROR;
            goto handle_icmp_drops;
        } else if (res2 == ZPN_MCONN_ICMP_PKT_ACCESS_ERR) {
            evbuffer_drain(buf, buf_len);
            mconn->icmp_access_err_drops++;
            res = ZPN_RESULT_NO_ERROR;
            goto handle_icmp_drops;
        } else if (res2 == ZPN_MCONN_ICMP_PKT_RATE_LIMIT_EXCEEDED_ERR) {
            evbuffer_drain(buf, buf_len);
            mconn->icmp_rate_limit_exceeded_err_drops++;
            res = ZPN_RESULT_NO_ERROR;
            goto handle_icmp_drops;
        } else if (res2 == ZPN_MCONN_ICMP_PKT_MALFORMED_DROP) {
            evbuffer_drain(buf, buf_len);
            mconn->icmp_malformed_pkt_drops++;
            res = ZPN_RESULT_BAD_DATA;
            goto handle_icmp_drops;
        } else if (res2 == ZPN_MCONN_ICMP_PKT_ZERO_LEN_DROP) {
            ZPN_DEBUG_MCONN_ICMP("%p: Received 0 len ICMP Message... dropping", mconn);
            evbuffer_drain(buf, buf_len);
            mconn->icmp_zero_len_pkt_drops++;
            res = ZPN_RESULT_INSUFFICIENT_DATA;
            goto handle_icmp_drops;
        } else if (res2 == ZPN_MCONN_ICMP_PKT_INTERNAL_ERR) {
            evbuffer_drain(buf, buf_len);
            mconn->icmp_internal_err_drops++;
            zpn_mconn_terminate(mconn, 1, 0, MT_CLOSED_INTERNAL_ERROR, NULL);
            res = ZPN_RESULT_ERR;
            goto handle_icmp_drops;
        }
    } else if (ip_proto == IPPROTO_ICMPV6) {
        res2 = zpn_mconn_icmpv6_pkt_handler(mconn, buf);
        if (res2 == ZPN_MCONN_ICMP_PKT_INTERNAL_ERR) {
            evbuffer_drain(buf, buf_len);
            mconn->icmpv6_internal_err_drops++;
            zpn_mconn_terminate(mconn, 1, 0, MT_CLOSED_INTERNAL_ERROR, NULL);
            res = ZPN_RESULT_ERR;
            goto handle_icmp_drops;
        } else if (res2 == ZPN_MCONN_ICMP_PKT_MALFORMED_DROP) {
            evbuffer_drain(buf, buf_len);
            mconn->icmpv6_malformed_pkt_drops++;
            res = ZPN_RESULT_BAD_DATA;
            goto handle_icmp_drops;
        } else if (res2 == ZPN_MCONN_ICMP_PKT_ZERO_LEN_DROP) {
            ZPN_DEBUG_MCONN_ICMP("%p: Received 0 len ICMPv6 Message... dropping", mconn);
            evbuffer_drain(buf, buf_len);
            mconn->icmp_zero_len_pkt_drops++;
            res = ZPN_RESULT_INSUFFICIENT_DATA;
            goto handle_icmp_drops;
        } else if (res2 == ZPN_MCONN_ICMP_PKT_RATE_LIMIT_EXCEEDED_ERR) {
            evbuffer_drain(buf, buf_len);
            mconn->icmpv6_rate_limit_exceeded_err_drops++;
            res = ZPN_RESULT_NO_ERROR;
            goto handle_icmp_drops;
        } else if (res2 == ZPN_MCONN_ICMP_PKT_ACCESS_ERR) {
            evbuffer_drain(buf, buf_len);
            mconn->icmpv6_access_err_drops++;
            res = ZPN_RESULT_NO_ERROR;
            goto handle_icmp_drops;
        } else if (res2 == ZPN_MCONN_ICMP_PKT_TIME_EXCEEDED_DROP) {
            evbuffer_drain(buf, buf_len);
            mconn->icmpv6_time_exceeded_drops++;
            res = ZPN_RESULT_NO_ERROR;
            goto handle_icmp_drops;
        }
    }

    if (((ip_proto == IPPROTO_ICMP) || (ip_proto == IPPROTO_ICMPV6)) &&
        ((mconn->type == mconn_fohh_tlv_c && mconn->peer->type == mconn_icmp_s) ||
         (mconn->type == mconn_zrdt_tlv_c && mconn->peer->type == mconn_icmp_s) ||
         (mconn->type == mconn_fohh_tlv_c && mconn->peer->type == mconn_fohh_tlv_s) ||
         (mconn->type == mconn_fohh_tlv_c && mconn->peer->type == mconn_zrdt_tlv_s) ||
         (mconn->type == mconn_zrdt_tlv_c && mconn->peer->type == mconn_fohh_tlv_s) ||
         (mconn->type == mconn_zrdt_tlv_c && mconn->peer->type == mconn_zrdt_tlv_s)) &&
        ((res2 == ZPN_MCONN_ICMP_PKT_FORWARD_TERROR_TO_CLIENT) ||
         (res2 == ZPN_MCONN_ICMP_PKT_FORWARD_DERROR_TO_CLIENT))) {
        res = zpn_mconn_send_data_to_client(mconn, buf, evbuffer_get_length(buf), 1, NULL);
    } else {
        mconn->bytes_to_peer_attempt += buf_len;

        /* track mconn performance stats after bytes_to_peer_attempt */
        zpn_mconn_track_perf_ingress(mconn);
        res = zpn_mconn_send_data_to_client(peer, buf, buf_len, 1, dropped_bytes);
        if (ip_proto == IPPROTO_UDP && dropped_bytes && drop_stats_type && *dropped_bytes) {
          if (res == ZPN_RESULT_BAD_DATA)
            *drop_stats_type = drop_stats_udp_frame_error;
          else
            *drop_stats_type = drop_stats_udp_tx_full;
        }
        ZPN_DEBUG_MCONN("%p: zpn_mconn_send_data_to_client, ret: %s", mconn, zpn_result_string(res));
    }

    if (res == ZPN_RESULT_NO_ERROR) {
        ;
    } else if (res == ZPN_RESULT_ASYNCHRONOUS) {
        res = zpn_mconn_pause_client(mconn, 1);
        if (res == ZPN_RESULT_NO_ERROR) {
            /* All is well... */
        } else if (res == ZPN_RESULT_WOULD_BLOCK) {
            /* Full block... */
        } else {
            /* Some error... */
            int64_t uncounted_dropped_bytes = 0;
            ZPN_LOG(AL_ERROR, "Could not pause client: %s", zpn_result_string(res));
            zpn_mconn_terminate(mconn, 1, 0, MT_CLOSED_INTERNAL_ERROR, &uncounted_dropped_bytes);
            if (uncounted_dropped_bytes) {
                ZPN_LOG(AL_WARNING, "%p uncounted_dropped_bytes %"PRId64" ", mconn, uncounted_dropped_bytes);
            }
        }
    } else {
        /* Some other error */
        int64_t uncounted_dropped_bytes = 0;
        if (res == ZPN_RESULT_BAD_DATA) {
            /* Framing (probably UDP) error... */
            char *mtunnel_id;

            mtunnel_id = (mconn->global_owner_calls->id)(mconn,
                                                         mconn->self,
                                                         mconn->global_owner,
                                                         mconn->global_owner_key,
                                                         mconn->global_owner_key_length);

            ZPN_LOG(AL_WARNING, "%s: Framing error", mtunnel_id ? mtunnel_id : "Unknown");
            zpn_mconn_terminate(mconn, 1, 0, MT_CLOSED_FRAMING_ERROR, &uncounted_dropped_bytes);
        } else {
            ZPN_LOG(AL_ERROR, "Could not send data to client: %s", zpn_result_string(res));
            zpn_mconn_terminate(mconn, 1, 0, MT_CLOSED_INTERNAL_ERROR, &uncounted_dropped_bytes);
        }

        if (uncounted_dropped_bytes) {
            ZPN_LOG(AL_WARNING, "%p, uncounted_dropped_bytes : %"PRId64" due to framing error ", mconn, uncounted_dropped_bytes);
        }
    }
    goto exit;

handle_icmp_drops:
  zpn_mconn_account_icmp_drops(mconn, buf_len, ip_proto, dropped_bytes, drop_stats_type, res2);
  // fall thru to exit label
exit:
    return res;
}

/*
 * Locking: it is assumed we have the lock of mtunnel to which mconn belongs
 */
int zpn_client_drain_tx_data(struct zpn_mconn *mconn)
{
    int res;

    ZPN_DEBUG_MCONN("%p: zpn_client_drain_tx_data, client transmit buffer: %p", mconn, zpn_mconn_get_transmit_buffer(mconn));

    res = zpn_mconn_dequeue_data(mconn, 0);

    ZPN_DEBUG_MCONN("%p: zpn_mconn_dequeue_data, ret: %s", mconn, zpn_result_string(res));
    if (res == ZPN_RESULT_NO_ERROR) {

        ZPN_DEBUG_MCONN("%p: zpn_client_drain_tx_data, dequeue success, client transmit buffer: %p, client_rx_data: %p",
                        mconn, zpn_mconn_get_transmit_buffer(mconn), mconn->client_rx_data);

        /* We have drained all buffered data, tell peer to resume again */
        /* FIXME: should we define a low wather mark here??? */

        if (zpn_mconn_get_transmit_buffer_len(mconn) < ZPN_MCONN_MAX_CLIENT_TX_DATA) {
            if (mconn->peer) {
                res = zpn_mconn_resume_client(mconn->peer, 1);

                if (res && (res != ZPN_RESULT_WOULD_BLOCK)) {
                    int64_t uncounted_dropped_bytes = 0;
                    ZPN_LOG(AL_ERROR, "%p: Cannot resume peer: %p, res = %s", mconn, mconn->peer, zpn_result_string(res));
                    zpn_mconn_terminate(mconn->peer, 1, 0, MT_CLOSED_INTERNAL_ERROR, &uncounted_dropped_bytes);
                    if (uncounted_dropped_bytes) {
                        ZPN_LOG(AL_ERROR, "%p, can not resume peer, uncounted_dropped_bytes %"PRId64" ", mconn, uncounted_dropped_bytes);
                    }
                }
            }
        }
    }

    return res;
}

int zpn_mconn_clean(struct zpn_mconn *mconn)
{
    int ret = 1;
    int res = ZPN_RESULT_NO_ERROR;
    int data_buffered = 0;

    ZPN_DEBUG_MCONN("%p: zpn_mconn_clean(), type = %s, terminated = %d, client transmit buffer: %p, client_rx_data: %p",
                    mconn, zpn_mconn_type_str(mconn->type), mconn->terminated, zpn_mconn_get_transmit_buffer(mconn), mconn->client_rx_data);

    /* Check to see if we still have data to be sent */
    if (zpn_mconn_transmit_buffer_exists(mconn)) {
        if (zpn_mconn_get_transmit_buffer_len(mconn)) {
            data_buffered = 1;
            ret = 0;
            goto done;
        } else {
            zpn_mconn_free_transmit_buffer(mconn);
        }
    }

    /* Check to see if we still have data to be sent to peer */
    if (mconn->client_rx_data) {
        if (evbuffer_get_length(mconn->client_rx_data)) {
            ZPN_DEBUG_MCONN("%p -- has client_rx_data", mconn);
            if (mconn->terminated) {
                zpn_mconn_discard_client_data(mconn, NULL);
            } else {
                data_buffered = 1;
                ret = 0;
                goto done;
            }
        } else {
            evbuffer_free(mconn->client_rx_data);
            mconn->client_rx_data = NULL;
        }
    }

    if (mconn->local_owner) {
        ZPN_DEBUG_MCONN("%p: -- has local_owner", mconn);
        if (!data_buffered && mconn->client_needs_to_disconnect_local_owner) {
            ZPN_DEBUG_MCONN("%p: try remove local owner", mconn);
            res = zpn_mconn_remove_local_owner(mconn, 0, 1, 0, NULL);
            if (res) {
                ZPN_DEBUG_MCONN("%p: cannot remove local owner", mconn);
                ret = 0;
                goto done;
            }
        } else {
            ret = 0;
            goto done;
        }
    }

    /* Check to see if we are still connected with others somehow */
    if (mconn->global_owner || mconn->peer) {
        if (mconn->global_owner) ZPN_DEBUG_MCONN("%p: -- has global_owner", mconn);
        if (mconn->peer) ZPN_DEBUG_MCONN("%p: -- has peer: %p", mconn, mconn->peer);
        if (!mconn->peer) {
            /* try remove global owner again, since we don't have local owner or peer already */
            ZPN_DEBUG_MCONN("%p: try remove global owner", mconn);
            res = zpn_mconn_remove_global_owner(mconn, 1, NULL);
            if (res) {
                ZPN_DEBUG_MCONN("%p: cannot remove global owner", mconn);
                ret = 0;
                goto done;
            }
        } else {
            ret = 0;
            goto done;
        }
    }

done:
    return ret;
}

/*
 * This should be called when we deemed that there is no hope of forwarding the data stuck in mconn.
 * Assume the global owner of the mconn is locked.
 */
void zpn_mconn_flush_data(struct zpn_mconn *mconn)
{
	zpn_mconn_free_transmit_buffer(mconn);

    /* Check to see if we still have data to be sent to peer */
    if (mconn->client_rx_data) {
        evbuffer_free(mconn->client_rx_data);
        mconn->client_rx_data = NULL;
    }
}

void zpn_mconn_set_fohh_thread_id(struct zpn_mconn *mconn, int fohh_thread_id)
{
    if (!mconn) return;
    mconn->fohh_thread_id = fohh_thread_id;
}

int zpn_mconn_get_fohh_thread_id(struct zpn_mconn *mconn, int *fohh_thread_id)
{
    if (!mconn) return ZPN_RESULT_ERR;

    *fohh_thread_id = mconn->fohh_thread_id;
    return ZPN_RESULT_NO_ERROR;
}

void zpn_mconn_internal_display(struct zpn_mconn *mconn)
{
    ZPN_DEBUG_MCONN("mconn(%p) bytes_to_client = %ld, bytes_to_peer = %ld, bytes_to_peer_attempt = %ld, "
                    "drop_udp_framed_data = %ld",
                    mconn, (long)mconn->bytes_to_client, (long)mconn->bytes_to_peer, (long)mconn->bytes_to_peer_attempt,
                    (long)mconn->drop_udp_framed_data);
    if (zpn_mconn_transmit_buffer_exists(mconn)) {
        ZPN_DEBUG_MCONN("mconn(%p) client transmit buffer len = %d", mconn, (int)zpn_mconn_get_transmit_buffer_len(mconn));
    } else {
        ZPN_DEBUG_MCONN("mconn(%p) client transmit buffer len = 0", mconn);
    }
    ZPN_DEBUG_MCONN("mconn(%p) fin_rcvd = %d, fin_sent = %d, needs_to_forward = %d, needs_to_disconnect = %d, "
                    "to_client_paused = %d, drop_udp_framed_data = %d, fin_exp = %"PRId64"",
                    mconn, mconn->fin_rcvd, mconn->fin_sent, mconn->client_needs_to_forward,
                    mconn->client_needs_to_disconnect_local_owner, mconn->to_client_paused,
                    mconn->drop_udp_framed_data, mconn->config_fin_expire_us);
    ZPN_DEBUG_MCONN("mconn(%p), peer(%p) "
                    "icmp_access_type = %d, icmp_time_exceeded_drops = %ld, icmp_pkt_frag_drops = %ld, icmp_malformed_pkt_drops = %ld, icmp_zero_len_pkt_drops = %ld, "
                    "icmp_internal_err_drops = %ld, icmp_timeout_failure_drops = %ld, icmp_access_err_drops = %ld, icmp_rate_limit_exceeded_err_drop: %ld, "
                    "icmp_tx_packets = %ld, icmp_rx_packets = %ld, icmpv6_internal_err_drops = %ld, icmpv6_timeout_failure_drops = %ld "
                    "icmpv6_tx_packets = %ld, icmpv6_rx_packets = %ld",
                    mconn, mconn->peer, mconn->icmp_access_type, (long)mconn->icmp_time_exceeded_drops, (long)mconn->icmp_pkt_frag_drops,
                    (long)mconn->icmp_malformed_pkt_drops, (long)mconn->icmp_zero_len_pkt_drops, (long)mconn->icmp_internal_err_drops, (long)mconn->icmp_timeout_failure_drops,
                    (long)mconn->icmp_access_err_drops, (long)mconn->icmp_rate_limit_exceeded_err_drops, (long)mconn->icmp_tx_packets,
                    (long)mconn->icmp_rx_packets, (long)mconn->icmpv6_internal_err_drops, (long)mconn->icmpv6_timeout_failure_drops,
                    (long)mconn->icmpv6_tx_packets, (long)mconn->icmpv6_rx_packets);
}

/*
 * Locking: it is assumed we have the lock to the mtunnel to which this mconn belongs
 */
void zpn_mconn_discard_client_data(struct zpn_mconn *mconn, int64_t *uncounted_dropped_bytes)
{
    if (!mconn) return;

    ZPN_DEBUG_MCONN("%p: zpn_mconn_discard_client_data, client transmit buffer: %p", mconn, zpn_mconn_get_transmit_buffer(mconn));

    if (zpn_mconn_transmit_buffer_exists(mconn)) {
        size_t len = zpn_mconn_get_transmit_buffer_len(mconn);

        if (len && mconn->peer) {
            zpn_mconn_client_window_update(mconn->peer, 0, (int)len, 0);
            zpn_mconn_stats_update(mconn, window_update_sucesss_discard_data);
        } else {
            if (len) {
                zpn_mconn_stats_update(mconn, window_update_no_peer_discard_data);
                ZPN_DEBUG_MTUNNEL("%p(%s) dropping %zu bytes from transmit buffer, but there is no peer to send window update", mconn, zpn_mconn_type_str(mconn->type), len);
                if (uncounted_dropped_bytes) {
                    *uncounted_dropped_bytes = len;
                }
            }
        }

        zpn_mconn_free_transmit_buffer(mconn);
        mconn->dropped_untransmitted_bytes = len;
    }

    ZPN_DEBUG_MCONN("%p: zpn_mconn_discard_client_data, client_rx_data: %p", mconn, mconn->client_rx_data);

    if (mconn->client_rx_data) {
        size_t len = evbuffer_get_length(mconn->client_rx_data);

        evbuffer_free(mconn->client_rx_data);
        mconn->client_rx_data = NULL;
        mconn->dropped_untransmitted_bytes = len;

        if (len && mconn->peer) {
            zpn_mconn_client_window_update(mconn->peer, 0, (int)len, 0);
            zpn_mconn_stats_update(mconn, window_update_sucesss_discard_data);
        } else {
            if (len) {
                zpn_mconn_stats_update(mconn, window_update_no_peer_discard_data);
                ZPN_DEBUG_MTUNNEL("%p(%s) dropping %zu bytes from client_rx buffer, but there is no peer to send window update", mconn, zpn_mconn_type_str(mconn->type), len);
                if (uncounted_dropped_bytes) {
                    *uncounted_dropped_bytes = len;
                }
            }
        }
    }
}

void zpn_mconn_to_client_pause(struct zpn_mconn *mconn)
{

    ZPN_DEBUG_MCONN("%p: zpn_mconn_to_client_pause, client transmit buffer: %p", mconn, zpn_mconn_get_transmit_buffer(mconn));

    mconn->to_client_paused = 1;
    mconn->pause_start_us = epoch_us();
    mconn->from_client_paused_count++; /* Received pause from client via mtunnel_tag_resume_cb */
}

void zpn_mconn_to_client_resume(struct zpn_mconn *mconn)
{
    int64_t pause_elapsed_time_us;

    ZPN_DEBUG_MCONN("%p: zpn_mconn_to_client_resume, client transmit buffer: %p", mconn, zpn_mconn_get_transmit_buffer(mconn));

    mconn->to_client_paused = 0;
    mconn->from_client_resume_count++; /* Received resume from client via mtunnel_tag_resume_cb */

    pause_elapsed_time_us = epoch_us() - mconn->pause_start_us;
    mconn->from_client_pause_time_total_us += pause_elapsed_time_us;
    if (mconn->from_client_pause_time_max_us < pause_elapsed_time_us) {
        mconn->from_client_pause_time_max_us = pause_elapsed_time_us;
        mconn->from_client_pause_time_max_epoch_us = epoch_us();
    }
    zpn_mconn_dequeue_data(mconn, 0);
}

int zpn_mconn_forward_mtunnel_end(struct zpn_mconn *mconn, const char *err, int32_t drop_data)
{
    int ret = ZPN_RESULT_NO_ERROR;

    if (!mconn) {
        ZPN_LOG(AL_CRITICAL, "No mconn");
        return ZPN_RESULT_ERR;
    }

    ZPN_DEBUG_MCONN("%p: zpn_mconn_forward_mtunnel_end, client_tx_data: %p error: %s", mconn,
                    zpn_mconn_get_transmit_buffer(mconn), err ? err : "");

    if (mconn->fin_sent && (!drop_data || mconn->fin_sent_drop_data)) {
        ZPN_DEBUG_MCONN("%p: No need to forward, fin sent already", mconn);
        return ZPN_RESULT_NO_ERROR;
    }

    if (!drop_data) {
        if (zpn_mconn_get_transmit_buffer_len(mconn)) {
                /* We have queued data, but we want to dequeue it before
                 * sending the mtunnel_end
                 */
            mconn->client_needs_to_forward = 1;
            ZPN_DEBUG_MCONN("%p: Need to wait for data drain before forwarding", mconn);
            return ret;
        }
    } else {
        ZPN_DEBUG_MCONN("%p: Drop all data since drop_data is true", mconn);
        zpn_mconn_discard_client_data(mconn, NULL);
    }

    if (!mconn->local_owner) {
        mconn->fin_sent = 1;
        mconn->hold_forward_mtunnel_end = 0;
        if (mconn->terminate_at_connector) {
            mconn->fin_rcvd = 1;
        }
        ZPN_DEBUG_MCONN("%p: Already disconnected from local owner", mconn);
        ZPN_DEBUG_MCONN("client transmit buffer = %ld, bytes_to_client = %ld, bytes_to_peer = %ld",
                        zpn_mconn_get_transmit_buffer_len(mconn),
                        (long)mconn->bytes_to_client,
                        (long)mconn->bytes_to_peer);
        return ZPN_RESULT_NO_ERROR;
    } else {
        if (!mconn->fin_sent_us) mconn->fin_sent_us = epoch_us();
        ret = (mconn->local_owner_calls->forward_tunnel_end)(mconn,
                                                             mconn->self,
                                                             mconn->local_owner,
                                                             mconn->local_owner_key,
                                                             mconn->local_owner_key_length,
                                                             mconn->local_owner_incarnation,
                                                             err,
                                                             drop_data);
    }

    return ret;
}

int zpn_mconn_done(struct zpn_mconn *mconn)
{
    if (mconn->fin_rcvd && mconn->fin_sent) {
        ZPN_DEBUG_MCONN("%p: zpn_mconn done, client transmit buffer: %p, fin exp: %"PRId64"", mconn, zpn_mconn_get_transmit_buffer(mconn), mconn->config_fin_expire_us);
        return 1;
    } else {
        ZPN_DEBUG_MCONN("%p: zpn_mconn not done, client transmit buffer: %p, fin_rcvd = %ld, fin_send = %ld",
                        mconn, zpn_mconn_get_transmit_buffer(mconn), (long)mconn->fin_rcvd, (long)mconn->fin_sent);
        return 0;
    }
}

int zpn_mconn_fin_expired(struct zpn_mconn *mconn)
{
#if MCONN_FIN_EXPIRED_ENABLE
    if (mconn->fin_rcvd ) {
        int64_t now = epoch_us();
        int64_t diff_us = (now - mconn->fin_rcvd_us);
        if ((mconn->config_fin_expire_us && (diff_us > mconn->config_fin_expire_us)) ||
            (diff_us > FIN_EXPIRE_US)) {
            return 1;
        } else {
            return 0;
        }
    } else {
        return 0;
    }
#else
    return 0;
#endif
}

int zpn_mconn_track(struct zpn_mconn *mconn)
{
    if (mconn->track && mconn->tx_data_us && mconn->rx_data_us) {
        if (mconn->tx_data_us > mconn->rx_data_us) {
            mconn->tx_rx_total -= mconn->tx_rx_diff[mconn->tx_rx_index];
            mconn->tx_rx_diff[mconn->tx_rx_index] = mconn->tx_data_us - mconn->rx_data_us;
            mconn->tx_rx_total += mconn->tx_rx_diff[mconn->tx_rx_index];
            mconn->tx_rx_index ++;
            mconn->tx_rx_index = mconn->tx_rx_index % ZPN_MCONN_TRACK_ARRAY_SIZE;
        } else {
            mconn->rx_tx_total -= mconn->rx_tx_diff[mconn->rx_tx_index];
            mconn->rx_tx_diff[mconn->rx_tx_index] = mconn->rx_data_us - mconn->tx_data_us;
            mconn->rx_tx_total += mconn->rx_tx_diff[mconn->rx_tx_index];
            mconn->rx_tx_index++;
            mconn->rx_tx_index = mconn->rx_tx_index % ZPN_MCONN_TRACK_ARRAY_SIZE;
        }
    }

    return ZPN_RESULT_NO_ERROR;
}

void zpn_mconn_track_perf_ingress(struct zpn_mconn *mconn)
{
    int64_t diff = 0;
    int64_t tx_peer_rx_data_us = 0;
    int64_t tx_peer_rx_data_bytes_to_peer_attempt = 0;

    // if track perf is not enabled
    if(!mconn->is_mconn_track_perf_stats_enabled) {
        return;
    }

    // assumes called after rx: mconn->rx_data_us
    if(!mconn->rx_data_us){
        return;
    }

    tx_peer_rx_data_us = __atomic_load_n(&mconn->tx_peer_rx_data_us, __ATOMIC_RELAXED);

    // if tx mconn->peer finished measuring, start another measure time diff between mconn tx to client and rx from mconn->peer. Thread safe as it might be set at peer thread
    if (tx_peer_rx_data_us == 0) {
        __atomic_store_n(&mconn->tx_peer_rx_data_us, mconn->rx_data_us, __ATOMIC_RELAXED);
        tx_peer_rx_data_us = __atomic_load_n(&mconn->tx_peer_rx_data_us, __ATOMIC_RELAXED);

        __atomic_store_n(&mconn->tx_peer_rx_data_bytes_to_peer_attempt, mconn->bytes_to_peer_attempt, __ATOMIC_RELAXED);
    }

    tx_peer_rx_data_bytes_to_peer_attempt = __atomic_load_n(&mconn->tx_peer_rx_data_bytes_to_peer_attempt, __ATOMIC_RELAXED);

    __atomic_add_fetch(&mconn->tx_peer_rx_data_cnt, 1, __ATOMIC_RELAXED);

    if (!mconn->rx_diff_rx_data_us) {
        mconn->rx_diff_rx_data_us = mconn->rx_data_us;
    } else {
        diff = mconn->rx_data_us - mconn->rx_diff_rx_data_us;
        if (diff < ZPN_MCONN_TRACK_RX_DIFF_RX_DATA_50000_US) {
            mconn->rx_diff_rx_data_hist[0]++;
        } else if (diff < ZPN_MCONN_TRACK_RX_DIFF_RX_DATA_200000_US) {
            mconn->rx_diff_rx_data_hist[1]++;
        } else if (diff < ZPN_MCONN_TRACK_RX_DIFF_RX_DATA_700000_US) {
            mconn->rx_diff_rx_data_hist[2]++;
        } else if (diff < ZPN_MCONN_TRACK_RX_DIFF_RX_DATA_1500000_US) {
            mconn->rx_diff_rx_data_hist[3]++;
        } else {
            mconn->rx_diff_rx_data_hist[4]++;
        }
        mconn->rx_diff_rx_data_us = mconn->rx_data_us;
    }
    ZPN_DEBUG_MCONN("%p, track perf ingress tx_peer_rx_data_us=%"PRId64", bytes_to_peer_attempt=%"PRId64", =%"PRId64", in-cnt=%d, diff from prev rx us:%"PRId64", rx_diff_rx_data: %d,%d,%d,%d,%d", mconn,
                    tx_peer_rx_data_us, mconn->bytes_to_peer_attempt, tx_peer_rx_data_bytes_to_peer_attempt, mconn->tx_peer_rx_data_cnt,
                    diff,
                    mconn->rx_diff_rx_data_hist[0],mconn->rx_diff_rx_data_hist[1], mconn->rx_diff_rx_data_hist[2], mconn->rx_diff_rx_data_hist[3], mconn->rx_diff_rx_data_hist[4]);
    return;
}

void zpn_mconn_track_perf_egress(struct zpn_mconn *mconn)
{
    int tx_data_unblock_us = 0;
    int tx_data_unblock_cnt = 0;
    int tx_data_unblock_max_us = 0;
    int tx_peer_rx_data_diff_us = 0;
    int tx_peer_rx_data_cnt = 0;
    int tx_peer_rx_data_max_us = 0;
    int tx_peer_rx_data_max_unblock_cnt_curr = 0;
    int64_t tx_peer_rx_data_us = 0;
    int64_t tx_peer_rx_data_bytes_to_peer_attempt = 0;

    // if track perf is not enabled
    if(!mconn->is_mconn_track_perf_stats_enabled) {
        return;
    }

    // assumed to be called at egress - tx: mconn->tx_data_us
    if(!mconn->tx_data_us) {
        return;
    }

    tx_peer_rx_data_max_unblock_cnt_curr = mconn->tx_peer_rx_data_max_unblock_cnt_curr;

    if (mconn->tx_data_unblock_us) {
        tx_data_unblock_us = (int)(mconn->tx_data_us - mconn->tx_data_unblock_us);
        tx_data_unblock_cnt = mconn->tx_data_unblock_cnt;

        // tx_data_unblock_max_us can be reset at any time
        tx_data_unblock_max_us = __atomic_load_n(&mconn->tx_data_unblock_max_us, __ATOMIC_RELAXED);
        if (tx_data_unblock_us > tx_data_unblock_max_us) {
            __atomic_store_n(&mconn->tx_data_unblock_max_us, tx_data_unblock_us, __ATOMIC_RELAXED);
            mconn->tx_data_unblock_max_cnt = tx_data_unblock_cnt;
        }
        mconn->tx_data_unblock_tot_us += tx_data_unblock_us;
        mconn->tx_data_unblock_us = 0;
        mconn->tx_data_unblock_cnt = 0;
    }

    if (mconn->peer) {
        tx_peer_rx_data_us = __atomic_load_n(&mconn->peer->tx_peer_rx_data_us, __ATOMIC_RELAXED);
        tx_peer_rx_data_cnt = __atomic_load_n(&mconn->peer->tx_peer_rx_data_cnt, __ATOMIC_RELAXED);
        tx_peer_rx_data_bytes_to_peer_attempt = __atomic_load_n(&mconn->peer->tx_peer_rx_data_bytes_to_peer_attempt, __ATOMIC_RELAXED);

        // if timestamp at ingress is set and
        // we send at egress mconn->bytes_to_client which is at least of that received
        // at ingress - mconn->peer->tx_peer_rx_data_bytes_to_peer_attempt, at timestamp mconn->peer->tx_peer_rx_data_us
        if (tx_peer_rx_data_us &&
            (mconn->bytes_to_client >= tx_peer_rx_data_bytes_to_peer_attempt)) {

            // tx_peer_rx_data_max_us might be reset to zero at any time
            tx_peer_rx_data_max_us = __atomic_load_n(&mconn->tx_peer_rx_data_max_us, __ATOMIC_RELAXED);
            tx_peer_rx_data_diff_us = (int)(mconn->tx_data_us - tx_peer_rx_data_us);

            // save max diff between tx to client and rx from peer
            if (tx_peer_rx_data_diff_us > tx_peer_rx_data_max_us) {
                __atomic_store_n(&mconn->tx_peer_rx_data_max_us, tx_peer_rx_data_diff_us, __ATOMIC_RELAXED);
                mconn->tx_peer_rx_data_max_epoch_us = mconn->tx_data_us;
                mconn->tx_peer_rx_data_max_cnt = tx_peer_rx_data_cnt;
                mconn->tx_peer_rx_data_max_unblock_cnt = tx_peer_rx_data_max_unblock_cnt_curr;
            }
            // update histogram of diffs between tx to client and rx from peer.
            if (tx_peer_rx_data_diff_us < ZPN_MCONN_TRACK_TX_PEER_RX_DATA_20000_US) {
                mconn->tx_peer_rx_data_hist[0]++;
            } else if (tx_peer_rx_data_diff_us < ZPN_MCONN_TRACK_TX_PEER_RX_DATA_50000_US) {
                mconn->tx_peer_rx_data_hist[1]++;
            } else if (tx_peer_rx_data_diff_us < ZPN_MCONN_TRACK_TX_PEER_RX_DATA_100000_US) {
                mconn->tx_peer_rx_data_hist[2]++;
            } else if (tx_peer_rx_data_diff_us < ZPN_MCONN_TRACK_TX_PEER_RX_DATA_500000_US) {
                mconn->tx_peer_rx_data_hist[3]++;
            } else {
                mconn->tx_peer_rx_data_hist[4]++;
            }

            mconn->tx_peer_rx_data_max_unblock_cnt_curr = 0;

            // start measuring next diff between tx to client and rx from peer - make sure thread safe as it zeros in peer thread
            __atomic_store_n(&mconn->peer->tx_peer_rx_data_us, 0, __ATOMIC_RELAXED);
            __atomic_store_n(&mconn->peer->tx_peer_rx_data_cnt, 0, __ATOMIC_RELAXED);
        }
    }

    ZPN_DEBUG_MCONN("%p, track perf  egress tx_peer_rx_data(rx_epoch=%"PRId64", diff us: curr=%d, max (us=%d, epoch=%"PRId64", in-cnt=%d, unbl_cnt=%d), hist=%d,%d,%d,%d,%d), tx_data_unblock: curr(us=%d cnt=%d), max(us=%d, cnt=%d, tot=%d), tot(us=%"PRId64"), bytes_to_peer_attempt=%"PRId64", bytes_to_client=%"PRId64" ",
                    mconn,
                    tx_peer_rx_data_us, tx_peer_rx_data_diff_us, mconn->tx_peer_rx_data_max_us, mconn->tx_peer_rx_data_max_epoch_us, tx_peer_rx_data_cnt, mconn->tx_peer_rx_data_max_unblock_cnt,
                    mconn->tx_peer_rx_data_hist[0], mconn->tx_peer_rx_data_hist[1], mconn->tx_peer_rx_data_hist[2], mconn->tx_peer_rx_data_hist[3], mconn->tx_peer_rx_data_hist[4],
                    tx_data_unblock_us, tx_data_unblock_cnt, mconn->tx_data_unblock_max_us, mconn->tx_data_unblock_max_cnt, tx_peer_rx_data_max_unblock_cnt_curr, mconn->tx_data_unblock_tot_us,
                    tx_peer_rx_data_bytes_to_peer_attempt, mconn->bytes_to_client);
    return;
}

char *zpn_mconn_type_str(enum zpn_mconn_type type)
{
    switch(type) {
    case mconn_bufferevent_s:
        return "mconn_bufferevent_s";
    case mconn_bufferevent_c:
        return "mconn_bufferevent_c";
    case mconn_bufferevent_udp_s:
        return "mconn_bufferevent_udp_s";
    case mconn_bufferevent_udp_c:
        return "mconn_bufferevent_udp_c";
    case mconn_bufferevent_tun_s:
        return "mconn_bufferevent_tun_s";
    case mconn_bufferevent_tun_c:
        return "mconn_bufferevent_tun_c";
    case mconn_icmp_s:
        return "mconn_icmp_s";
    case mconn_icmp_c:
        return "mconn_icmp_c";
    case mconn_fohh_tlv_s:
        return "mconn_fohh_tlv_s";
    case mconn_fohh_tlv_c:
        return "mconn_fohh_tlv_c";
    case mconn_zrdt_tlv_s:
        return "mconn_zrdt_tlv_s";
    case mconn_zrdt_tlv_c:
        return "mconn_zrdt_tlv_c";
    case mconn_buffereventpair_s:
        return "mconn_buffereventpair_s";
    case mconn_buffereventpair_c:
        return "mconn_buffereventpair_c";
    default:
        return "mconn_unknown_type";
    }
}

static void zpn_mconn_dequeue_data_cb(struct zevent_base *base, void *cookie1, int64_t cookie2)
{
    struct zpn_mconn *mconn = cookie1;
    if (!mconn || !mconn->global_owner || !mconn->global_owner_calls) {
        return;
    }

    (mconn->global_owner_calls->lock)(mconn,
                                      mconn->self,
                                      mconn->global_owner,
                                      mconn->global_owner_key,
                                      mconn->global_owner_key_length);

    zpn_mconn_dequeue_data(mconn, 0);

    (mconn->global_owner_calls->unlock)(mconn,
                                        mconn->self,
                                        mconn->global_owner,
                                        mconn->global_owner_key,
                                        mconn->global_owner_key_length);
}

static int zpn_pipeline_exit_func(struct evbuffer* pipeline_exit_buffer, void* cookie)
{
    struct zpn_mconn* mconn = cookie;
    struct zevent_base *base;
    if (!zpn_mconn_transmit_buffer_exists(mconn)) {
        return ZPN_RESULT_NO_ERROR;
    }
    if (!zpn_mconn_get_transmit_buffer_len(mconn)) {
        return ZPN_RESULT_NO_ERROR;
    }

    base = fohh_thread_id_zevent_base(mconn->peer->fohh_thread_id);
    if (base) {
        zevent_base_call(base, zpn_mconn_dequeue_data_cb, mconn, 0);
    }
    return ZPN_RESULT_NO_ERROR;
}

static int zpn_pipeline_respond_func(struct evbuffer* pipeline_exit_buffer, void* cookie)
{
    struct zpn_mconn* mconn = cookie;
    struct zpn_mconn *peer_mconn = mconn->peer;
    size_t bytes_in_response = evbuffer_get_length(pipeline_exit_buffer);
    if (!bytes_in_response) {
        return ZPN_RESULT_NO_ERROR;
    }

    if (!zpn_mconn_transmit_buffer_exists(peer_mconn)) {
        if (!zpn_mconn_create_transmit_buffer(peer_mconn)) {
            ZPN_LOG(AL_ERROR, "%p: Could not allocate client tx data", peer_mconn);
            return ZPN_RESULT_NO_ERROR;
        }
    }
    if (peer_mconn->setup_pipeline_cb) {
        peer_mconn->setup_pipeline_cb(peer_mconn, peer_mconn->pipeline_cookie);
    }

    zpn_mconn_add_to_transmit_buffer(peer_mconn, pipeline_exit_buffer, evbuffer_get_length(pipeline_exit_buffer));
    return ZPN_RESULT_NO_ERROR;
}

static int zpn_pipeline_drop_func(struct evbuffer* pipeline_exit_buffer, void* cookie)
{
    struct zpn_mconn* mconn = cookie;
    zpn_mconn_discard_client_data(mconn, NULL);
    return ZPN_RESULT_NO_ERROR;
}

static int zpn_pipeline_error_func(struct evbuffer* pipeline_exit_buffer, void* cookie)
{
    struct zpn_mconn* mconn = cookie;
    zpn_mconn_terminate(mconn, 1, 0, MT_CLOSED_INTERNAL_ERROR, NULL);
    return ZPN_RESULT_NO_ERROR;
}

void zpn_mconn_setup_pipeline(struct zpn_mconn *mconn, char* pipeline_name)
{
    if (!zpn_mconn_transmit_buffer_exists(mconn)) {
        zpn_mconn_create_transmit_buffer(mconn);
    }
    zpn_mconn_transmit_buffer_setup_pipeline(mconn, zpn_pipeline_exit_func, zpn_pipeline_respond_func, zpn_pipeline_drop_func, zpn_pipeline_error_func, mconn, pipeline_name);
}


struct zpn_mconn *global_get_peer_no_op(void *mconn_base,
                                        void *mconn_self,
                                        void *global_owner,
                                        void *global_owner_key,
                                        size_t global_owner_key_length)
{
    return NULL;
}


int64_t global_get_customer_gid_no_op(void *mconn_base,
                                      void *mconn_self,
                                      void *global_owner,
                                      void *global_owner_key,
                                      size_t global_owner_key_length)
{
    return 0;
}
