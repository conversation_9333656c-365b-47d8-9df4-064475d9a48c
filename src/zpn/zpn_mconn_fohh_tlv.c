/*
 * zpn_mconn_fohh_tlv.c. Copyright (C) 2014 Zscaler Inc. All Rights Reserved.
 */

#include <sys/types.h>		/* XXX temporary hack to get u_ types */
#include <event2/event.h>
#include <event2/bufferevent_ssl.h>

#include <arpa/inet.h>
#include <netinet/in.h>
#include <netinet/udp.h>
#include <netinet/ip.h>
#include <netinet/ip6.h>
#include <netinet/tcp.h>

#include "zpath_lib/zpath_debug.h"
#include "argo/argo_hash.h"
#include "fohh/fohh.h"
#include "fohh/fohh_private.h"

#include "zpn/zpn_lib.h"
#include "zpn/zpn_rpc.h"
#include "zpn/zpn_mconn_fohh_tlv.h"
#include "zpn/zpn_fohh_worker.h"

#include "argo/argo_log.h"

#define FOHH_TLV_WINDOW_SIZE        (2*0x10*0x100000)  //32 MB
#define FOHH_TLV_MCONN_WINDOW_SIZE  (2*0x100000)  //2MB
#define FOHH_TLV_EXTENDED_WINDOW_SIZE (3*0x10*0x100000)  //48MB
#define FOHH_TLV_EXTENDED_MCONN_WINDOW_SIZE  (8*0x100000) //8MB
#define FOHH_TLV_MIN_WINDOW_SIZE  (2*0x100000)  //2MB

#define FOHH_TLV_FC_OUT_OF_SYNC_TIMEOUT_US   (1000000)       /* 1 seconds */

int fohh_tlv_window_size = FOHH_TLV_WINDOW_SIZE;
int fohh_tlv_mconn_window_size = FOHH_TLV_MCONN_WINDOW_SIZE;
static int fohh_tlv_low_watermark =  (FOHH_TLV_WINDOW_SIZE >> 1);
static int fohh_tlv_mconn_low_watermark = (FOHH_TLV_MCONN_WINDOW_SIZE >> 1);
int fohh_window_update_timeout_us = 500000;
int mconn_window_update_timeout_us = 500000;

static int debug_queue_check = 0;


/*those stats will be kept track at mconn level as well, but put it here for overall*/
static struct zpn_mconn_fohh_tlv_stats {                        /* _ARGO: object_definition */
    int64_t  window_update_no_peer_discard_data;                /* _ARGO: integer */
    int64_t  window_update_no_peer_after_dequeue_successfully;  /* _ARGO: integer */
    int64_t  window_update_no_peer_drop_because_fin_sent;       /* _ARGO: integer */
    int64_t  mconn_terminate_no_peer;                           /* _ARGO: integer */
    int64_t  mconn_terminate_check_data_buffer_no_peer;         /* _ARGO: integer */
    int64_t  mconn_not_clean_due_to_peer_still_data_buffered;   /* _ARGO: integer */
    int64_t  window_update_sucesss_discard_data;                /* _ARGO: integer */

} stats;

#include "zpn/zpn_mconn_fohh_tlv_compiled_c.h"
static struct argo_structure_description* zpn_mconn_fohh_tlv_stats_description;


static int64_t zpn_mconn_fohh_tlv_get_conn_incarnation(struct zpn_fohh_tlv *fohh_tlv);

int zpn_mconn_fohh_tlv_local_owner_sanity(struct zpn_fohh_tlv *fohh_tlv, struct zpn_mconn_fohh_tlv *mconn_fohh_tlv)
{
    if (!fohh_tlv ||
        !zpn_mconn_fohh_tlv_get_conn(fohh_tlv) ||
        (zpn_mconn_fohh_tlv_get_conn_incarnation(fohh_tlv) != fohh_connection_incarnation(zpn_mconn_fohh_tlv_get_conn(fohh_tlv)))) {
        ZPN_LOG(AL_CRITICAL, "QC: Local owner sanity check failed, fohh_tlv = %p", fohh_tlv);
        if (fohh_tlv) {
            ZPN_LOG(AL_CRITICAL, "QC: Local owner sanity check failed, fohh_tlv = %p, fohh_tlv->f_conn = %p, fohh_tlv->f_conn_down = %d, fohh_tlv->f_conn_incarnation = %ld, f_conn_incarnation = %ld",
                    fohh_tlv, zpn_mconn_fohh_tlv_get_conn(fohh_tlv), fohh_tlv->f_conn_down,
                    (long)zpn_mconn_fohh_tlv_get_conn_incarnation(fohh_tlv),
                    (long)(zpn_mconn_fohh_tlv_get_conn(fohh_tlv) ? fohh_connection_incarnation(zpn_mconn_fohh_tlv_get_conn(fohh_tlv)) : 0));
        } else {
            ZPN_LOG(AL_CRITICAL, "QC: Local owner sanity check failed, NULL fohh_tlv");
        }
        return ZPN_RESULT_ERR;
    }

    if (mconn_fohh_tlv && mconn_fohh_tlv->mconn.local_owner_incarnation) {
        if (mconn_fohh_tlv->mconn.local_owner_incarnation != zpn_mconn_fohh_tlv_get_conn_incarnation(fohh_tlv)) {
            ZPN_LOG(AL_CRITICAL, "QC: mconn_fohh_tlv->mconn_local_owner_incarnation = %ld, fohh_tlv->f_conn_incarnation = %ld do not match",
                    (long)mconn_fohh_tlv->mconn.local_owner_incarnation,
                    (long)zpn_mconn_fohh_tlv_get_conn_incarnation(fohh_tlv));
            return ZPN_RESULT_ERR;
        }
    }

    return ZPN_RESULT_NO_ERROR;
}

/* This function assume mtunnel and fohh_tlv lock are held */
static int zpn_mconn_fohh_tlv_append_to_busy_queue(struct zpn_fohh_tlv *fohh_tlv, struct zpn_mconn_fohh_tlv *mconn_fohh_tlv)
{
    struct zpn_mconn_fohh_tlv *mconn_fohh_tlv_temp = NULL;

    if (zpn_mconn_fohh_tlv_local_owner_sanity(fohh_tlv, mconn_fohh_tlv) != ZPN_RESULT_NO_ERROR) {
        ZPN_LOG(AL_CRITICAL, "QC: disconnect %p from local owner", mconn_fohh_tlv);
        return ZPN_RESULT_ERR;
    }

    if (mconn_fohh_tlv->busy != -1) {
        ZPN_LOG(AL_CRITICAL, "QC: mconn_fohh_tlv %p already in the list, busy = %d", mconn_fohh_tlv, mconn_fohh_tlv->busy);
        return ZPN_RESULT_ERR;
    }

    if (debug_queue_check) {
        TAILQ_FOREACH(mconn_fohh_tlv_temp, &(fohh_tlv->busy_mconn_list), mconn_list_entry) {
            if (mconn_fohh_tlv_temp == mconn_fohh_tlv) {
                ZPN_LOG(AL_CRITICAL, "QC: mconn_fohh_tlv %p already exist in busy list, do not insert", mconn_fohh_tlv);
                mconn_fohh_tlv->busy = 1;
                return ZPN_RESULT_NO_ERROR;
            }
        }
    }

    mconn_fohh_tlv->busy = 1;
    TAILQ_INSERT_TAIL(&(fohh_tlv->busy_mconn_list), mconn_fohh_tlv, mconn_list_entry);
    fohh_tlv->busy_list_size++;
    return ZPN_RESULT_NO_ERROR;
}

/* This function assume mtunnel and fohh_tlv lock are held */
static int zpn_mconn_fohh_tlv_remove_from_busy_queue(struct zpn_fohh_tlv *fohh_tlv, struct zpn_mconn_fohh_tlv *mconn_fohh_tlv)
{
    struct zpn_mconn_fohh_tlv *mconn_fohh_tlv_temp = NULL;
    int found = 0;

    if (mconn_fohh_tlv->busy != 1) {
        ZPN_LOG(AL_CRITICAL, "QC: mconn_fohh_tlv %p not in the busy list, busy = %d", mconn_fohh_tlv, mconn_fohh_tlv->busy);
    }

    if (debug_queue_check) {
        TAILQ_FOREACH(mconn_fohh_tlv_temp, &(fohh_tlv->busy_mconn_list), mconn_list_entry) {
            if (mconn_fohh_tlv_temp == mconn_fohh_tlv) {
                ZPN_DEBUG_MCONN("Found mconn_fohh_tlv to remove from busy list");
                found = 1;
                break;
            }
        }
    }

    if (debug_queue_check && !found) {
        ZPN_LOG(AL_CRITICAL, "QC: Cannot find mconn_fohh_tlv to remove from busy list");
        return ZPN_RESULT_ERR;
    }

    TAILQ_REMOVE(&(fohh_tlv->busy_mconn_list), mconn_fohh_tlv, mconn_list_entry);
    mconn_fohh_tlv->busy = -1;
    fohh_tlv->busy_list_size--;
    return ZPN_RESULT_NO_ERROR;
}

/* This function assume mtunnel and fohh_tlv lock are held */
static int zpn_mconn_fohh_tlv_append_to_idle_queue(struct zpn_fohh_tlv *fohh_tlv, struct zpn_mconn_fohh_tlv *mconn_fohh_tlv)
{
    struct zpn_mconn_fohh_tlv *mconn_fohh_tlv_temp = NULL;

    if (zpn_mconn_fohh_tlv_local_owner_sanity(fohh_tlv, mconn_fohh_tlv) != ZPN_RESULT_NO_ERROR) {
        ZPN_LOG(AL_CRITICAL, "QC: disconnect %p from local owner", mconn_fohh_tlv);
        return ZPN_RESULT_ERR;
    }

    if (mconn_fohh_tlv->busy != -1) {
        ZPN_LOG(AL_CRITICAL, "QC: mconn_fohh_tlv %p already in the list, busy = %d", mconn_fohh_tlv, mconn_fohh_tlv->busy);
        return ZPN_RESULT_ERR;
    }

    if (debug_queue_check) {
        TAILQ_FOREACH(mconn_fohh_tlv_temp, &(fohh_tlv->idle_mconn_list), mconn_list_entry) {
            if (mconn_fohh_tlv_temp == mconn_fohh_tlv) {
                ZPN_LOG(AL_CRITICAL, "QC: mconn_fohh_tlv %p already exist in idle list, do not insert", mconn_fohh_tlv);
                mconn_fohh_tlv->busy = 0;
                return ZPN_RESULT_NO_ERROR;
            }
        }
    }

    mconn_fohh_tlv->busy = 0;
    TAILQ_INSERT_TAIL(&(fohh_tlv->idle_mconn_list), mconn_fohh_tlv, mconn_list_entry);
    fohh_tlv->idle_list_size++;
    return ZPN_RESULT_NO_ERROR;
}

/* This function assume mtunnel and fohh_tlv lock are held */
static int zpn_mconn_fohh_tlv_remove_from_idle_queue(struct zpn_fohh_tlv *fohh_tlv, struct zpn_mconn_fohh_tlv *mconn_fohh_tlv)
{
    struct zpn_mconn_fohh_tlv *mconn_fohh_tlv_temp = NULL;
    int found = 0;

    if (mconn_fohh_tlv->busy != 0) {
        ZPN_LOG(AL_CRITICAL, "QC: mconn_fohh_tlv %p not in the idle list, busy = %d", mconn_fohh_tlv, mconn_fohh_tlv->busy);
    }

    if (debug_queue_check) {
        TAILQ_FOREACH(mconn_fohh_tlv_temp, &(fohh_tlv->idle_mconn_list), mconn_list_entry) {
            if (mconn_fohh_tlv_temp == mconn_fohh_tlv) {
                ZPN_DEBUG_MCONN("Found mconn_fohh_tlv to remove from idle list");
                found = 1;
                break;
            }
        }
    }

    if (debug_queue_check && !found) {
        ZPN_LOG(AL_CRITICAL, "QC: Cannot find mconn_fohh_tlv to remove from idle list");
        return ZPN_RESULT_ERR;
    }

    TAILQ_REMOVE(&(fohh_tlv->idle_mconn_list), mconn_fohh_tlv, mconn_list_entry);
    mconn_fohh_tlv->busy = -1;
    fohh_tlv->idle_list_size--;
    return ZPN_RESULT_NO_ERROR;
}

/* This function assume mtunnel and fohh_tlv lock are held */
static int zpn_mconn_fohh_tlv_idle_to_busy(struct zpn_fohh_tlv *fohh_tlv, struct zpn_mconn_fohh_tlv *mconn_fohh_tlv)
{
    /* If f_conn is down, don't move from idle to busy */
    if (fohh_tlv->f_conn_down) {
        return ZPN_RESULT_NO_ERROR;
    }

    zpn_mconn_fohh_tlv_remove_from_idle_queue(fohh_tlv, mconn_fohh_tlv);
    return zpn_mconn_fohh_tlv_append_to_busy_queue(fohh_tlv, mconn_fohh_tlv);
}

/* This function assume mtunnel and fohh_tlv lock are held */
static int zpn_mconn_fohh_tlv_append_to_queue(struct zpn_fohh_tlv *fohh_tlv, struct zpn_mconn_fohh_tlv *mconn_fohh_tlv)
{
#if OLD_SCHEME   /* Old scheme */
    TAILQ_INSERT_TAIL(&(fohh_tlv->mconn_list), mconn_fohh_tlv, mconn_list_entry);
    fohh_tlv->list_size++;
    if (!fohh_tlv->next_mconn) {
        fohh_tlv->next_mconn = TAILQ_FIRST(&(fohh_tlv->mconn_list));
    }
#else
    struct zpn_mconn *mconn = &(mconn_fohh_tlv->mconn);

    if (zpn_mconn_get_transmit_buffer_len(mconn)) {
        return zpn_mconn_fohh_tlv_append_to_busy_queue(fohh_tlv, mconn_fohh_tlv);
    } else {
        return zpn_mconn_fohh_tlv_append_to_idle_queue(fohh_tlv, mconn_fohh_tlv);
    }
#endif
}

/* This function assume mtunnel and fohh_tlv lock are held */
static int zpn_mconn_fohh_tlv_remove_from_queue(struct zpn_fohh_tlv *fohh_tlv, struct zpn_mconn_fohh_tlv *mconn_fohh_tlv)
{
#if OLD_SCHEME /* Old scheme */
    if (mconn_fohh_tlv == fohh_tlv->next_mconn) {
        if (TAILQ_NEXT(mconn_fohh_tlv, mconn_list_entry)) {
            /* we are not the last in the list */
            fohh_tlv->next_mconn = TAILQ_NEXT(mconn_fohh_tlv, mconn_list_entry);
        } else {
            /* we are the last in the list */
            fohh_tlv->next_mconn = NULL;
        }
    }

    TAILQ_REMOVE(&(fohh_tlv->mconn_list), mconn_fohh_tlv, mconn_list_entry);
    fohh_tlv->list_size--;

    if (!fohh_tlv->next_mconn) {
        fohh_tlv->next_mconn = TAILQ_FIRST(&(fohh_tlv->mconn_list));
    }
#else
    if (mconn_fohh_tlv->busy == 1) {
        return zpn_mconn_fohh_tlv_remove_from_busy_queue(fohh_tlv, mconn_fohh_tlv);
    } else if (mconn_fohh_tlv->busy == 0) {
        return zpn_mconn_fohh_tlv_remove_from_idle_queue(fohh_tlv, mconn_fohh_tlv);
    } else {
        /* mconn is not on any queue */
        return ZPN_RESULT_NO_ERROR;
    }
#endif
}

static struct zpn_mconn_fohh_tlv *
zpn_mconn_fohh_tlv_lookup_tag(struct zpn_fohh_tlv *fohh_tlv, int32_t tag, int64_t *incarnation)
{
    struct zpn_mconn_fohh_tlv *res;

    ZPATH_MUTEX_LOCK(&(fohh_tlv->lock), __FILE__, __LINE__);
    res = argo_hash_lookup(fohh_tlv->tags,
                           &tag,
                           sizeof(tag),
                           NULL);
    if (res) {
        struct zpn_mconn *mconn = &(res->mconn);
        *incarnation = (mconn->global_owner_calls->incarnation)(mconn,
                                                                mconn->self,
                                                                mconn->global_owner,
                                                                mconn->global_owner_key,
                                                                mconn->global_owner_key_length);
    }
    ZPATH_MUTEX_UNLOCK(&(fohh_tlv->lock), __FILE__, __LINE__);
    return res;
}


int zpn_mconn_fohh_tlv_bind_cb(void *mconn_base,
                               void *mconn_self,
                               void *owner,
                               void *owner_key,
                               size_t owner_key_length,
                               int64_t *owner_incarnation)
{
    struct zpn_mconn_fohh_tlv *mconn_fohh_tlv = mconn_base;
    struct zpn_fohh_tlv *fohh_tlv = owner;
    int res;

    if (mconn_fohh_tlv->tag_id) {
        ZPN_LOG(AL_ERROR, "bind when already bound");
        return ZPN_RESULT_ERR;
    }

    res = argo_hash_store(fohh_tlv->tags,
                          owner_key,
                          owner_key_length,
                          0,
                          mconn_fohh_tlv);
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not store: %s", zpn_result_string(res));
        return res;
    }

    zpn_mconn_fohh_tlv_append_to_queue(fohh_tlv, mconn_fohh_tlv);

    mconn_fohh_tlv->tag_id = *((int32_t *) owner_key);

    /* Inherit flow control flag from FOHH connection */
    mconn_fohh_tlv->remote_fc_status = fohh_tlv->remote_fc_status;

    if (owner_incarnation) {
        *owner_incarnation = fohh_connection_incarnation(zpn_mconn_fohh_tlv_get_conn(fohh_tlv));
    }

    return ZPN_RESULT_NO_ERROR;
}

static int zpn_fohh_tlv_pipeline_latency_get_enabled(struct zpn_fohh_tlv *fohh_tlv,
                                                     struct zpn_mconn *mconn)
{
  assert(fohh_tlv);
  struct fohh_connection *f_conn = zpn_mconn_fohh_tlv_get_conn(fohh_tlv);
  assert(f_conn);
  assert(mconn);
  return (mconn->drop_udp_framed_data && fohh_get_pipeline_latency_trace_config(f_conn));
}

/*
 * If we come here, either we don't have any data waiting to be transmitted or drop_buffered_data = 1.
 * So there is no reason to hang on to the local owner. Just delink the mconn from its local owner.
 */
int zpn_mconn_fohh_tlv_unbind_cb(void *mconn_base,
                                 void *mconn_self,
                                 void *owner,
                                 void *owner_key,
                                 size_t owner_key_length,
                                 int64_t owner_incarnation,
                                 int drop_buffered_data,
                                 int dont_propagate,
                                 const char *err)
{
    struct zpn_mconn_fohh_tlv *mconn_fohh_tlv = mconn_base;
    struct zpn_fohh_tlv *fohh_tlv = owner;
    int res;

    if (zpn_mconn_fohh_tlv_local_owner_sanity(fohh_tlv, NULL) != ZPN_RESULT_NO_ERROR) {
        ZPN_LOG(AL_ERROR, "local owner is insane");
        return ZPN_RESULT_ERR;
    }

    if (!dont_propagate) {
        res = zpn_mconn_forward_mtunnel_end(&(mconn_fohh_tlv->mconn), err, drop_buffered_data);
        if (res) {
            ZPN_LOG(AL_ERROR, "Could not send mtunnel_end. Is this really a problem?");
        }
    }

    if (mconn_fohh_tlv->disabled_read_socket) {

        if (fohh_tlv->tunnel_pr) {
            ZPN_DEBUG_MCONN("zpn_mconn_fohh_tlv_unbind_cb tag_id : %d , enabling fohh_tlv read socket", mconn_fohh_tlv->tag_id);
            fohh_connection_enable_read(zpn_mconn_fohh_tlv_get_conn(fohh_tlv));

        }
    }

    if (fohh_tlv->tags && mconn_fohh_tlv->tag_id) {
        res = argo_hash_remove(fohh_tlv->tags,
                               owner_key,
                               owner_key_length,
                               mconn_fohh_tlv);

        if (res) {
            ZPN_LOG(AL_ERROR, "Cannot find tag to remove from fohh tag hash: %s", zpn_result_string(res));
        }
    }

    zpn_mconn_fohh_tlv_remove_from_queue(fohh_tlv, mconn_fohh_tlv);

    fohh_tlv->deq_bytes += mconn_fohh_tlv->mconn.dropped_untransmitted_bytes;
    fohh_connection_set_stats_fohh_deq_bytes(fohh_tlv->tlv.conn.f_conn,fohh_tlv->deq_bytes);

    mconn_fohh_tlv->tag_id = 0;
    struct zpn_mconn *mconn = &(mconn_fohh_tlv->mconn);
    // we could skip the config override check here without any harm
    if (zpn_fohh_tlv_pipeline_latency_get_enabled(fohh_tlv, mconn)) {
      if (mconn == fohh_tlv->pipeline_latency_mconn)
        fohh_tlv->pipeline_latency_mconn = NULL;
    }

    return ZPN_RESULT_NO_ERROR;
}


void zpn_mconn_fohh_tlv_lock_cb(void *mconn_base,
                                void *mconn_self,
                                void *owner,
                                void *owner_key,
                                size_t owner_key_length)
{
    struct zpn_fohh_tlv *fohh_tlv = owner;

    ZPATH_MUTEX_LOCK(&(fohh_tlv->lock), __FILE__, __LINE__);
}


void zpn_mconn_fohh_tlv_unlock_cb(void *mconn_base,
                                  void *mconn_self,
                                  void *owner,
                                  void *owner_key,
                                  size_t owner_key_length)
{
    struct zpn_fohh_tlv *fohh_tlv = owner;

    ZPATH_MUTEX_UNLOCK(&(fohh_tlv->lock), __FILE__, __LINE__);
}

int udp_peek_packet_length(struct evbuffer *evbuf)
{
    unsigned char *mem;

    mem = evbuffer_pullup(evbuf, sizeof(struct udphdr));
    if (mem) {
        struct udphdr *header = (struct udphdr *)mem;
#if (defined(__APPLE__) && defined(__MACH__)) || defined(__FreeBSD__)
        return ntohs(header->uh_ulen);
#else
        return ntohs(header->len);
#endif
    } else {
        return 0;
    }
}

int icmpv6_peek_packet_length(struct evbuffer *evbuf)
{
    unsigned char *mem;

    mem = evbuffer_pullup(evbuf, sizeof(struct ip6_hdr));
    if (mem) {
        struct ip6_hdr *ip6h = (struct ip6_hdr *)mem;
        return (sizeof(struct ip6_hdr) + ntohs(ip6h->ip6_plen));
    } else {
        return 0;
    }
}

int icmp_peek_packet_length(struct evbuffer *evbuf)
{
    unsigned char *mem;

    mem = evbuffer_pullup(evbuf, sizeof(struct ip));
    if (mem) {
        struct ip *header = (struct ip *)mem;
        return ntohs(header->ip_len);
    } else {
        return 0;
    }
}

size_t get_transmit_len(struct evbuffer *buf, size_t len, int proto, int double_encrypt, int chunk_size)
{
    size_t tx_len = len;

    if (!no_udp_packetize_on_tlv && (proto == IPPROTO_UDP) && !double_encrypt) {
        tx_len = udp_peek_packet_length(buf);
    } else if (proto == IPPROTO_ICMP) {
        tx_len = icmp_peek_packet_length(buf);
    } else if (proto == IPPROTO_ICMPV6) {
        tx_len = icmpv6_peek_packet_length(buf);
    } else {
        if (chunk_size) {
            tx_len = (len > (chunk_size*1024)) ? (chunk_size*1024) : len;
        }
        tx_len = (tx_len > ZPN_MCONN_MTU) ? ZPN_MCONN_MTU : tx_len;
    }

    return tx_len;
}

#define FC_CONTINUOUS_TIMEOUT_SECOND_TO_MICROSECOND (1000000)
static void zpn_mconn_fohh_tlv_continuous_fc_recover_evaluate(struct zpn_fohh_tlv *fohh_tlv, struct zpn_mconn_fohh_tlv *mconn_fohh_tlv)
{
    struct fohh_connection *f_conn = fohh_tlv->tlv.conn.f_conn;
    struct zpn_mconn *mconn = &(mconn_fohh_tlv->mconn);
    int is_reset_recover_enabled = fohh_connection_is_fc_reset_recover_enabled(f_conn);

    if (!is_reset_recover_enabled) {
        return;
    }

    int64_t fc_continuous_timeout_us;
    int64_t fc_continuous_timeout_s;
    double fc_threshold_percent;
    int64_t delta_on_last_seen_kick_flow_control_us;
    int threshold_value;

    fc_continuous_timeout_s = fohh_connection_get_fc_continous_timeout_interval_s(f_conn);
    fc_continuous_timeout_us = fc_continuous_timeout_s * FC_CONTINUOUS_TIMEOUT_SECOND_TO_MICROSECOND;

    fc_threshold_percent = fohh_connection_get_fc_continuous_threshold_percent(f_conn);
    if (!fohh_tlv->fohh_tlv_last_seen_kick_flow_us) {
        /*very first time*/
        delta_on_last_seen_kick_flow_control_us = 0;
    } else {
        delta_on_last_seen_kick_flow_control_us = epoch_us() - fohh_tlv->fohh_tlv_last_seen_kick_flow_us;
    }

    threshold_value = (int) (fc_continuous_timeout_s * fc_threshold_percent) / 100;

    if (delta_on_last_seen_kick_flow_control_us <= fc_continuous_timeout_us) {

        /*check if we have exceeded threshold in current monitoring interval*/
        if (++fohh_tlv->continuous_kick_flow_control_seen >= threshold_value) {

            if (!fohh_tlv->kick_flow_control_reset) {
                fohh_tlv->kick_flow_control_reset = 1;
                fohh_connection_set_kick_flow_control_reset(fohh_tlv->tlv.conn.f_conn, fohh_tlv->kick_flow_control_reset);
                ZPN_LOG(AL_ERROR, "%p tag= %d, seen continous kick flow control just crossed threshold value %d over last %ld second interval, "
                                  "seting fohh_tlv->kick_flow_control_reset = 1, seen kick flow total %"PRId64" "
                                  "fc_threshold_percent %.2f",
                                                  mconn,
                                                  mconn_fohh_tlv->tag_id,
                                                  threshold_value,
                                                  (long) fc_continuous_timeout_s,
                                                  fohh_tlv->continuous_kick_flow_control_seen,
                                                  fc_threshold_percent);
            } else {
                ZPN_DEBUG_FC_MCONN("%p tag= %d, seen repeated flow control total of %"PRId64" times already crossed threshold %.2f percent, threshold %d evaluated over interval %ld seconds, waiting for reset ",
                                                  mconn,
                                                  mconn_fohh_tlv->tag_id,
                                                  fohh_tlv->continuous_kick_flow_control_seen,
                                                  fc_threshold_percent,
                                                  threshold_value,
                                                  (long) fc_continuous_timeout_s);
            }


        } else {
            ZPN_DEBUG_FC_MCONN("%p tag= %d, start seeing repeated flow control total of %"PRId64" times but not yet cross threshold %.2f percent, threshold %d evaluated over interval %ld seconds ",
                                                  mconn,
                                                  mconn_fohh_tlv->tag_id,
                                                  fohh_tlv->continuous_kick_flow_control_seen,
                                                  fc_threshold_percent,
                                                  threshold_value,
                                                  (long) fc_continuous_timeout_s);
        }
    } else {

        ZPN_DEBUG_FC_MCONN("%p tag= %d, seen kick flow first time over last %ld second interval, resetting the counter, starting new monitor interval"
                           "previous continuous_kick_flow_control_seen %"PRId64" ",
                                mconn,
                                mconn_fohh_tlv->tag_id,
                                (long) fc_continuous_timeout_s,
                                fohh_tlv->continuous_kick_flow_control_seen);
        fohh_tlv->kick_flow_control_reset = 0;
        fohh_tlv->continuous_kick_flow_control_seen = 0;
        fohh_connection_set_kick_flow_control_reset(fohh_tlv->tlv.conn.f_conn, fohh_tlv->kick_flow_control_reset);
    }


}

// Note: UDP uses packet marshalling and this function leverages this to trace
// Threading interaction:
// Only owner thread can set new latency trace mconn
// broker monitor thread can unset latency mconn; when this happen, owner thread can set a new trace mconn
// RX threads cannot set or unset latency mconn to trace
// first packet on mconn is not counted
static void zpn_fohh_tlv_pipeline_latency_udp_set(struct zpn_fohh_tlv *fohh_tlv, int stage,
                                                  struct zpn_mconn *mconn, struct fohh_connection *f_conn,
                                                  int64_t rx_data_us)
{
  if (!fohh_tlv->pipeline_latency_mconn) {
    fohh_tlv->pipeline_latency_mconn = mconn;
  } else if (mconn != fohh_tlv->pipeline_latency_mconn) {
    return;
  }
  assert(stage < latency_inspection_stage_num);
  struct evbuffer *tx_buf_time_buf = zpn_mconn_get_transmit_buf_time_buf(mconn);
  if (!tx_buf_time_buf)
    return;
  int64_t base_time;
  if (stage == latency_inspection_stage_rx_0) {
	base_time = rx_data_us;
  } else {
    if (stage == latency_inspection_stage_tx_0)
      evbuffer_copyout(tx_buf_time_buf, &(mconn->rx_data_us_tx_cnxt), sizeof(mconn->rx_data_us_tx_cnxt));
    else if (stage == latency_inspection_stage_last)
      evbuffer_drain(tx_buf_time_buf, sizeof(mconn->rx_data_us_tx_cnxt));
    base_time = mconn->rx_data_us_tx_cnxt;
  }
  if (!base_time)
    return; // not set yet, hence skip recording a time measurement for this first packet

  int64_t now = epoch_us();
  int64_t delta_time = now - base_time;
  if (delta_time < 0) {
    ZPN_DEBUG_MCONN_LATENCY("Unexpected negative pipeline latency: %s; stage: %d;"
                            " base time: %"PRId64"; now %"PRId64" time buf len: %zu",
                            f_conn->connection_description, stage, base_time, now,
                            zpn_mconn_get_transmit_buf_time_buf_len(mconn));
    return;
  }
  fohh_tlv->pipeline_latency[stage] = delta_time;
  ZPN_DEBUG_MCONN_LATENCY("Pipeline latency: %s; stage: %d; delta time: %"PRId64"; now %"PRId64" time buf len: %zu",
                          f_conn->connection_description, stage, delta_time, now,
                          zpn_mconn_get_transmit_buf_time_buf_len(mconn));
  if (stage == latency_inspection_stage_last) {
    if (delta_time > fohh_tlv->pipeline_latency_max)
      fohh_tlv->pipeline_latency_max = delta_time;
    if (delta_time < ZPN_FOHH_PIPELINE_LATENCY_BKT_0) {
      fohh_tlv->pipeline_latency_hist[0]++;
    } else if (delta_time < ZPN_FOHH_PIPELINE_LATENCY_BKT_1) {
      fohh_tlv->pipeline_latency_hist[1]++;
    } else if (delta_time < ZPN_FOHH_PIPELINE_LATENCY_BKT_2) {
      fohh_tlv->pipeline_latency_hist[2]++;
    } else if (delta_time < ZPN_FOHH_PIPELINE_LATENCY_BKT_3) {
      fohh_tlv->pipeline_latency_hist[3]++;
    } else if (delta_time < ZPN_FOHH_PIPELINE_LATENCY_BKT_4) {
      fohh_tlv->pipeline_latency_hist[4]++;
    } else if (delta_time < ZPN_FOHH_PIPELINE_LATENCY_BKT_5) {
      fohh_tlv->pipeline_latency_hist[5]++;
    } else if (delta_time < ZPN_FOHH_PIPELINE_LATENCY_BKT_6) {
      fohh_tlv->pipeline_latency_hist[6]++;
    } else if (delta_time < ZPN_FOHH_PIPELINE_LATENCY_BKT_7) {
      fohh_tlv->pipeline_latency_hist[7]++;
    } else if (delta_time < ZPN_FOHH_PIPELINE_LATENCY_BKT_8) {
      fohh_tlv->pipeline_latency_hist[8]++;
    } else if (delta_time < ZPN_FOHH_PIPELINE_LATENCY_BKT_9) {
      fohh_tlv->pipeline_latency_hist[9]++;
    } else {
      fohh_tlv->pipeline_latency_hist[10]++;
    }
  }
}

// TX thread context
static void zpn_fohh_tlv_pipeline_latency_udp_set_wrapper(struct zpn_fohh_tlv *fohh_tlv, int stage,
                                                          struct zpn_mconn *mconn)
{
  if (!zpn_fohh_tlv_pipeline_latency_get_enabled(fohh_tlv, mconn))
    return;
  struct fohh_connection *f_conn = zpn_mconn_fohh_tlv_get_conn(fohh_tlv);
  zpn_fohh_tlv_pipeline_latency_udp_set(fohh_tlv, stage, mconn, f_conn, 0);
}

// RX threads context
static void zpn_fohh_tlv_rx_pipeline_latency_udp_set(struct zpn_fohh_tlv *fohh_tlv,
                                                     struct zpn_mconn_fohh_tlv *mconn_fohh_tlv,
                                                     int64_t rx_data_us)
{
  assert(mconn_fohh_tlv);
  struct zpn_mconn *mconn = &(mconn_fohh_tlv->mconn);
  if (!zpn_fohh_tlv_pipeline_latency_get_enabled(fohh_tlv, mconn))
    return;
  if (mconn == fohh_tlv->pipeline_latency_mconn) {
    evbuffer_add(zpn_mconn_get_transmit_buf_time_buf(mconn), &(rx_data_us), sizeof(rx_data_us));
    struct fohh_connection *f_conn = zpn_mconn_fohh_tlv_get_conn(fohh_tlv);
    zpn_fohh_tlv_pipeline_latency_udp_set(fohh_tlv, latency_inspection_stage_rx_0, mconn, f_conn, rx_data_us);
  }
}

static int zpn_fohh_tlv_data_write(struct zpn_mconn_fohh_tlv *mconn_fohh_tlv,
                                   struct zpn_fohh_tlv *fohh_tlv,
                                   int64_t owner_incarnation,
                                   struct evbuffer *buf,
                                   size_t buf_len)
{
    int buf_len_exceed = 0;
    int chunk_size = fohh_get_chunk_size(zpn_mconn_fohh_tlv_get_conn(fohh_tlv));
    int allowed_chunk = fohh_get_allowed_chunk(zpn_mconn_fohh_tlv_get_conn(fohh_tlv));
    size_t allowed_bytes;
    // set to 100,000,000 so it is clear in logs that these values were not set by the algorithm
    int64_t mconn_allowed = 100000000;
    int64_t fohh_allowed = 100000000;

    size_t len;
    int res = ZPN_RESULT_NO_ERROR;

    int32_t header[2];

    int total_xmt = 0;
    int total_drop = 0;
    uint8_t block_fohh = 0;
    uint8_t block_evbuf = 0;

    struct zpn_mconn *mconn = &(mconn_fohh_tlv->mconn);
    assert(mconn);
    int ip_proto = (mconn->global_owner_calls->ip_proto)(mconn,
                                                         mconn->self,
                                                         mconn->global_owner,
                                                         mconn->global_owner_key,
                                                         mconn->global_owner_key_length);

    int double_encrypt = (mconn->global_owner_calls->double_encrypt)(mconn,
                                                                     mconn->self,
                                                                     mconn->global_owner,
                                                                     mconn->global_owner_key,
                                                                     mconn->global_owner_key_length);
    int64_t customer_gid = (mconn->global_owner_calls->get_customer_gid)(mconn,
                                                                         mconn->self,
                                                                         mconn->global_owner,
                                                                         mconn->global_owner_key,
                                                                         mconn->global_owner_key_length);

    if (!allowed_chunk) allowed_chunk = 8;

    if (ip_proto == IPPROTO_UDP || ip_proto == IPPROTO_ICMP || ip_proto == IPPROTO_ICMPV6) {
        allowed_bytes = 8*ZPN_MCONN_MTU;
    } else {
        if (!chunk_size) {
            allowed_bytes = allowed_chunk * ZPN_MCONN_MTU;
        } else {
            allowed_bytes = allowed_chunk * chunk_size * 1024;
        }
    }

    /* If flow control is enabled for the mconn and the outer tunnel, find out how much we are allowed to send
     *
     * This is the FOHH flow control logic where we are checking if we have credit to send to remote.
     * flow control logic is only required if it is enabled at 3 level:
     *
     *          1) fohh_control_enabled - global level, it is set to enabled by default. we can flip this variable through command line binary,
     *                                    this one is mostly for dev purpose.
     *          2) fohh_tlv->remote_fc_status - FOHH TLS Connection level, this flag controls at the whole FOHH TLS conneciton level.
     *                                          if disabled flow control at this level, all mtunnel using this TLS connection has flow control disabled.
     *          3) mconn_fohh_tlv->remote_fc_status - mtunnel level, this flag only control whether we want flow control for particular mtunnel mconn.
     *
     */
    if (flow_control_enabled && fohh_tlv->remote_fc_status == flow_ctrl_enabled && mconn_fohh_tlv->remote_fc_status == flow_ctrl_enabled) {
        /*
         *  In current design fohh_tlv structure is always handled on the same fohh thread that the fohh connection gets created.
         *  the only case we access fohh_tlv and its not on its dedicated thread is
         *  when we are accessing fohh_tlv->busy_mconn_list and fohh_tlv->idle_mconn_list
         *
         *  We do not have to acquire fohh_tlv->lock based on current design, but in the future,
         *  if this code path can be triggered on different thread other than fohh_tlv orignal thread, lock is required.
         *
         *  for now if we are acquiring the lock, ill will introduce performance impact.
         */
        /* coverity[missing_lock: FALSE] */
        fohh_allowed = fohh_tlv->tx_limit - fohh_tlv->tx_data;

        if (fohh_allowed <= 0) {
            /* We ran out of credit to send on fohh connection */
            if (!fohh_tlv->fc_blocked_timestamp) {
                /* First time blocked, just note the time */
                fohh_tlv->fc_blocked_timestamp = epoch_us();
                fohh_tlv->fc_blocked_timestamp_initial = fohh_tlv->fc_blocked_timestamp;
            } else if (epoch_us() - fohh_tlv->fc_blocked_timestamp < FOHH_TLV_FC_OUT_OF_SYNC_TIMEOUT_US) {
                /* Don't do anything if blocked less than FOHH_TLV_FC_OUT_OF_SYNC_TIMEOUT_US */
            } else {
                /* Blcoked longer than FOHH_TLV_FC_OUT_OF_SYNC_TIMEOUT_US, send something to kick start transmit */
                ZPN_LOG(AL_ERROR, "%p tag= %d Kick flow control to connection %s, as it is blocked for more than %ld seconds, "
                                   "last tx_limit update=%ld us, fohh_allowed: %"PRId64", fohh_tlv->tx_limit: %"PRId64" fohh_tlv->tx_data : %"PRId64", allowed_bytes: %zu, chunk_size: %d, allowed_chunk: %d",
                        mconn,
                        mconn_fohh_tlv->tag_id,
                        fohh_description(zpn_mconn_fohh_tlv_get_conn(fohh_tlv)),
                        (long)FOHH_TLV_FC_OUT_OF_SYNC_TIMEOUT_US/1000000,
                        (long)(fohh_tlv->tx_limit_update_us - epoch_us()),
                        fohh_allowed,
                        fohh_tlv->tx_limit,
                        fohh_tlv->tx_data,
                        allowed_bytes,
                        chunk_size,
                        allowed_chunk);
                fohh_allowed = allowed_bytes;
                fohh_tlv->fc_blocked_timestamp = epoch_us();

                zpn_mconn_fohh_tlv_continuous_fc_recover_evaluate(fohh_tlv, mconn_fohh_tlv);
                fohh_tlv->fohh_tlv_last_seen_kick_flow_us = epoch_us();
            }
        } else {
            /* We are not blocked, clear the block timestamp */
            if (fohh_tlv->fc_blocked_timestamp) {
                fohh_tlv->fohh_fc_blocked_time = epoch_us() - fohh_tlv->fc_blocked_timestamp;
                fohh_connection_set_stats_fohh_fc_blocked_time(fohh_tlv->tlv.conn.f_conn, fohh_tlv->fohh_fc_blocked_time);
                fohh_tlv->tot_fohh_fc_blocked_time += fohh_tlv->fohh_fc_blocked_time;
                if(fohh_tlv->max_fohh_fc_blocked_time < fohh_tlv->fohh_fc_blocked_time) {
                    fohh_tlv->max_fohh_fc_blocked_time = fohh_tlv->fohh_fc_blocked_time;
                    fohh_connection_set_stats_fohh_fc_blocked_max_time(fohh_tlv->tlv.conn.f_conn, fohh_tlv->fohh_fc_blocked_time);
                }
            }
            fohh_tlv->fc_blocked_timestamp = 0;
            fohh_tlv->fc_blocked_timestamp_initial = 0;

        }

        if (allowed_bytes > fohh_allowed) allowed_bytes = fohh_allowed;

        mconn_allowed = mconn_fohh_tlv->tx_limit - mconn_fohh_tlv->tx_data;
        if (allowed_bytes > mconn_allowed) allowed_bytes = mconn_allowed;

        ZPN_DEBUG_MCONN(
                "%p Send data, flow control enabled %s - TLV, client transmit buffer=%p, tag= %d, allowed_bytes=%ld, "
                "buf_len=%ld fohh_allowed=%ld tx_limit=%" PRId64 " tx_data=%" PRId64 ,
                mconn, fohh_allowed>0?"not blocked":"blocked",
                zpn_mconn_get_transmit_buffer(mconn), mconn_fohh_tlv->tag_id, (long)allowed_bytes, (long)buf_len,
                (long)fohh_allowed, mconn_fohh_tlv->tx_limit, mconn_fohh_tlv->tx_data);
    }

    if (mconn->drop_tx) {
        ZPN_DEBUG_MCONN("%p tag=%d, Drop all data because down stream won't accept", mconn, mconn_fohh_tlv->tag_id);
        evbuffer_drain(buf, buf_len);
        total_drop = buf_len;
        goto exit;
    }

    /* We don't have any credit to send */
    if (!allowed_bytes) {
        ZPN_DEBUG_MCONN("%p tag= %d Don't have any credit to send to %s, fohh_allowed=%ld, mconn_allowed=%ld tx_limit=%"PRId64" tx_data=%"PRId64,
                         mconn,
                         mconn_fohh_tlv->tag_id,
                         fohh_description(zpn_mconn_fohh_tlv_get_conn(fohh_tlv)),
                         (long)fohh_allowed, (long)mconn_allowed,mconn_fohh_tlv->tx_limit ,mconn_fohh_tlv->tx_data);
        mconn_fohh_tlv->fc_blocked = 1;
        __sync_fetch_and_add_8(&(fohh_tlv->fohh_fc_blocked), 1);
        fohh_connection_set_stats_fohh_fc_blocked(fohh_tlv->tlv.conn.f_conn,fohh_tlv->fohh_fc_blocked);
        return FOHH_RESULT_WOULD_BLOCK;
    }

    if (buf_len > allowed_bytes) {
        buf_len = allowed_bytes;
        buf_len_exceed = 1;
        ZPN_DEBUG_MCONN("%p tag= %d - buf len exceed, actual xmit %d",mconn, mconn_fohh_tlv->tag_id, (int)buf_len);
    }

    if (!mconn_fohh_tlv->tag_id) {
        /* It's diconnected- drop data. */
        ZPN_DEBUG_MCONN("%p Dropping data- transmit on closed connection",mconn);
        return ZPN_RESULT_NO_ERROR;
    }

    header[0] = htonl(mconn_fohh_tlv->tag_id);

    while ((len = buf_len)) {

        len = get_transmit_len(buf, len, ip_proto, double_encrypt, chunk_size);
        if ((len > buf_len) || (len <= 0)) {
            ZPN_DEBUG_MCONN("Unexpected len: %s: mconn: %p, tag= %d buf: %p, buf_len: %zu, len: %zu, ip_proto: %d, double_encrypt: %d, chunk_size: %d",
                            __func__, mconn, mconn_fohh_tlv->tag_id, buf, buf_len, len, ip_proto, double_encrypt, chunk_size);
            break;
        }
        buf_len -= len;

        header[1] = htonl(len);

        if (evbuffer_prepend(buf, header, sizeof(header))) {
            ZPN_LOG(AL_CRITICAL, "%p tag= %d Could not prepend", mconn, mconn_fohh_tlv->tag_id);
            return ZPN_RESULT_ERR;
        }

        if (ip_proto == IPPROTO_ICMP && mconn->peer) {
          ZPN_DEBUG_MCONN_ICMP("DEBUG ICMP call fohh_tlv_send_raw: %s: ip_src 0x%x; ip_dst 0x%x; icmp ID %u; icmp seq# %u; now %"PRId64,
                               __func__, ntohl(mconn->peer->icmp_state.s_ip.s_addr), ntohl(mconn->peer->icmp_state.a_ip.s_addr),
                               ntohs(mconn->peer->icmp_state.id), ntohs(mconn->peer->icmp_state.seq), epoch_us());
        }
        zpn_fohh_tlv_pipeline_latency_udp_set_wrapper(fohh_tlv, latency_inspection_stage_tx_2, mconn);

        // ZPN_DEBUG_MCONN("%p: fohh_tlv_send_raw (chunk) - tag = %d, len = %ld", mconn, (int)mconn_fohh_tlv->tag_id, (long)len);
        block_fohh = block_evbuf = 0;
        res = fohh_tlv_send_raw(zpn_mconn_fohh_tlv_get_conn(fohh_tlv),
                                owner_incarnation,
                                buf,
                                len + sizeof(header),
                                &block_fohh,
                                &block_evbuf);

        ZPN_DEBUG_MCONN("%s: mconn: %p result from fohh_tlv_send_raw: tag= %d, len: %ld, buf: %p, ret: %s",
                        __func__, mconn, mconn_fohh_tlv->tag_id, (long)len, buf, zpn_result_string(res));
        if (res) {
            if (res != FOHH_RESULT_WOULD_BLOCK) {
                ZPN_LOG(AL_ERROR, "Error transmit raw: %s", zpn_result_string(res));
            } else {
                /* If we return FOHH_RESULT_WOULD_BLOCK, the caller will buffer the
                 * the data, we need to remove the tlv header we added to return
                 * the buffer to its original form
                 */
                if (block_fohh) {
                    __sync_fetch_and_add_8(&(fohh_tlv->fohh_data_write_fohh_blocked), 1);
                }
                if (block_evbuf) {
                    __sync_fetch_and_add_8(&(fohh_tlv->fohh_data_write_evbuf_blocked), 1);
                }
                __sync_fetch_and_add_8(&(fohh_tlv->fohh_data_write_blocked), 1);
                fohh_connection_set_stats_fohh_data_write_blocked(fohh_tlv->tlv.conn.f_conn,fohh_tlv->fohh_data_write_blocked,
                                    fohh_tlv->fohh_data_write_fohh_blocked,
                                    fohh_tlv->fohh_data_write_evbuf_blocked);
                ZPN_DEBUG_MCONN("%p tag= %d FOHH_RESULT_WOULD_BLOCK: Removing the tlv header we added", mconn, mconn_fohh_tlv->tag_id);
                evbuffer_remove(buf, header, sizeof(header));

                /* curr number of attempts to schedule packet at egress mconn - number of WOULD_BLOCK in sequence*/
                mconn->tx_data_unblock_cnt++;

                mconn->tx_peer_rx_data_max_unblock_cnt_curr++;
            }

            //channel_blocked = 1;
            goto exit;
        }

        mconn_fohh_tlv->mconn.bytes_to_client += len;
        mconn_fohh_tlv->tx_data += len;
        total_xmt += len;
        fohh_tlv->tx_data_us = epoch_us();
    }

    res = buf_len_exceed ? FOHH_RESULT_WOULD_BLOCK : ZPN_RESULT_NO_ERROR;

exit:
    if (total_xmt) {
        /* We sent out something */
        if (!mconn->tx_data_us_b) {
            mconn->tx_data_us_b = epoch_us();
        }
        mconn->tx_data_us = epoch_us();

        zpn_mconn_track(mconn);
        zpn_mconn_track_perf_egress(mconn);
        if (ip_proto == IPPROTO_ICMP && mconn->peer) {
          ZPN_DEBUG_MCONN_ICMP("DEBUG ICMP fohh_tlv_send_raw success: %s: ip_src 0x%x; ip_dst 0x%x; icmp ID %u; icmp seq# %u; total_xmt %d; now %"PRId64,
                               __func__, ntohl(mconn->peer->icmp_state.s_ip.s_addr), ntohl(mconn->peer->icmp_state.a_ip.s_addr),
                               ntohs(mconn->peer->icmp_state.id), ntohs(mconn->peer->icmp_state.seq), total_xmt, epoch_us());
        }
        zpn_fohh_tlv_pipeline_latency_udp_set_wrapper(fohh_tlv, latency_inspection_stage_tx_3, mconn);
    }

    /* No need to activate drain timer if underlying channel is blocked. It will
     * call unblock when it is drained
     */
    //if (!channel_blocked && (res == FOHH_RESULT_WOULD_BLOCK)) {
    //    /* Activate drain timer because the mconn is buffering data */
    //    zpn_mconn_fohh_tlv_activate_drain_timer(fohh_tlv);
    //}

    zpn_fohh_worker_tx_data_fohh(fohh_connection_get_thread_id(zpn_mconn_fohh_tlv_get_conn(fohh_tlv)),
                            total_xmt);
    /* outer tunnel's tx_data shouldn't be updated when the mconn's flow control is disabled. For such mtunnels
     * peer won't generate window updates thus not expanding the tx_limit. This can result in broker resetting
     * the data channel if there are many zdx mtunnels and no application mtunnel for long periods of time
     */
    if (flow_control_enabled && fohh_tlv->remote_fc_status == flow_ctrl_enabled && mconn_fohh_tlv->remote_fc_status == flow_ctrl_enabled) {
        fohh_tlv->tx_data += total_xmt;
        fohh_connection_set_stats_fohh_tx_data(fohh_tlv->tlv.conn.f_conn, fohh_tlv->tx_data);
    }
    fohh_tlv->deq_bytes += (total_xmt + total_drop);
    fohh_connection_set_stats_fohh_deq_bytes(fohh_tlv->tlv.conn.f_conn, fohh_tlv->deq_bytes);

    if (((total_xmt + total_drop) > 0) && mconn_fohh_tlv->mconn.peer) {
        zpn_mconn_client_window_update(mconn_fohh_tlv->mconn.peer, 0, total_xmt + total_drop, 1);
    } else {
        if ((total_xmt + total_drop > 0) && !mconn_fohh_tlv->mconn.peer) {
            zpn_mconn_stats_update(mconn, window_update_no_peer_after_dequeue_successfully);
            ZPN_LOG(AL_WARNING, "%p tag= %d customer_gid %"PRId64" uncounted_dropped_bytes window_update_no_peer_after_dequeue_successfully %"PRId64"  ", mconn, mconn_fohh_tlv->tag_id, customer_gid, (int64_t)(total_xmt + total_drop));
        }
    }

    return res;
}

int zpn_mconn_fohh_tlv_transmit_cb(void *mconn_base,
                                   void *mconn_self,
                                   void *owner,
                                   void *owner_key,
                                   size_t owner_key_length,
                                   int64_t owner_incarnation,
                                   int fohh_thread_id,
                                   struct evbuffer *buf,
                                   size_t buf_len)
{
    struct zpn_mconn_fohh_tlv *mconn_fohh_tlv = mconn_base;
    struct zpn_fohh_tlv *fohh_tlv = owner;

    if (zpn_mconn_fohh_tlv_local_owner_sanity(fohh_tlv, mconn_fohh_tlv) != ZPN_RESULT_NO_ERROR) {
        ZPN_DEBUG_MCONN("Local owner has gone bad");
        return ZPN_RESULT_NO_ERROR;
    }

    ZPATH_MUTEX_LOCK(&(fohh_tlv->lock), __FILE__, __LINE__);
    if (flow_control_enabled && fohh_tlv->remote_fc_status == flow_ctrl_enabled && mconn_fohh_tlv->remote_fc_status == flow_ctrl_enabled) {
        mconn_fohh_tlv->fc_blocked = 0;
    }
    fohh_tlv->enq_bytes += mconn_fohh_tlv->mconn.bytes_from_peer - mconn_fohh_tlv->bytes_from_peer;
    fohh_tlv->enq_data_us = epoch_us();
    fohh_connection_set_stats_fohh_enq_data_us(fohh_tlv->tlv.conn.f_conn, fohh_tlv->enq_data_us);
    fohh_connection_set_stats_fohh_enq_bytes(fohh_tlv->tlv.conn.f_conn, fohh_tlv->enq_bytes);
    mconn_fohh_tlv->bytes_from_peer = mconn_fohh_tlv->mconn.bytes_from_peer;
    zpn_mconn_tx_buf_len_stats(&mconn_fohh_tlv->mconn, buf_len);

    if (buf_len && (mconn_fohh_tlv->busy == 0)) {
        zpn_mconn_fohh_tlv_idle_to_busy(fohh_tlv, mconn_fohh_tlv);
    }

    ZPATH_MUTEX_UNLOCK(&(fohh_tlv->lock), __FILE__, __LINE__);

    struct zpn_mconn *peer_mconn = mconn_fohh_tlv->mconn.peer;
    zpn_fohh_tlv_rx_pipeline_latency_udp_set(fohh_tlv, mconn_fohh_tlv, peer_mconn ? peer_mconn->rx_data_us : 0);

    zpn_mconn_fohh_tlv_activate_drain_timer(fohh_tlv);

    return ZPN_RESULT_NO_ERROR;
}

int zpn_mconn_fohh_tlv_pause_cb(void *mconn_base,
                                void *mconn_self,
                                void *owner,
                                void *owner_key,
                                size_t owner_key_length,
                                int64_t owner_incarnation,
                                int fohh_thread_id)
{
    struct zpn_mconn_fohh_tlv *mconn_fohh_tlv = mconn_base;
    struct zpn_fohh_tlv *fohh_tlv = owner;
    int32_t tag_id = *((int32_t *)owner_key);

    if (mconn_fohh_tlv->tag_id && !mconn_fohh_tlv->remote_paused) {
        mconn_fohh_tlv->remote_paused = 1;
        mconn_fohh_tlv->pause_sent_us = epoch_us();
        mconn_fohh_tlv->mconn.to_client_paused_count++; /* Sent pause to client */

        if (fohh_tlv->tunnel_pr) {
            //  deploy read disable per configuration flag
            if (!mconn_fohh_tlv->mconn.fohh_connection_disable_read_config_flag) {
                ZPN_DEBUG_MCONN("zpn_mconn_fohh_tlv_pause_cb -> fohh_connection_disable_read, tag=%d disabled_read_socket (before set) %d", tag_id, mconn_fohh_tlv->disabled_read_socket);
                mconn_fohh_tlv->disabled_read_socket = 1;
                fohh_connection_disable_read(zpn_mconn_fohh_tlv_get_conn(fohh_tlv));
                __sync_fetch_and_add_8(&(fohh_tlv->fohh_connection_disable_read_cnt), 1);
            } else {
                ZPN_DEBUG_MCONN("zpn_mconn_fohh_tlv_pause_cb -> fohh_connection_disable_read, tag=%d disabled_read_socket %d not configured %d", tag_id, mconn_fohh_tlv->disabled_read_socket, mconn_fohh_tlv->mconn.fohh_connection_disable_read_config_flag);
            }
        }

        if (zpn_mconn_fohh_tlv_local_owner_sanity(fohh_tlv, mconn_fohh_tlv) == ZPN_RESULT_NO_ERROR) {
            ZPN_DEBUG_MCONN("zpn_mconn_fohh_tlv_pause_cb -> zpn_send_zpn_mtunnel_tag_pause. tag=%d", tag_id);
            return zpn_send_zpn_mtunnel_tag_pause(zpn_mconn_fohh_tlv_get_conn(fohh_tlv),
                                                  owner_incarnation,
                                                  tag_id);
        } else {
            ZPN_DEBUG_MTUNNEL("Do not send pause, local owner gone");
            return ZPN_RESULT_NO_ERROR;
        }
    } else {
        return ZPN_RESULT_NO_ERROR;
    }
}


int zpn_mconn_fohh_tlv_resume_cb(void *mconn_base,
                                 void *mconn_self,
                                 void *owner,
                                 void *owner_key,
                                 size_t owner_key_length,
                                 int64_t owner_incarnation,
                                 int fohh_thread_id)
{
    struct zpn_mconn_fohh_tlv *mconn_fohh_tlv = mconn_base;
    struct zpn_fohh_tlv *fohh_tlv = owner;
    int32_t tag_id = *((int32_t *)owner_key);

    if (mconn_fohh_tlv->tag_id && mconn_fohh_tlv->remote_paused) {
        int64_t pause_elapsed_time_us = epoch_us() - mconn_fohh_tlv->pause_sent_us;
        mconn_fohh_tlv->remote_paused = 0;
        mconn_fohh_tlv->mconn.to_client_resume_count++;
        mconn_fohh_tlv->mconn.to_client_pause_time_total_us += pause_elapsed_time_us;
        if (mconn_fohh_tlv->mconn.to_client_pause_time_max_us < pause_elapsed_time_us) {
            mconn_fohh_tlv->mconn.to_client_pause_time_max_us = pause_elapsed_time_us;
            mconn_fohh_tlv->mconn.to_client_pause_time_max_epoch_us = epoch_us();
        }
        if (fohh_tlv->tunnel_pr) {
            //  read disable per configuration flag
            if (!mconn_fohh_tlv->mconn.fohh_connection_disable_read_config_flag) {
                ZPN_DEBUG_MCONN("zpn_mconn_fohh_tlv_resume_cb -> fohh_connection_enable_read, tag=%d disabled_read_socket(before set) %d", tag_id, mconn_fohh_tlv->disabled_read_socket);
                mconn_fohh_tlv->disabled_read_socket = 0;
                fohh_connection_enable_read(zpn_mconn_fohh_tlv_get_conn(fohh_tlv));
                __sync_fetch_and_add_8(&(fohh_tlv->fohh_connection_enable_read_cnt), 1);
            } else {
                ZPN_DEBUG_MCONN("zpn_mconn_fohh_tlv_resume_cb -> fohh_connection_enable_read, tag=%d disabled_read_socket %d, not configured %d", tag_id, mconn_fohh_tlv->disabled_read_socket, mconn_fohh_tlv->mconn.fohh_connection_disable_read_config_flag);
            }
        }

        if (zpn_mconn_fohh_tlv_local_owner_sanity(fohh_tlv, mconn_fohh_tlv) == ZPN_RESULT_NO_ERROR) {
            ZPN_DEBUG_MCONN("zpn_mconn_fohh_tlv_resume_cb -> zpn_send_zpn_mtunnel_tag_resume. tag=%d", tag_id);
            return zpn_send_zpn_mtunnel_tag_resume(zpn_mconn_fohh_tlv_get_conn(fohh_tlv),
                                                   owner_incarnation,
                                                   tag_id);
        } else {
            ZPN_DEBUG_MTUNNEL("Do not send resume, local owner gone");
            return ZPN_RESULT_NO_ERROR;
        }
    } else {
        return ZPN_RESULT_NO_ERROR;
    }
}

int zpn_mconn_fohh_tlv_disable_read_cb(void *mconn_base,
                                void *mconn_self,
                                void *owner,
                                void *owner_key,
                                size_t owner_key_length,
                                int64_t owner_incarnation,
                                int fohh_thread_id)
{
    struct zpn_mconn_fohh_tlv *mconn_fohh_tlv = mconn_base;
    struct zpn_fohh_tlv *fohh_tlv = owner;
    int32_t tag_id = *((int32_t *)owner_key);

    //  deploy read disable per configuration flag
    if (fohh_tlv->tunnel_pr &&
        mconn_fohh_tlv->mconn.fohh_connection_disable_read_config_flag &&
        !mconn_fohh_tlv->disabled_read_socket) {

        mconn_fohh_tlv->disabled_read_socket = 1;
        mconn_fohh_tlv->disable_read_client_tx_buff_high_time_start_us = epoch_us();
        mconn_fohh_tlv->mconn.disable_read_client_count++;
        ZPN_DEBUG_MCONN("disable read client cb -> fohh_connection_disable_read before tag=%d  count %d", tag_id, mconn_fohh_tlv->mconn.disable_read_client_count);

        fohh_connection_disable_read(zpn_mconn_fohh_tlv_get_conn(fohh_tlv));
        __sync_fetch_and_add_8(&(fohh_tlv->fohh_connection_disable_read_client_tx_buff_high_cnt), 1);
    }
    return ZPN_RESULT_NO_ERROR;
}

int zpn_mconn_fohh_tlv_enable_read_cb(void *mconn_base,
                                 void *mconn_self,
                                 void *owner,
                                 void *owner_key,
                                 size_t owner_key_length,
                                 int64_t owner_incarnation,
                                 int fohh_thread_id)
{
    struct zpn_mconn_fohh_tlv *mconn_fohh_tlv = mconn_base;
    struct zpn_fohh_tlv *fohh_tlv = owner;
    int32_t tag_id = *((int32_t *)owner_key);

    //  read disable per configuration flag
    if (fohh_tlv->tunnel_pr &&
        mconn_fohh_tlv->mconn.fohh_connection_disable_read_config_flag &&
        mconn_fohh_tlv->disabled_read_socket) {

        mconn_fohh_tlv->disabled_read_socket = 0;

        int64_t disable_read_elapsed_time_us = epoch_us() - mconn_fohh_tlv->disable_read_client_tx_buff_high_time_start_us;
        mconn_fohh_tlv->mconn.enable_read_client_count++;
        mconn_fohh_tlv->mconn.disable_read_client_time_total_us += disable_read_elapsed_time_us;
        if (mconn_fohh_tlv->mconn.disable_read_client_time_max_us < disable_read_elapsed_time_us) {
            mconn_fohh_tlv->mconn.disable_read_client_time_max_us = disable_read_elapsed_time_us;
            mconn_fohh_tlv->mconn.disable_read_client_time_max_epoch_us = epoch_us();
        }
        ZPN_DEBUG_MCONN("enable read client cb -> fohh_connection_enable_read before tag=%d  count %d", tag_id, mconn_fohh_tlv->mconn.enable_read_client_count);

        fohh_connection_enable_read(zpn_mconn_fohh_tlv_get_conn(fohh_tlv));
        __sync_fetch_and_add_8(&(fohh_tlv->fohh_connection_enable_read_client_tx_buff_high_cnt), 1);
        __sync_fetch_and_add_8(&(fohh_tlv->fohh_connection_disable_read_client_tx_buff_high_total_us), disable_read_elapsed_time_us);
    }
    return ZPN_RESULT_NO_ERROR;
}

int zpn_mconn_fohh_tlv_forward_tunnel_end_cb(void *mconn_base,
                                             void *mconn_self,
                                             void *owner,
                                             void *owner_key,
                                             size_t owner_key_length,
                                             int64_t owner_incarnation,
                                             const char *err,
                                             int32_t drop_data)
{
    struct zpn_mconn_fohh_tlv *mconn_fohh_tlv = mconn_base;
    struct zpn_fohh_tlv *fohh_tlv = owner;
    int res = ZPN_RESULT_NO_ERROR;

    /* We should set fin_sent even when mtunnel_end send failed, because in that case,
     * there is something wrong with the fohh connection, and we should kill the mtunnel
     * anyway.
     */
    mconn_fohh_tlv->mconn.fin_sent = 1;
    if (drop_data) {
        mconn_fohh_tlv->mconn.fin_sent_drop_data = 1;
    }

    if (zpn_mconn_fohh_tlv_local_owner_sanity(fohh_tlv, mconn_fohh_tlv) == ZPN_RESULT_NO_ERROR) {
        res = zpn_send_zpn_mtunnel_end(&(fohh_tlv->tlv),
                                       owner_incarnation,
                                       NULL,
                                       *((int32_t *)owner_key),
                                       err,
                                       drop_data);
        if (res) {
            ZPN_LOG(AL_ERROR, "Could not send mtunnel_end. Is this really a problem?");
        } else {
            ZPN_DEBUG_MCONN("Forwarded mtunnel_end message to %s, drop_data = %d, to_client = %ld",
                        fohh_description(zpn_mconn_fohh_tlv_get_conn(fohh_tlv)),
                        drop_data, (long)mconn_fohh_tlv->mconn.bytes_to_client);
        }
    } else {
        ZPN_DEBUG_MTUNNEL("Do not send tunnel end, local owner gone");
    }

    return res;
}

/*
 * This is usually called when our peer has sent out some data and we want to
 * see if we need to update the tx_limit at the remote end of fohh connection
 */
void zpn_mconn_fohh_tlv_window_update_cb(void *mconn_base,
                                         void *mconn_self,
                                         void *owner,
                                         void *owner_key,
                                         size_t owner_key_length,
                                         int64_t owner_incarnation,
                                         int fohh_thread_id,
                                         int tx_len,
                                         int batch_win_upd)
{
    struct zpn_mconn_fohh_tlv *mconn_fohh_tlv = mconn_base;
    struct zpn_fohh_tlv *fohh_tlv = owner;
    struct zpn_mconn *peer_mconn = mconn_fohh_tlv->mconn.peer;
    int64_t next_tx_limit;
    int64_t free_buf;
    int64_t peer_to_client_bytes = 0;
    int64_t now_us = epoch_us();
    int64_t filled_buf;

    ZPN_DEBUG_MCONN("zpn_mconn_fohh_tlv_window_update_cb(), tag_id = %d, flow_control = %d, tx_len = %d",
                    mconn_fohh_tlv->tag_id, flow_control_enabled, tx_len);

    /*
     * This is the FOHH flow control logic where we are checking if we have credit to send to remote.
     * flow control logic is only required if it is enabled at 3 level:
     *
     *          1) fohh_control_enabled - global level, it is set to enabled by default. we can flip this variable through command line binary,
     *                                    this one is mostly for dev purpose.
     *          2) fohh_tlv->remote_fc_status - FOHH TLS Connection level, this flag controls at the whole FOHH TLS conneciton level.
     *                                          if disabled flow control at this level, all mtunnel using this TLS connection has flow control disabled.
     *          3) mconn_fohh_tlv->remote_fc_status - mtunnel level, this flag only control whether we want flow control for particular mtunnel mconn.
     *
     */
    if (!flow_control_enabled || (fohh_tlv->remote_fc_status == flow_ctrl_none) || (mconn_fohh_tlv->remote_fc_status == flow_ctrl_disabled)) {
        ZPN_DEBUG_MCONN("We are not flow control enabled, or remote doesn't know flow control, exit");
        return;
    }

    mconn_fohh_tlv->last_wnd_rx_update_us = now_us;

    /*
     * First check for mconn to see if we want to update mconn window at remote end
     *
     * peer_to_client_bytes tracks the number of bytes we have processed, this includes
     * the bytes we sent out and the bytes we dropped.
     *
     * The value for (mconn_fohh_tlv->mconn.bytes_to_peer - (peer_mconn->bytes_to_client + peer_mconn.bytes_dropped_udp))
     * should be the amount of data we have buffered for the direction of data flow of the mtunnel.
     */
    if (peer_mconn) {
        peer_to_client_bytes = peer_mconn->bytes_to_client + peer_mconn->bytes_dropped_udp;
    } else {
        peer_to_client_bytes = mconn_fohh_tlv->mconn.bytes_to_peer;
    }

    free_buf = mconn_fohh_tlv->remote_tx_limit - peer_to_client_bytes;

    if ((free_buf < fohh_tlv_mconn_low_watermark) ||
        (now_us - mconn_fohh_tlv->last_wnd_tx_update_us > mconn_window_update_timeout_us)) {
        next_tx_limit = peer_to_client_bytes + fohh_tlv_mconn_window_size;
        if (!batch_win_upd || !fohh_get_batched_mconn_window_updates_config(zpn_mconn_fohh_tlv_get_conn(fohh_tlv))) {
          ZPN_DEBUG_MCONN("zpn_send_zpn_fohh_window_update(), tag_id = %d, tx_limit = %"PRId64 ", data = %"PRId64,
                          mconn_fohh_tlv->tag_id, next_tx_limit, mconn_fohh_tlv->data_arrived);

          zpn_send_zpn_fohh_window_update(zpn_mconn_fohh_tlv_get_conn(fohh_tlv),
                                          owner_incarnation,
                                          mconn_fohh_tlv->tag_id,
                                          next_tx_limit,
                                          mconn_fohh_tlv->data_arrived);
          mconn_fohh_tlv->last_wnd_tx_update_us = now_us;
        } else if (!fohh_tlv->unblock_thread_call_count) {
          zpn_client_drain_tx_data(&mconn_fohh_tlv->mconn); // batched case
          ZPN_DEBUG_MCONN("f_conn %s:%s calling zpn_client_drain_tx_data for window update batching; tag %d",
                          fohh_description(fohh_tlv->tlv.conn.f_conn), fohh_state(fohh_tlv->tlv.conn.f_conn), mconn_fohh_tlv->tag_id);
        }
        mconn_fohh_tlv->remote_tx_limit = next_tx_limit;
    }

    /*
     * Then check for fohh_tlv to see if we want to update fohh_tlv window at remote end
     */
    ZPATH_MUTEX_LOCK(&(fohh_tlv->lock), __FILE__, __LINE__);

    fohh_tlv->peer_tx_data += tx_len;
    free_buf = fohh_tlv->remote_tx_limit - fohh_tlv->peer_tx_data;

    fohh_connection_set_stats_fohh_peer_tx(fohh_tlv->tlv.conn.f_conn, fohh_tlv->peer_tx_data);

    filled_buf = fohh_tlv->rx_data - fohh_tlv->peer_tx_data;

    bool available_buf_valid = ((fohh_tlv->last_filled_buf - filled_buf) >= FOHH_TLV_MIN_WINDOW_SIZE) &&
                                    ((fohh_tlv->last_filled_buf - filled_buf) < fohh_tlv_low_watermark);

    /* Feature flag enabled fohh flow control enhacement that aims to provide more frequent window updates*/
    bool available_buf_above_threshold = fohh_get_fohh_fc_enhacement_config(zpn_mconn_fohh_tlv_get_conn(fohh_tlv)) && available_buf_valid;

    if ((free_buf < fohh_tlv_low_watermark) ||
        (now_us - fohh_tlv->last_wnd_update_us > fohh_window_update_timeout_us) ||
        available_buf_above_threshold) {

        if (free_buf < fohh_tlv_low_watermark) {
            fohh_connection_incr_stats_fohh_win_update_low_water_mark(fohh_tlv->tlv.conn.f_conn);
            __sync_add_and_fetch_8(&(fohh_tlv->fohh_win_update_low_water_mark), 1);
            ZPN_DEBUG_MCONN("FC f_conn %s:%s zpn_send_zpn_fohh_window_update() window update due to free buf below lower watermark: %"PRId64,
                    fohh_description(fohh_tlv->tlv.conn.f_conn), fohh_state(fohh_tlv->tlv.conn.f_conn), fohh_tlv->tlv.conn.f_conn->stats.fohh_win_update_low_water_mark);

        }

        if (now_us - fohh_tlv->last_wnd_update_us > fohh_window_update_timeout_us) {
            fohh_connection_incr_stats_fohh_win_update_expired(fohh_tlv->tlv.conn.f_conn);
            __sync_add_and_fetch_8(&(fohh_tlv->fohh_win_update_expired), 1);
            ZPN_DEBUG_MCONN("FC f_conn %s:%s zpn_send_zpn_fohh_window_update() window update due to time expiration : %"PRId64,
                    fohh_description(fohh_tlv->tlv.conn.f_conn), fohh_state(fohh_tlv->tlv.conn.f_conn), fohh_tlv->tlv.conn.f_conn->stats.fohh_win_update_expired);
        }

        if (available_buf_above_threshold) {
            fohh_connection_incr_stats_fohh_win_update_above_threshold(fohh_tlv->tlv.conn.f_conn);
            __sync_add_and_fetch_8(&(fohh_tlv->fohh_win_update_available_buf_above_threshold), 1);
            ZPN_DEBUG_MCONN("FC f_conn %s:%s zpn_send_zpn_fohh_window_update() window update due to available buf above threshold : %"PRId64,
                    fohh_description(fohh_tlv->tlv.conn.f_conn), fohh_state(fohh_tlv->tlv.conn.f_conn), fohh_tlv->tlv.conn.f_conn->stats.fohh_win_update_available_buf_above_threshold);
        }

        next_tx_limit = fohh_tlv->peer_tx_data + fohh_tlv_window_size;
        if (zpn_mconn_fohh_tlv_local_owner_sanity(fohh_tlv, mconn_fohh_tlv) == ZPN_RESULT_NO_ERROR) {
            zpn_send_zpn_fohh_window_update(zpn_mconn_fohh_tlv_get_conn(fohh_tlv),
                                            owner_incarnation,
                                            0,
                                            next_tx_limit,
                                            fohh_tlv->rx_data);
            fohh_tlv->window_update_delta_time = now_us - fohh_tlv->last_wnd_update_us;
            if (fohh_tlv->max_window_update_delta_time < fohh_tlv->window_update_delta_time) {
                fohh_tlv->max_window_update_delta_time = fohh_tlv->window_update_delta_time;
            }

            fohh_tlv->remote_tx_limit = next_tx_limit;
            fohh_tlv->last_wnd_update_us = now_us;
            fohh_tlv->last_filled_buf = filled_buf;

            ZPN_DEBUG_MCONN("FC f_conn %s:%s zpn_send_zpn_fohh_window_update(),  delta_time = %"PRId64 " max delta time = %"PRId64,
                    fohh_description(fohh_tlv->tlv.conn.f_conn), fohh_state(fohh_tlv->tlv.conn.f_conn), fohh_tlv->window_update_delta_time, fohh_tlv->max_window_update_delta_time);
        } else {
            ZPN_DEBUG_MTUNNEL("Do not send tunnel end, local owner gone");
        }
    }

    ZPATH_MUTEX_UNLOCK(&(fohh_tlv->lock), __FILE__, __LINE__);

    return;
}

/*
 * in SET-1903, we observed a unrecoverable flow control scenarios where PSE thinks App Connector has big enough window to send data to itself,
 * but App Connector side is not getting enough window room for sending data towards PSE.
 *
 * to repro customer scenario, we figured a way such that, we started traffic at to reach minimum 800MB/sec on PSE.
 * then we apply firewall so that PSE blocks/drop data from 2 from 2 zcc clients, and resume incoming traffics from clients after few seconds.
 * we observed in such scenarios, PSE will re-establish a new connection to those blocked ZCC clients afterwards and teminates mtunnels that were
 * established on previous connection. during the mtunnel termination process, we observed that there were lots of data buffered and PSE were dropping those data.
 * ideally, when PSE dequeuing any data, it should increment peer's conenctions peer_tx_data via the zpn_mconn_client_window_update(peer_mconn) call.
 * but in this repro scenario, zpn_mconn_client_window_update is not happen due to peer mconn got detached early.
 * and we are fixing this issue in ET-48679
 *
 * FOHH flow control mechnism explain:
 * if PSE and app connector is doing fohh flow control, and data is flowing from AC to PSE,
 * that PSE has to advertise fohh window to AC so that AC can only send data window the window limit.
 * PSE : free_buf = tx_limit - peer_tx_data
 * AC : fohh_allowed = tx_limit - tx_data
 *
 * tx_limit is accurated as this number is exchanged and shared between PSE and AC.
 *
 * peer_tx_data is managed by PSE and inside zpn_mconn_client_window_update() call, so whenever AC sent data to PSE, PSE enqueue data on its buffer,
 * then it dequeues data from buffer, either drop it or send to ZCC. PSE needs to update peer_tx_data number in zpn_mconn_client_window_update().
 * but zpn_mconn_client_window_update() is only triggered when 2 mconns are peered together.
 *
 * if we see an issue on fohh flow control, we should enable FC_MCONN debug bit and monitor follow 3 stats:
 *   - window_update_no_peer_discard_data
 *   - window_update_no_peer_after_dequeue_successfully
 *   - window_update_no_peer_drop_because_fin_sent
 *
 */
void zpn_mconn_fohh_tlv_stats_update_cb(void *mconn_base,
                                        void *mconn_self,
                                        void *owner,
                                        void *owner_key,
                                        size_t owner_key_length,
                                        int64_t owner_incarnation,
                                        int fohh_thread_id,
                                        enum zpn_mconn_stats stats_name)
{
    if (zpn_debug_get(ZPN_DEBUG_FC_MCONN_IDX)) {
        if (stats_name == window_update_no_peer_discard_data) {
            __sync_fetch_and_add_8(&(stats.window_update_no_peer_discard_data), 1);
        } else if (stats_name == window_update_no_peer_after_dequeue_successfully) {
            __sync_fetch_and_add_8(&(stats.window_update_no_peer_after_dequeue_successfully), 1);
        } else if (stats_name == window_update_no_peer_drop_because_fin_sent) {
            __sync_fetch_and_add_8(&(stats.window_update_no_peer_drop_because_fin_sent), 1);
        } else if (stats_name == mconn_terminate_no_peer) {
            __sync_fetch_and_add_8(&(stats.mconn_terminate_no_peer), 1);
        } else if (stats_name == mconn_terminate_check_data_buffer_no_peer) {
            __sync_fetch_and_add_8(&(stats.mconn_terminate_check_data_buffer_no_peer), 1);
        } else if (stats_name == mconn_not_clean_due_to_peer_still_data_buffered) {
            __sync_fetch_and_add_8(&(stats.mconn_not_clean_due_to_peer_still_data_buffered), 1);
        } else if (stats_name == window_update_sucesss_discard_data) {
            __sync_fetch_and_add_8(&(stats.window_update_sucesss_discard_data), 1);
        }
    }

    return;
}

// trusted called
static void zpn_fohh_tlv_count_dropped_data(struct zpn_fohh_tlv *fohh_tlv, int64_t dropped_bytes,
                                            enum zpn_mconn_drop_stats drop_stats_type)
{
    switch (drop_stats_type)
    {
    case drop_stats_udp_frame_error:
      fohh_tlv->rx_udp_data_dropped_frame_error += dropped_bytes;
      break;
    case drop_stats_udp_tx_full:
      fohh_tlv->rx_udp_data_dropped_tx_buf_full += dropped_bytes;
      break;
    case drop_stats_icmp_error:
      fohh_tlv->rx_icmp_error_data_dropped += dropped_bytes;
      break;
    case drop_stats_icmp6_error:
      fohh_tlv->rx_icmp6_error_data_dropped += dropped_bytes;
      break;
    default:
      break;
    }
}

int zpn_fohh_tlv_data_callback(struct fohh_connection *connection,
                               void *cookie,
                               int32_t tag,
                               int32_t data_length,
                               struct evbuffer *data)
{
    struct zpn_fohh_tlv *fohh_tlv = fohh_connection_get_dynamic_cookie(connection);
    struct zpn_mconn_fohh_tlv *mconn_fohh_tlv;
    struct zpn_mconn *mconn;
    size_t buffer_len;
    int res = ZPN_RESULT_NO_ERROR;
    int64_t original_incarnation = 0;

    if (!fohh_tlv) {
        ZPN_LOG(AL_ERROR, "No fohh_tlv in dynamic cookie.");
        return ZPN_RESULT_ERR;
    }

    mconn_fohh_tlv = zpn_mconn_fohh_tlv_lookup_tag(fohh_tlv, tag, &original_incarnation);

    if (!mconn_fohh_tlv) {
        ZPN_DEBUG_MCONN("Data arrived on invalid tag = %d, len = %d, %s", (int) tag, (int)data_length, fohh_description(connection));

        if (tag > fohh_tlv->max_tag_id) {
            ZPN_DEBUG_MCONN("FOHH max tag = %d, received tag = %d, data arrived before mtunnel is setup", fohh_tlv->max_tag_id, tag);
        }

        /* Don't return error, or the full TLS connection will
         * close. This is likely just stale data thar arrived after we
         * closed this tag. */
        return ZPN_RESULT_NO_ERROR;
    } else {
        ZPN_DEBUG_MCONN("%s: Tag %d, %d data arrived", fohh_description(connection), (int) tag, (int) data_length);
    }

#ifdef __linux__
    if (fohh_peer_get_quickack_read_config(connection)) {
        ZPN_DEBUG_MCONN("quickack read for client/assistant config %s", fohh_description(connection));
        int tmp_val = 1;
        setsockopt(connection->sock, IPPROTO_TCP, TCP_QUICKACK, (void *)&tmp_val, sizeof(tmp_val));
    }
#endif
    fohh_tlv->rx_data_us = epoch_us();
    fohh_connection_set_stats_fohh_rx_data_us(fohh_tlv->tlv.conn.f_conn, fohh_tlv->rx_data_us);

    mconn = &(mconn_fohh_tlv->mconn);

    /* NOTE: The following framing fix code will eventually need to
     * pay attention to ZAPP version if ZAPP ever sends contiguous udp
     * frames within a single tlv */
    if (mconn->drop_udp_framed_data && (mconn->is_zapp_client || mconn->is_zapp_partner_client || mconn->is_machine_tunnel_client)) {
        /* Fix UDP framing errors when we can... */
        struct evbuffer *tmp = evbuffer_new();
        evbuffer_set_dont_dump(tmp);
        uint16_t udp_header[4];
        int drop = 0;
        while (!drop && (data_length >= 8)) {
            if (8 == evbuffer_copyout(data, udp_header, sizeof(udp_header))) {
                size_t len = ntohs(udp_header[2]);
                if (len < 8) {
                    /* Invalid UDP frame */
                    drop = 1;
                } else {
                    if (len > data_length) {
                        /* We don't have enough data- drop! */
                        drop = 1;
                    } else {
                        /* We have enough data for a complete UDP packet, copy it out */
                        evbuffer_remove_buffer(/*src*/data, /*dst*/tmp, len);
                        data_length -= len;
                    }
                }
            } else {
                /* Drop the frame, as we could not read 8 byte
                 * header. This exact code path should never occur */
                drop = 1;
            }
        }

        /* Throw away any left over data, here drop = 1 because that's the only way to exit the loop when "data" is not empty */
        if (evbuffer_get_length(data)) {
            ZPN_LOG(AL_WARNING, "%s: tag %d, Dropping malformed UDP frame", fohh_description(connection), tag);
            evbuffer_drain(data, evbuffer_get_length(data));
            mconn->packets_malformed_udp++;
        }

        /* If we haven't extracted any valid UDP packet, no need to proceed further */
        if (!evbuffer_get_length(tmp)) {
            /* Nothing to forward to peer */
            evbuffer_free(tmp);
            return ZPN_RESULT_NO_ERROR;
        }

        /* We have sanitized packets in tmp, move its content to "data", and proceed to normal processing */
        data_length = evbuffer_get_length(tmp);
        evbuffer_remove_buffer(tmp, data, data_length);
        evbuffer_free(tmp);
    }

    if (mconn->global_owner) {
        if (mconn->global_owner_calls) {
            (mconn->global_owner_calls->lock)(mconn,
                                              mconn->self,
                                              mconn->global_owner,
                                              mconn->global_owner_key,
                                              mconn->global_owner_key_length);
        } else {
            return ZPN_RESULT_NO_ERROR;
        }
    } else {
        return ZPN_RESULT_NO_ERROR;
    }

    if (!mconn->global_owner) {
        /*
         * Global owner might be gone after we grab the lock, so check it again.
         * Bail if that's the case.
         */
        (mconn->global_owner_calls->unlock)(mconn,
                                            mconn->self,
                                            mconn->global_owner,
                                            mconn->global_owner_key,
                                            mconn->global_owner_key_length);
        return ZPN_RESULT_NO_ERROR;
    }

    if (0 == (mconn->global_owner_calls->validate_incarnation)(mconn,
                                                               mconn->self,
                                                               mconn->global_owner,
                                                               mconn->global_owner_key,
                                                               mconn->global_owner_key_length,
                                                               original_incarnation)) {
        /* mtunnel changed under us? move on to next mtunnel */
        (mconn->global_owner_calls->unlock)(mconn,
                                            mconn->self,
                                            mconn->global_owner,
                                            mconn->global_owner_key,
                                            mconn->global_owner_key_length);

        return ZPN_RESULT_NO_ERROR;
    }

    mconn_fohh_tlv->data_arrived += data_length;
    mconn_fohh_tlv->callbacks ++;
    buffer_len = evbuffer_get_length(data);
    if (data_length > buffer_len) {
        mconn_fohh_tlv->buffer_under ++;
    } else if (data_length < buffer_len) {
        mconn_fohh_tlv->buffer_over ++;
    }
    mconn_fohh_tlv->data_to_peer_attemp += buffer_len;
    int64_t dropped_bytes = 0;
    enum zpn_mconn_drop_stats drop_stats_type = drop_stats_none;
    res = zpn_client_process_rx_data(&(mconn_fohh_tlv->mconn), data, data_length, &dropped_bytes, &drop_stats_type);
    zpn_fohh_tlv_count_dropped_data(fohh_tlv, dropped_bytes, drop_stats_type);
    /* Asynchronous result or UDP framing error are both okay, the
     * tunnel will be fine */
    if ((res == ZPN_RESULT_ASYNCHRONOUS) ||
        (res == ZPN_RESULT_BAD_DATA)) {
        res = ZPN_RESULT_NO_ERROR;
    } else if (res == ZPN_RESULT_INSUFFICIENT_DATA) {
        int ip_proto = (mconn->global_owner_calls->ip_proto)(mconn,
                                                             mconn->self,
                                                             mconn->global_owner,
                                                             mconn->global_owner_key,
                                                             mconn->global_owner_key_length);
        if (ip_proto == IPPROTO_ICMP || ip_proto == IPPROTO_ICMPV6) {
            // If we receive an ICMP request with insufficient data
            // Then we will just drop the packet and move on - ICMP request are 1 per TLV received
            // We can emit a log here if we see issues but because malformed TLV are a good target for
            // malicious behavior lets only log if we want to turn it on
            // Targeting a small subset of mconns like this allows us greater opportunity to enable logging
            // without increasing any attack vector
            ZPN_DEBUG_MCONN_ICMP("%s: processing rx data for %p returned %d",
                                 fohh_description(connection),
                                 &(mconn_fohh_tlv->mconn),
                                 res);
            res = ZPN_RESULT_NO_ERROR;
        }
    }

    if (mconn->global_owner_calls) {
        (mconn->global_owner_calls->unlock)(mconn,
                                            mconn->self,
                                            mconn->global_owner,
                                            mconn->global_owner_key,
                                            mconn->global_owner_key_length);
    }

    zpn_fohh_worker_rx_data_fohh(fohh_connection_get_thread_id(zpn_mconn_fohh_tlv_get_conn(fohh_tlv)),
                            data_length);
    fohh_tlv->rx_data += data_length;
    fohh_connection_set_stats_fohh_rx_data(fohh_tlv->tlv.conn.f_conn, fohh_tlv->rx_data);
    return res;
}

static int zpn_fohh_tlv_mconn_drain_tx_data(struct zpn_fohh_tlv *fohh_tlv, struct zpn_mconn_fohh_tlv *mconn_fohh_tlv)
{
    struct zpn_mconn *mconn = &mconn_fohh_tlv->mconn;
    int res = ZPN_RESULT_NO_ERROR;

    ZPN_DEBUG_MCONN("%p zpn_fohh_tlv_mconn_drain_tx_data: client transmit buffer: %p, transmit buffer len: %ld, tag= %d",
                    mconn, zpn_mconn_get_transmit_buffer(mconn), (long)zpn_mconn_get_transmit_buffer_len(mconn), mconn_fohh_tlv->tag_id);

    if (!mconn->local_owner) {
        /* Cannot send */
        ZPN_DEBUG_MCONN("%p: tag= %d no local owner", mconn, mconn_fohh_tlv->tag_id);
        return ZPN_RESULT_NO_ERROR;
    }

    if (mconn->fin_sent) {
        ZPN_DEBUG_MCONN("%p tag= %d FIN sent already, drop any data", mconn, mconn_fohh_tlv->tag_id);
        if (zpn_mconn_transmit_buffer_exists(mconn)) {
            size_t len = zpn_mconn_get_transmit_buffer_len(mconn);
            zpn_mconn_free_transmit_buffer(mconn);

            fohh_tlv->tx_data_drop += len;
            fohh_connection_set_stats_fohh_tx_data_drop(fohh_tlv->tlv.conn.f_conn, fohh_tlv->tx_data_drop);
            fohh_tlv->deq_bytes += len;
            fohh_connection_set_stats_fohh_deq_bytes(fohh_tlv->tlv.conn.f_conn, fohh_tlv->deq_bytes);
            if (len && mconn->peer) {
                zpn_mconn_client_window_update(mconn->peer, 0, (int)len, 0);
            } else {
                int64_t customer_gid = 0;
                if (mconn->global_owner && mconn->global_owner_calls) {
                    customer_gid = (mconn->global_owner_calls->get_customer_gid)(mconn,
                                                                                 mconn->self,
                                                                                 mconn->global_owner,
                                                                                 mconn->global_owner_key,
                                                                                 mconn->global_owner_key_length);
                }

                zpn_mconn_stats_update(mconn, window_update_no_peer_drop_because_fin_sent);
                ZPN_LOG(AL_WARNING, "%p tag= %d %"PRId64" FIN sent already, dropped data but count not udpate peer %zu ", mconn, mconn_fohh_tlv->tag_id, customer_gid, len);
            }
        }
    }

    if (mconn->to_client_paused) {
        ZPN_DEBUG_MCONN("%p tag = %d paused, client transmit buffer: %p",
                    mconn, mconn_fohh_tlv->tag_id, zpn_mconn_get_transmit_buffer(mconn));
        int64_t pause_elapsed_time_us = epoch_us() - mconn->pause_start_us;
        if (pause_elapsed_time_us < ZPN_MCONN_PAUSE_TIMEOUT) {
            /* We are blocked by the client, bail */
            return ZPN_RESULT_CUR_MCONN_BLOCK_AND_NO_IMPACT_ON_SHARED_RESOURCE;
        } else {
            /* Pause has timed out, so we resume transmit */
            mconn->to_client_paused = 0;
            mconn->to_client_pause_timed_out_count++;
            mconn->from_client_pause_time_total_us += pause_elapsed_time_us;
            if (mconn->from_client_pause_time_max_us < pause_elapsed_time_us) {
                mconn->from_client_pause_time_max_us = pause_elapsed_time_us;
                mconn->from_client_pause_time_max_epoch_us = epoch_us();
            }
        }
    }

    if (!zpn_mconn_transmit_buffer_exists(mconn)) {
        /* Nothing to send */
        return ZPN_RESULT_NO_ERROR;
    }

    int ip_proto = (mconn->global_owner_calls->ip_proto)(mconn,
                                                         mconn->self,
                                                         mconn->global_owner,
                                                         mconn->global_owner_key,
                                                         mconn->global_owner_key_length);
    if (ip_proto == IPPROTO_ICMP && mconn->peer) {
      ZPN_DEBUG_MCONN_ICMP("DEBUG ICMP: %s: ip_src 0x%x; ip_dst 0x%x; icmp ID %u; icmp seq# %u; now (redundant) %"PRId64,
                           __func__, ntohl(mconn->peer->icmp_state.s_ip.s_addr), ntohl(mconn->peer->icmp_state.a_ip.s_addr),
                           ntohs(mconn->peer->icmp_state.id), ntohs(mconn->peer->icmp_state.seq), epoch_us());
    }
    zpn_fohh_tlv_pipeline_latency_udp_set_wrapper(fohh_tlv, latency_inspection_stage_tx_1, mconn);

    res = zpn_fohh_tlv_data_write(mconn_fohh_tlv,
                                  fohh_tlv,
                                  mconn->local_owner_incarnation,
                                  zpn_mconn_get_transmit_buffer(mconn),
                                  zpn_mconn_get_transmit_buffer_len(mconn));

    if (res == ZPN_RESULT_NO_ERROR) {
        /* Do nothing */
    } else if (res == ZPN_RESULT_WOULD_BLOCK) {
        ZPN_DEBUG_MCONN("%p tag=%d Failed to send err = %s", mconn, mconn_fohh_tlv->tag_id,  zpn_result_string(res));
        res = ZPN_RESULT_NO_ERROR;
    } else {
        ZPN_LOG(AL_ERROR, "%p tag=%d Failed to send err = %s", mconn, mconn_fohh_tlv->tag_id,  zpn_result_string(res));
        /* Let higher layer terminate on recognition of error. */
    }

    if (!zpn_mconn_get_transmit_buffer_len(mconn)) {
        zpn_mconn_free_transmit_buffer(mconn);
    }

    ZPN_DEBUG_MCONN("%p tag= %d client transmit buffer: %p, tag=%d len=%lu",
                    mconn, mconn_fohh_tlv->tag_id, zpn_mconn_get_transmit_buffer(mconn), mconn_fohh_tlv->tag_id,zpn_mconn_get_transmit_buffer_len(mconn));
    /* Check to see if we need to resume peer */
    if (zpn_mconn_get_transmit_buffer_len(mconn) < ZPN_MCONN_MAX_CLIENT_TX_DATA) {


        ZPN_DEBUG_MCONN("%p: tag= %d mconn < max tx data, res: %s", mconn, mconn_fohh_tlv->tag_id, zpn_result_string(res));
        if (mconn->peer) {
            res = zpn_mconn_resume_client(mconn->peer, 1);

            if (res && (res != ZPN_RESULT_WOULD_BLOCK)) {
                ZPN_LOG(AL_ERROR, "%p: tag= %d Cannot resume peer, res = %s", mconn, mconn_fohh_tlv->tag_id, zpn_result_string(res));
                zpn_mconn_terminate(mconn->peer, 1, 0, MT_CLOSED_INTERNAL_ERROR, NULL);
            }
        }
    }

    if (mconn->fohh_connection_disable_read_config_flag) {

        size_t tx_buff_len = zpn_mconn_get_transmit_buffer_len(mconn);

        if (!mconn->disable_read_client_tx_buff_low ||
            !mconn->disable_read_client_tx_buff_high ||
            !mconn->disable_read_client_tx_buff_high_allow_time_max_us) {
            ZPN_DEBUG_MCONN("mconn=%p, peer=%p: disable read client flag with values zero, tag= %d, tx buff len=%lu, low=%"PRId64", high=%"PRId64", time=%"PRId64" ",
                            mconn, mconn->peer, mconn_fohh_tlv->tag_id, tx_buff_len,
                            mconn->disable_read_client_tx_buff_low, mconn->disable_read_client_tx_buff_high, mconn->disable_read_client_tx_buff_high_allow_time_max_us);
            return res;
        }

        if(!mconn->peer) {
            ZPN_DEBUG_MCONN("disable read client flag mconn=%p peer null", mconn);
            return res;
        }

        int64_t now = epoch_us();
        int64_t client_tx_buff_high_allow_time_us = 0;

        if (tx_buff_len > mconn->disable_read_client_tx_buff_low ||
            tx_buff_len > mconn->disable_read_client_tx_buff_high) {

            // if above high water trigger timer
            if (tx_buff_len > mconn->disable_read_client_tx_buff_high &&
                mconn->disable_read_client_tx_buff_high_allow_time_start_us == 0) {
                mconn->disable_read_client_tx_buff_high_allow_time_start_us = now;
            }
            if( mconn->disable_read_client_tx_buff_high_allow_time_start_us) {
                client_tx_buff_high_allow_time_us = now - mconn->disable_read_client_tx_buff_high_allow_time_start_us;
            }

            ZPN_DEBUG_MCONN("mconn=%p, peer=%p: disable read client above, tag= %d, tx buff len=%lu, low=%"PRId64", high=%"PRId64", time=%"PRId64", curr_time=%"PRId64" us, trig=%d , peer cnt(dis/en-able)=(%d,%d), tot=%"PRId64"us peer(pause,resume)=(%d,%d), tot=%"PRId64"us ",
                            mconn, mconn->peer, mconn_fohh_tlv->tag_id, tx_buff_len,
                            mconn->disable_read_client_tx_buff_low, mconn->disable_read_client_tx_buff_high, mconn->disable_read_client_tx_buff_high_allow_time_max_us,
                            client_tx_buff_high_allow_time_us,
                            mconn->disable_read_client_tx_buff_high_allow_time_max_flag,
                            mconn->peer->disable_read_client_count, mconn->peer->enable_read_client_count,
                            mconn->peer->disable_read_client_time_total_us,
                            mconn->peer->to_client_paused_count, mconn->peer->to_client_resume_count,
                            mconn->peer->to_client_pause_time_total_us);

            // if more than allowed max time above tx buff high buff level, block client
            if (client_tx_buff_high_allow_time_us > mconn->disable_read_client_tx_buff_high_allow_time_max_us) {
                mconn->disable_read_client_tx_buff_high_allow_time_max_flag = 1;

                // block upstream client - mconn peer
                res = zpn_mconn_disable_read_client(mconn->peer, 1);
                if (res && (res != ZPN_RESULT_WOULD_BLOCK)) {
                    ZPN_LOG(AL_ERROR, "%p: tag= %d Cannot disable read client peer, res = %s", mconn, mconn_fohh_tlv->tag_id, zpn_result_string(res));
                    zpn_mconn_terminate(mconn->peer, 1, 0, MT_CLOSED_INTERNAL_ERROR, NULL);
                }
            }
        } else {

            if( mconn->disable_read_client_tx_buff_high_allow_time_start_us) {
                client_tx_buff_high_allow_time_us = now - mconn->disable_read_client_tx_buff_high_allow_time_start_us;
            } else {
                client_tx_buff_high_allow_time_us = 0;
            }

            // reset timer
            mconn->disable_read_client_tx_buff_high_allow_time_start_us = 0;

            ZPN_DEBUG_MCONN("mconn=%p, peer=%p: disable read client below, tag= %d, tx buff len=%lu, low=%"PRId64", high=%"PRId64", time=%"PRId64", curr_time=%"PRId64" us, trig=%d , peer cnt(dis/en-able)=(%d,%d), tot=%"PRId64"us peer(pause,resume)=(%d,%d), tot=%"PRId64"us ",
                            mconn, mconn->peer, mconn_fohh_tlv->tag_id, tx_buff_len,
                            mconn->disable_read_client_tx_buff_low, mconn->disable_read_client_tx_buff_high, mconn->disable_read_client_tx_buff_high_allow_time_max_us,
                            client_tx_buff_high_allow_time_us,
                            mconn->disable_read_client_tx_buff_high_allow_time_max_flag,
                            mconn->peer->disable_read_client_count, mconn->peer->enable_read_client_count,
                            mconn->peer->disable_read_client_time_total_us,
                            mconn->peer->to_client_paused_count, mconn->peer->to_client_resume_count,
                            mconn->peer->to_client_pause_time_total_us);


            //if client was disabled then unblock it
            if (mconn->disable_read_client_tx_buff_high_allow_time_max_flag) {
                mconn->disable_read_client_tx_buff_high_allow_time_max_flag = 0;

                res = zpn_mconn_enable_read_client(mconn->peer, 1);
                if (res && (res != ZPN_RESULT_WOULD_BLOCK)) {
                    ZPN_LOG(AL_ERROR, "%p: tag= %d Cannot enable read client peer, res = %s", mconn, mconn_fohh_tlv->tag_id, zpn_result_string(res));
                    zpn_mconn_terminate(mconn->peer, 1, 0, MT_CLOSED_INTERNAL_ERROR, NULL);
                }
            }
        }
    }

    return res;
}

static int zpn_fohh_tlv_mconn_win_upd_changed(struct zpn_mconn_fohh_tlv *mconn_fohh_tlv)
{
  int remote_tx_limit_changed = mconn_fohh_tlv->remote_tx_limit != mconn_fohh_tlv->prev_batch_remote_tx_limit;
  mconn_fohh_tlv->prev_batch_remote_tx_limit = mconn_fohh_tlv->remote_tx_limit;
  return remote_tx_limit_changed;
}

// used buffer is low enough to warrant more frequent window update
static int zpn_fohh_tlv_mconn_win_upd_needed(const struct zpn_mconn_fohh_tlv *mconn_fohh_tlv, int64_t start_us)
{
  int64_t used_buf;
  if (!(mconn_fohh_tlv->mconn.peer)) {
    used_buf = mconn_fohh_tlv->remote_tx_limit - mconn_fohh_tlv->mconn.bytes_to_peer;
  } else {
    used_buf = mconn_fohh_tlv->remote_tx_limit - (mconn_fohh_tlv->mconn.peer->bytes_to_client + mconn_fohh_tlv->mconn.peer->bytes_dropped_udp);
  }
  ZPN_DEBUG_MCONN("start_us: %ld, mconn_fohh_tlv->last_wnd_tx_update_us: %ld",
                  (long)start_us, (long)mconn_fohh_tlv->last_wnd_tx_update_us);
  return used_buf < fohh_tlv_mconn_low_watermark ||
         (start_us - mconn_fohh_tlv->last_wnd_tx_update_us > mconn_window_update_timeout_us);
}

// allocates memory for struct zpn_fohh_tlv_window_update_batch arrays
static void zpn_fohh_tlv_win_upd_batch_init(struct zpn_fohh_tlv_window_update_batch *zpn_fohh_tlv_window_update_batch,
                                            int count, int batched_window_updates)
{
  assert(zpn_fohh_tlv_window_update_batch);
  if (!batched_window_updates)
    return;
  memset(zpn_fohh_tlv_window_update_batch, 0, sizeof(*zpn_fohh_tlv_window_update_batch));
  zpn_fohh_tlv_window_update_batch->tag_id = ZPN_MALLOC(count * sizeof(*zpn_fohh_tlv_window_update_batch->tag_id));
  assert(zpn_fohh_tlv_window_update_batch->tag_id);
  zpn_fohh_tlv_window_update_batch->tx_limit = ZPN_MALLOC(count * sizeof(*zpn_fohh_tlv_window_update_batch->tx_limit));
  assert(zpn_fohh_tlv_window_update_batch->tx_limit);
  zpn_fohh_tlv_window_update_batch->rx_data = ZPN_MALLOC(count * sizeof(*zpn_fohh_tlv_window_update_batch->rx_data));
  assert(zpn_fohh_tlv_window_update_batch->rx_data);
}

static void zpn_fohh_tlv_win_upd_batch_add(struct zpn_fohh_tlv_window_update_batch *zpn_fohh_tlv_window_update_batch,
                                           struct zpn_mconn_fohh_tlv *mconn_fohh_tlv,
                                           int *mconn_window_update_changed_and_needed, int batched_window_updates,
                                           int64_t start_us)
{
  if (!batched_window_updates) {
    return;
  }
  if (mconn_fohh_tlv->remote_fc_status == flow_ctrl_disabled) {
    return;
  }
  if (!(zpn_fohh_tlv_mconn_win_upd_changed(mconn_fohh_tlv))) {
    return;
  }
  *mconn_window_update_changed_and_needed = zpn_fohh_tlv_mconn_win_upd_needed(mconn_fohh_tlv, start_us) ? 1 : 0;
  if (*mconn_window_update_changed_and_needed) {
    mconn_fohh_tlv->last_wnd_tx_update_us = start_us;
  }
  // yeah, its multiple dereferences for count - minor in this context
  zpn_fohh_tlv_window_update_batch->tag_id[zpn_fohh_tlv_window_update_batch->tag_id_count] = mconn_fohh_tlv->tag_id;
  zpn_fohh_tlv_window_update_batch->tx_limit[zpn_fohh_tlv_window_update_batch->tag_id_count] = mconn_fohh_tlv->tx_limit;
  zpn_fohh_tlv_window_update_batch->rx_data[zpn_fohh_tlv_window_update_batch->tag_id_count] = mconn_fohh_tlv->data_arrived;
  zpn_fohh_tlv_window_update_batch->tag_id_count++;
}

static void zpn_fohh_tlv_win_upd_batch_finalize_counts(struct zpn_fohh_tlv_window_update_batch *zpn_fohh_tlv_window_update_batch)
{
  zpn_fohh_tlv_window_update_batch->tx_limit_count = zpn_fohh_tlv_window_update_batch->rx_data_count = zpn_fohh_tlv_window_update_batch->tag_id_count;
}

static void zpn_fohh_tlv_win_upd_batch_free(struct zpn_fohh_tlv_window_update_batch *zpn_fohh_tlv_window_update_batch)
{
  ZPN_FREE(zpn_fohh_tlv_window_update_batch->tag_id);
  ZPN_FREE(zpn_fohh_tlv_window_update_batch->tx_limit);
  ZPN_FREE(zpn_fohh_tlv_window_update_batch->rx_data);
}

static void zpn_fohh_tlv_win_upd_batch_send(struct zpn_fohh_tlv_window_update_batch *zpn_fohh_tlv_window_update_batch,
                                            struct zpn_fohh_tlv *fohh_tlv, int mconn_window_update_changed_and_needed,
                                            int batched_window_updates)
{
  assert(zpn_fohh_tlv_window_update_batch);
  assert(fohh_tlv);
  if (!batched_window_updates) {
    return;
  }
  zpn_fohh_tlv_win_upd_batch_finalize_counts(zpn_fohh_tlv_window_update_batch);
  if (!zpn_fohh_tlv_window_update_batch->tag_id_count) {
    goto done;
  }
  int64_t now_us = epoch_us();
  if (mconn_window_update_changed_and_needed) {
    zpn_send_zpn_fohh_window_batch_update(zpn_mconn_fohh_tlv_get_conn(fohh_tlv), zpn_fohh_tlv_window_update_batch);
    fohh_tlv->last_batch_wnd_update_us = now_us;
  }
done:
  zpn_fohh_tlv_win_upd_batch_free(zpn_fohh_tlv_window_update_batch);
}

#ifdef __COVERITY__
static void zpn_fohh_tlv_upd_batch_from_idle_queue(struct zpn_fohh_tlv *fohh_tlv, int batched_window_updates,
                                                   struct zpn_fohh_tlv_window_update_batch *zpn_fohh_tlv_window_update_batch,
                                                   int count, int *mconn_window_update_changed_and_needed_aggregate,
                                                   int64_t scheduler_start_us)
{
  return;
}
#else
// coverity[missing_lock: FALSE] does not suppress false positive about gap in lock &(fohh_tlv->lock) taking
// Both Sherry and I agree this is an ongoing problem with this false positive continually showing up
// Only way to suppress this is to note it here and suppress the function itself from Coverity
static void zpn_fohh_tlv_upd_batch_from_idle_queue(struct zpn_fohh_tlv *fohh_tlv, int batched_window_updates,
                                                   struct zpn_fohh_tlv_window_update_batch *zpn_fohh_tlv_window_update_batch,
                                                   int count, int *mconn_window_update_changed_and_needed_aggregate,
                                                   int64_t scheduler_start_us)
{
    assert(zpn_fohh_tlv_window_update_batch);
    assert(fohh_tlv);
    assert(mconn_window_update_changed_and_needed_aggregate);
    if (!batched_window_updates) {
      return;
    }
    struct zpn_mconn_fohh_tlv *mconn_fohh_tlv;
    struct zpn_mconn *mconn = NULL;
    struct fohh_connection *f_conn = zpn_mconn_fohh_tlv_get_conn(fohh_tlv);

    while (count) {
        int64_t original_incarnation = 0;
        ZPATH_MUTEX_LOCK(&(fohh_tlv->lock), __FILE__, __LINE__);
        if ((f_conn != zpn_mconn_fohh_tlv_get_conn(fohh_tlv)) || fohh_tlv->f_conn_down) {
            ZPN_LOG(AL_CRITICAL, "fohh_tlv down or changed?");
            ZPATH_MUTEX_UNLOCK(&(fohh_tlv->lock), __FILE__, __LINE__);
            break;
        }

        mconn_fohh_tlv = TAILQ_FIRST(&(fohh_tlv->idle_mconn_list));
        if (!mconn_fohh_tlv) {
            ZPATH_MUTEX_UNLOCK(&(fohh_tlv->lock), __FILE__, __LINE__);
            break;
        }
        ZPATH_MUTEX_UNLOCK(&(fohh_tlv->lock), __FILE__, __LINE__);
        mconn = &(mconn_fohh_tlv->mconn);
        original_incarnation = (mconn->global_owner_calls->incarnation)(mconn,
                                                                        mconn->self,
                                                                        mconn->global_owner,
                                                                        mconn->global_owner_key,
                                                                        mconn->global_owner_key_length);
        if (!mconn->global_owner) {
            ZPN_DEBUG_MCONN("%p tag= %d Not global owner, exit loop", mconn, mconn_fohh_tlv->tag_id);
            break;
        }
        if (mconn->global_owner_calls) {
            (mconn->global_owner_calls->lock)(mconn,
                                              mconn->self,
                                              mconn->global_owner,
                                              mconn->global_owner_key,
                                              mconn->global_owner_key_length);
            if (fohh_tlv->f_conn_down) {
                ZPN_LOG(AL_NOTICE, "%p: tag= %d fohh_tlv is down while we wait for global lock?", mconn, mconn_fohh_tlv->tag_id);
                (mconn->global_owner_calls->unlock)(mconn,
                                                    mconn->self,
                                                    mconn->global_owner,
                                                    mconn->global_owner_key,
                                                    mconn->global_owner_key_length);
                break;
            }
            if (mconn->global_owner &&
                (1 == (mconn->global_owner_calls->validate_incarnation)(mconn,
                                                                        mconn->self,
                                                                        mconn->global_owner,
                                                                        mconn->global_owner_key,
                                                                        mconn->global_owner_key_length,
                                                                        original_incarnation))) {
                    ZPATH_MUTEX_LOCK(&(fohh_tlv->lock), __FILE__, __LINE__);
                    zpn_mconn_fohh_tlv_remove_from_idle_queue(fohh_tlv, mconn_fohh_tlv);
                    zpn_mconn_fohh_tlv_append_to_idle_queue(fohh_tlv, mconn_fohh_tlv);
                    ZPATH_MUTEX_UNLOCK(&(fohh_tlv->lock), __FILE__, __LINE__);
                    int mconn_window_update_changed_and_needed = 0;
                    zpn_fohh_tlv_win_upd_batch_add(zpn_fohh_tlv_window_update_batch, mconn_fohh_tlv,
                                                   &mconn_window_update_changed_and_needed,
                                                   batched_window_updates, scheduler_start_us);
                    *mconn_window_update_changed_and_needed_aggregate =
                      *mconn_window_update_changed_and_needed_aggregate || mconn_window_update_changed_and_needed;
            } else {
                ZPN_LOG(AL_NOTICE, "%p: tag= %d mt gone while we wait for mt lock?", mconn, mconn_fohh_tlv->tag_id);
            }
            (mconn->global_owner_calls->unlock)(mconn,
                                                mconn->self,
                                                mconn->global_owner,
                                                mconn->global_owner_key,
                                                mconn->global_owner_key_length);
        } else {
            ZPN_LOG(AL_NOTICE, "%p: tag= %d no global_owner_call, mt gone while we wait for mt lock?", mconn, mconn_fohh_tlv->tag_id);
            break;
        }
        count--;
    } // while
}
#endif

#if OLD_SCHEME
/***********************************************************************************
 *  This is the function that sends data from mconn to f_conn. It goes through all
 *  the mtunnels on an f_conn and try to send each mconn's data out. The WHILE loop
 *  will break under three conditions:
 *  Case 1: The transmit to f_conn got blocked. There are 2 sub cases here.
 *    Case 1.1: The block is caused by f_conn. The unblock call from f_conn
 *              will call this function again.
 *    Case 1.2: The block is caused by flow control. In this case, the window
 *              update message from remote side will cause this function to be
 *              called again.
 *  Case 2: We have gone through the mtunnel list 3 times and sent_bytes is 0
 *    In this case we don't have to schedule thread call of this function.
 *    There are also 2 sub cases here.
 *    Case 2.1: Nothing got enqueued during the loop. We have nothing to send,
 *              so no need to schedule thread call.
 *    Case 2.2: Something got enqueued during the loop, the act of enqueue
 *              will call the local owner transmit function, and generate a
 *              a thread call of this function to send out newly enqueued bytes.
 *  Case 3: We have gone through the mtunnel list 3 times and sent_bytes is greater than 0.
 *    In this case, we schedule a thread call of this function to send out any remaining
 *    data. The newly schedule thread call may break in Case 3, or change into Case 1 or Case 2.
 */
int zpn_fohh_tlv_unblock_cb(struct zpn_fohh_tlv *fohh_tlv)
{
    int res = ZPN_RESULT_NO_ERROR;
    struct zpn_mconn_fohh_tlv *mconn_fohh_tlv;
    struct zpn_mconn *mconn = NULL;
    int loop_count = 0;
    int mt_count = fohh_tlv->list_size;
    int count = 0;
    int sent_bytes = 0;
    int blocked = 0;

    ZPN_DEBUG_MCONN("zpn_fohh_tlv_unblock_cb()");

    if (!mt_count) return res;

    /* Drain each mconn in the linked list until we filled up fohh queue or
     * we run out of data to send, or we loop through certain times
     */
    while (1) {
        //int looped_around = 0;
        int64_t original_incarnation = 0;

        if (count >= (3*mt_count)) {
            /* Break out of the look if we have gone throught the mtunnel list 3 times */
            ZPN_DEBUG_MCONN("Gone through mtunnel list 3 times, break out of loop. mt_count = %d", mt_count);
            break;
        }

        ZPATH_MUTEX_LOCK(&(fohh_tlv->lock), __FILE__, __LINE__);

#if 0
        if (fohh_tlv->enq_bytes <= fohh_tlv->deq_bytes) {
            ZPN_DEBUG_MCONN("Nothing to send on f_conn");
            ZPATH_MUTEX_UNLOCK(&(fohh_tlv->lock), __FILE__, __LINE__);
            break;
        }

        if (loop_count > 10) {
            /* God forbid, we should never come here!!!!
             * Why do we keep looping? The only possibility of getting here
             * is that enq_bytes and deq_bytes are out of sync, we think we
             * have stuff to send but the trasnmit buffer of all mconn's are empty.
             * Any transmit blocking would have exited the loop.
             */
            ZPN_LOG(AL_NOTICE, "f_conn %s:%s, unblock looped too many times, resync and bail", fohh_description(fohh_tlv->f_conn), fohh_state(fohh_tlv->f_conn));

            /* We will try to resync the two counters for now, but the above
             * log should prompt us to find out why the out of sync happened
             */
            fohh_tlv->enq_bytes = fohh_tlv->deq_bytes;
            ZPATH_MUTEX_UNLOCK(&(fohh_tlv->lock), __FILE__, __LINE__);
            break;
        }
#endif

        mconn_fohh_tlv = fohh_tlv->next_mconn;
        if (!mconn_fohh_tlv) {
            ZPATH_MUTEX_UNLOCK(&(fohh_tlv->lock), __FILE__, __LINE__);
            break;
        }

        mconn = &mconn_fohh_tlv->mconn;

        if (TAILQ_NEXT(mconn_fohh_tlv, mconn_list_entry)) {
            mconn_fohh_tlv = TAILQ_NEXT(mconn_fohh_tlv, mconn_list_entry);
        } else {
            mconn_fohh_tlv = TAILQ_FIRST(&(fohh_tlv->mconn_list));
            /* We have run around list */
            //looped_around = 1;
            loop_count++;
        }

        count ++;

        fohh_tlv->next_mconn = mconn_fohh_tlv;

        original_incarnation = (mconn->global_owner_calls->incarnation)(mconn,
                                                                        mconn->self,
                                                                        mconn->global_owner,
                                                                        mconn->global_owner_key,
                                                                        mconn->global_owner_key_length);


        ZPN_DEBUG_MCONN("zpn_fohh_tlv_unblock_cb: %p, transmit buffer: %p, tag_id: %d",
                        mconn, zpn_mconn_get_transmit_buffer(mconn), mconn_fohh_tlv->tag_id);

        ZPATH_MUTEX_UNLOCK(&(fohh_tlv->lock), __FILE__, __LINE__);

        if (!mconn->global_owner) {
            return res;
        }

        if (mconn->global_owner_calls) {
            (mconn->global_owner_calls->lock)(mconn,
                                              mconn->self,
                                              mconn->global_owner,
                                              mconn->global_owner_key,
                                              mconn->global_owner_key_length);
            /* We locked the mtunnel */
            int before_tx = 0;
            int after_tx = 0;

            if (mconn->global_owner &&
                (1 == (mconn->global_owner_calls->validate_incarnation)(mconn,
                                                                       mconn->self,
                                                                       mconn->global_owner,
                                                                       mconn->global_owner_key,
                                                                       mconn->global_owner_key_length,
                                                                       original_incarnation))) {
                before_tx = mconn->bytes_to_client;
                //res = zpn_client_drain_tx_data(mconn);
                res = zpn_fohh_tlv_mconn_drain_tx_data(fohh_tlv, (struct zpn_mconn_fohh_tlv *)mconn);
                after_tx = mconn->bytes_to_client;

                if (res || ((after_tx <= before_tx) && zpn_mconn_get_transmit_buffer_len(mconn))) {

                    /* Either blocked or something serious wrong, bail*/

                    if (after_tx <= before_tx) {
                        /* We didn't sent out any */
                        int64_t tx_limit = ((struct zpn_mconn_fohh_tlv *)mconn)->tx_limit;
                        int64_t tx_data = ((struct zpn_mconn_fohh_tlv *)mconn)->tx_data;

                        if ((((struct zpn_mconn_fohh_tlv *)mconn)->remote_fc_status != flow_ctrl_enabled) ||
                                (tx_limit > tx_data)) {

                            ZPN_DEBUG_MCONN("fohh blocked again");
                            /* We didn't send anything out and its not because of our own limit */
                            if (!mconn->to_client_paused) {
                                ZPATH_MUTEX_LOCK(&(fohh_tlv->lock), __FILE__, __LINE__);
                                fohh_tlv->next_mconn = (struct zpn_mconn_fohh_tlv *)mconn;
                                ZPATH_MUTEX_UNLOCK(&(fohh_tlv->lock), __FILE__, __LINE__);
                            }
                        }
                        blocked = 1;
                    }

                    (mconn->global_owner_calls->unlock)(mconn,
                                                    mconn->self,
                                                    mconn->global_owner,
                                                    mconn->global_owner_key,
                                                    mconn->global_owner_key_length);

                    break;
                }

                sent_bytes += (after_tx - before_tx);

                /* If we were just dequeueing data while waiting to kill the
                 * connection, kill the connection now. */
                if ((mconn->client_needs_to_forward) &&
                    (zpn_mconn_get_transmit_buffer_len(mconn) == 0)) {
                    ZPN_DEBUG_MCONN("%p: Finished transmit data, send out pending mtunnel end message", mconn);
                    zpn_mconn_forward_mtunnel_end(mconn, MT_CLOSED_TERMINATED, 0);
                }
            } else {
                /* mtunnel changed under us? move on to next mtunnel */
                /* Do do anything */
                ZPN_DEBUG_MCONN("%p: mt gone?", mconn);
            }

            (mconn->global_owner_calls->unlock)(mconn,
                                                mconn->self,
                                                mconn->global_owner,
                                                mconn->global_owner_key,
                                                mconn->global_owner_key_length);
        }
    }

    if (!blocked && sent_bytes) {
        zpn_mconn_fohh_tlv_activate_drain_timer(fohh_tlv);
    }

    return res;
}
#else
int zpn_fohh_tlv_unblock_cb(struct zpn_fohh_tlv *fohh_tlv)
{
    int res = ZPN_RESULT_NO_ERROR;
    struct zpn_mconn_fohh_tlv *mconn_fohh_tlv;
    struct zpn_mconn *mconn = NULL;
    int blocked = 0;
    struct fohh_connection *f_conn = zpn_mconn_fohh_tlv_get_conn(fohh_tlv);
    int64_t scheduler_start_us = epoch_us();
    int busy_num;
    int idle_num;
    ZPATH_MUTEX_LOCK(&(fohh_tlv->lock), __FILE__, __LINE__);
    busy_num = __sync_fetch_and_add_4(&(fohh_tlv->busy_list_size), 0);
    idle_num = __sync_fetch_and_add_4(&(fohh_tlv->idle_list_size), 0);
    int count = busy_num + idle_num;
    ZPATH_MUTEX_UNLOCK(&(fohh_tlv->lock), __FILE__, __LINE__);
    struct zpn_fohh_tlv_window_update_batch zpn_fohh_tlv_window_update_batch;
    int batched_window_updates = fohh_get_batched_mconn_window_updates_config(zpn_mconn_fohh_tlv_get_conn(fohh_tlv)) &&
       flow_control_enabled && (fohh_tlv->remote_fc_status != flow_ctrl_none);
    zpn_fohh_tlv_win_upd_batch_init(&zpn_fohh_tlv_window_update_batch, count, batched_window_updates);
    int mconn_window_update_changed_and_needed;
    int mconn_window_update_changed_and_needed_aggregate = 0;

    while (1) {
        int64_t original_incarnation = 0;

        ZPATH_MUTEX_LOCK(&(fohh_tlv->lock), __FILE__, __LINE__);

        if ((f_conn != zpn_mconn_fohh_tlv_get_conn(fohh_tlv)) || fohh_tlv->f_conn_down) {
            ZPN_LOG(AL_CRITICAL, "fohh_tlv down or changed?");
            ZPATH_MUTEX_UNLOCK(&(fohh_tlv->lock), __FILE__, __LINE__);
            break;
        }

        mconn_fohh_tlv = TAILQ_FIRST(&(fohh_tlv->busy_mconn_list));
        if (!mconn_fohh_tlv) {
            // ZPN_DEBUG_MCONN("Empty busy list, exit loop");
            ZPATH_MUTEX_UNLOCK(&(fohh_tlv->lock), __FILE__, __LINE__);
            break;
        }

        ZPATH_MUTEX_UNLOCK(&(fohh_tlv->lock), __FILE__, __LINE__);

        mconn = &(mconn_fohh_tlv->mconn);
        zpn_fohh_tlv_pipeline_latency_udp_set_wrapper(fohh_tlv, latency_inspection_stage_tx_0, mconn);

        original_incarnation = (mconn->global_owner_calls->incarnation)(mconn,
                                                                        mconn->self,
                                                                        mconn->global_owner,
                                                                        mconn->global_owner_key,
                                                                        mconn->global_owner_key_length);

        /* time stamp starting measurements for max time taken to schedule packet waiting at egress mconn for tx - tx_data_unblock_max_us */
        if (!mconn->tx_data_unblock_us) {
            mconn->tx_data_unblock_us = epoch_us();
        }

        ZPN_DEBUG_MCONN("%p client transmit buffer: %p, tag= %d",
                        mconn, zpn_mconn_get_transmit_buffer(mconn), mconn_fohh_tlv->tag_id);

        if (!mconn->global_owner) {
            /* Should not happen, but just guard against it */
            ZPN_DEBUG_MCONN("%p tag= %d Not global owner, exit loop", mconn, mconn_fohh_tlv->tag_id);
            break;
        }

        if (mconn->global_owner_calls) {
            (mconn->global_owner_calls->lock)(mconn,
                                              mconn->self,
                                              mconn->global_owner,
                                              mconn->global_owner_key,
                                              mconn->global_owner_key_length);

            /* We locked the mtunnel */

            if (fohh_tlv->f_conn_down) {
                ZPN_LOG(AL_NOTICE, "%p: tag= %d fohh_tlv is down while we wait for global lock?", mconn, mconn_fohh_tlv->tag_id);
                (mconn->global_owner_calls->unlock)(mconn,
                                                    mconn->self,
                                                    mconn->global_owner,
                                                    mconn->global_owner_key,
                                                    mconn->global_owner_key_length);
                break;
            }

            /*
             * Prior to ET-84110, the while loop always terminated when a mconn is append back to the busy queue, fohh_tlv->busy_mconn_list.
             * In ET-84110, we added a fix to continue the loop after pushing a paused mconn back the tail of the busy queue, fohh_tlv->busy_mconn_list
             * Thus adding following logic to prevent infinite loop.
             * Example, 2 mconns in busy_mconn_list, while mconn_1 is paused by ZCC
             * t1: dequeue mconn_1, zpn_fohh_tlv_mconn_drain_tx_data
             * t2: mconn_1 is paused by ZCC, no progress being made, append it back to busy_mconn_list, continue the loop
             * t3: dequeue next mconn, mconn_2, zpn_fohh_tlv_mconn_drain_tx_data
             * t4: successfully drained all data for mconn_2
             * t5: dequeue next mconn, mconn_1 again, break the loop since we already processed this mconn in this round.
             */
            if (mconn_fohh_tlv->last_unblock_cb_process_us && (mconn_fohh_tlv->last_unblock_cb_process_us > scheduler_start_us)) {
                ZPN_DEBUG_MCONN("%p: tag= %d processed the entire loop, exiting", mconn, mconn_fohh_tlv->tag_id);
                (mconn->global_owner_calls->unlock)(mconn,
                                                    mconn->self,
                                                    mconn->global_owner,
                                                    mconn->global_owner_key,
                                                    mconn->global_owner_key_length);
                break;
            } else {
                mconn_fohh_tlv->last_unblock_cb_process_us = epoch_us();
            }

            if (mconn->global_owner &&
                (1 == (mconn->global_owner_calls->validate_incarnation)(mconn,
                                                                        mconn->self,
                                                                        mconn->global_owner,
                                                                        mconn->global_owner_key,
                                                                        mconn->global_owner_key_length,
                                                                        original_incarnation))) {

                /* Looks like MT is still around when we come here */

                res = zpn_fohh_tlv_mconn_drain_tx_data(fohh_tlv, (struct zpn_mconn_fohh_tlv *)mconn);
                ZPN_DEBUG_MCONN("%p: tag= %d zpn_fohh_tlv_mconn_drain_tx_data, res: %s", mconn, mconn_fohh_tlv->tag_id, zpn_result_string(res));

                if (!zpn_mconn_get_transmit_buffer_len(mconn)) {

                    /* We have sent out all the data, move to idle queue */

                    ZPATH_MUTEX_LOCK(&(fohh_tlv->lock), __FILE__, __LINE__);
                    zpn_mconn_fohh_tlv_remove_from_busy_queue(fohh_tlv, mconn_fohh_tlv);
                    zpn_mconn_fohh_tlv_append_to_idle_queue(fohh_tlv, mconn_fohh_tlv);
                    mconn_window_update_changed_and_needed = 0;
                    zpn_fohh_tlv_win_upd_batch_add(&zpn_fohh_tlv_window_update_batch, mconn_fohh_tlv,
                                                   &mconn_window_update_changed_and_needed,
                                                   batched_window_updates, scheduler_start_us);
                    mconn_window_update_changed_and_needed_aggregate =
                      mconn_window_update_changed_and_needed_aggregate || mconn_window_update_changed_and_needed;
                    ZPATH_MUTEX_UNLOCK(&(fohh_tlv->lock), __FILE__, __LINE__);

                    /* If we were just dequeueing data while waiting to kill the
                     * connection, kill the connection now. */
                    if (mconn->client_needs_to_forward) {
                        ZPN_DEBUG_MCONN("%p: tag= %d Finished transmit data, send out pending mtunnel end message", mconn, mconn_fohh_tlv->tag_id);
                        zpn_mconn_forward_mtunnel_end(mconn, MT_CLOSED_TERMINATED, 0);
                    }
                } else {
                    ZPATH_MUTEX_LOCK(&(fohh_tlv->lock), __FILE__, __LINE__);
                    zpn_mconn_fohh_tlv_remove_from_busy_queue(fohh_tlv, mconn_fohh_tlv);

                    if (res && res == ZPN_RESULT_CUR_MCONN_BLOCK_AND_NO_IMPACT_ON_SHARED_RESOURCE) {
                        /* Move mconn to end of busy list, so next mconn will be sent when we come back */
                        zpn_mconn_fohh_tlv_append_to_busy_queue(fohh_tlv, mconn_fohh_tlv);

                        ZPN_DEBUG_MCONN("%p: tag= %d Append mconn to busy queue, continue to process next mconn, res: %s", mconn, mconn_fohh_tlv->tag_id, zpn_result_string(res));

                        res = ZPN_RESULT_NO_ERROR;
                    } else if (res && (res != ZPN_RESULT_WOULD_BLOCK)) {
                        /* Something went wrong, move the mconn to idle queue, waiting to be terminated */
                        zpn_mconn_fohh_tlv_append_to_idle_queue(fohh_tlv, mconn_fohh_tlv);

                        ZPN_DEBUG_MCONN("%p: tag= %d Append to idle queue, res: %s", mconn, mconn_fohh_tlv->tag_id, zpn_result_string(res));
                    } else {
                        /* Move mconn to end of busy list, so next mconn will be sent when we come back */
                        zpn_mconn_fohh_tlv_append_to_busy_queue(fohh_tlv, mconn_fohh_tlv);

                        ZPN_DEBUG_MCONN("%p: tag= %d Append mconn to busy queue, res: %s", mconn, mconn_fohh_tlv->tag_id, zpn_result_string(res));
                        blocked = 1;
                    }
                    mconn_window_update_changed_and_needed = 0;
                    zpn_fohh_tlv_win_upd_batch_add(&zpn_fohh_tlv_window_update_batch, mconn_fohh_tlv,
                                                   &mconn_window_update_changed_and_needed,
                                                   batched_window_updates, scheduler_start_us);
                    mconn_window_update_changed_and_needed_aggregate =
                      mconn_window_update_changed_and_needed_aggregate || mconn_window_update_changed_and_needed;
                    ZPATH_MUTEX_UNLOCK(&(fohh_tlv->lock), __FILE__, __LINE__);

                    if (blocked) {
                        /* We are blocked, exit loop */
                        (mconn->global_owner_calls->unlock)(mconn,
                                                            mconn->self,
                                                            mconn->global_owner,
                                                            mconn->global_owner_key,
                                                            mconn->global_owner_key_length);

                        break;
                    }
                }

            } else {
                /* mtunnel changed under us? move on to next mtunnel */
                /* Move to idle, which eventually will be timed out */
                ZPN_LOG(AL_NOTICE, "%p: tag= %d no global_owner, mt gone while we wait for mt lock?", mconn, mconn_fohh_tlv->tag_id);
            }

            (mconn->global_owner_calls->unlock)(mconn,
                                                mconn->self,
                                                mconn->global_owner,
                                                mconn->global_owner_key,
                                                mconn->global_owner_key_length);
        } else {
            /* No global owner call? Bail */
            ZPN_LOG(AL_NOTICE, "%p: tag= %d no global_owner_call, mt gone while we wait for mt lock?", mconn, mconn_fohh_tlv->tag_id);
            break;
        }
    } // while
    fohh_connection_set_stats_fohh_peer_tx(fohh_tlv->tlv.conn.f_conn, fohh_tlv->peer_tx_data);
    zpn_fohh_tlv_upd_batch_from_idle_queue(fohh_tlv, batched_window_updates, &zpn_fohh_tlv_window_update_batch,
                                           idle_num, &mconn_window_update_changed_and_needed_aggregate, scheduler_start_us);
    zpn_fohh_tlv_win_upd_batch_send(&zpn_fohh_tlv_window_update_batch, fohh_tlv,
                                    mconn_window_update_changed_and_needed_aggregate, batched_window_updates);
    return res;
}
#endif

void zpn_fohh_tlv_clear_state(struct zpn_fohh_tlv *fohh_tlv)
{
    fohh_tlv->tx_data = 0;
    fohh_tlv->tx_data_drop = 0;
    fohh_tlv->rx_data = 0;
    fohh_tlv->tx_limit = fohh_tlv_window_size;
    fohh_tlv->remote_rx_data = 0;
    fohh_tlv->remote_rx_data_change_us = 0;
    fohh_tlv->remote_fc_status = flow_ctrl_none;
    fohh_tlv->last_wnd_update_us = 0;
    fohh_tlv->last_batch_wnd_update_us = 0;
    fohh_tlv->remote_tx_limit = fohh_tlv_window_size;
    fohh_tlv->peer_tx_data = 0;

    fohh_tlv->enq_bytes = 0;
    fohh_tlv->deq_bytes = 0;

    fohh_tlv->fc_blocked_timestamp = 0;
    fohh_tlv->fc_blocked_timestamp_initial = 0;

    fohh_tlv->max_tag_id = 0;
}

void zpn_fohh_tlv_set_conn(struct zpn_fohh_tlv *fohh_tlv, struct fohh_connection *f_conn)
{
    zpn_tlv_set_conn(&(fohh_tlv->tlv), zpn_fohh_tlv, f_conn);
}

struct fohh_connection *zpn_mconn_fohh_tlv_get_conn(struct zpn_fohh_tlv *fohh_tlv)
{
    return (fohh_tlv->tlv.conn.f_conn);
}

int64_t zpn_mconn_fohh_tlv_get_conn_incarnation(struct zpn_fohh_tlv *fohh_tlv)
{
    return (fohh_tlv->tlv.conn_incarnation);
}


/*
 * NOTE: this sets the fohh_tlv object as the dynamic cookie for the connection.
 */
int zpn_fohh_tlv_init(struct zpn_fohh_tlv *fohh_tlv,
                      struct fohh_connection *f_conn,
                      int64_t tlv_incarnation)
{
    ZPN_DEBUG_MCONN("%p: Initializing fohh_tlv state for conn %s", fohh_tlv, fohh_description(f_conn));

    zpn_fohh_tlv_clear_state(fohh_tlv);
    zpn_tlv_init(&(fohh_tlv->tlv), zpn_fohh_tlv, f_conn, tlv_incarnation);

    fohh_tlv->lock = ZPATH_MUTEX_INIT;
    if (fohh_tlv->tags) argo_hash_free(fohh_tlv->tags);
    fohh_tlv->tags = argo_hash_alloc(7, 1);
    if (!fohh_tlv->tags) return ZPN_RESULT_NO_MEMORY;
#if OLD_SCHEME
    TAILQ_INIT(&(fohh_tlv->mconn_list));
    fohh_tlv->next_mconn = NULL;
#else
    TAILQ_INIT(&(fohh_tlv->busy_mconn_list));
    TAILQ_INIT(&(fohh_tlv->idle_mconn_list));
    fohh_tlv->f_conn_down = 0;
#endif
    fohh_connection_set_dynamic_cookie(f_conn, fohh_tlv);
    fohh_connection_set_tlv_conn(f_conn, 1);
    fohh_tlv->drain_ev = NULL;
    fohh_tlv->monitor_ev = NULL;
    fohh_tlv->expire_ev = NULL;

    return ZPN_RESULT_NO_ERROR;
}

static struct zpn_mconn_fohh_tlv *zpn_fohh_tlv_get_mconn(struct zpn_fohh_tlv *fohh_tlv, int busy)
{
    struct zpn_mconn_fohh_tlv *mconn_fohh_tlv = NULL;

    ZPATH_MUTEX_LOCK(&(fohh_tlv->lock), __FILE__, __LINE__);
    if (busy == 1) {
        mconn_fohh_tlv = TAILQ_FIRST(&(fohh_tlv->busy_mconn_list));
    } else if (busy == 0) {
        mconn_fohh_tlv = TAILQ_FIRST(&(fohh_tlv->idle_mconn_list));
    } else {
#if OLD_SCHEME
        mconn_fohh_tlv = TAILQ_FIRST(&(fohh_tlv->mconn_list));
#endif
    }
    ZPATH_MUTEX_UNLOCK(&(fohh_tlv->lock), __FILE__, __LINE__);

    return mconn_fohh_tlv;
}



/* Termiante all mconns in the queue
 *   busy: specify which queue
 */
static int terminate_all_mconn_fohh_tlv(struct zpn_fohh_tlv *fohh_tlv, int busy, const char *reason)
{
    struct zpn_mconn_fohh_tlv *mconn_fohh_tlv = NULL;
    int res;
    int ret = ZPN_RESULT_NO_ERROR;
    int peer_connected = 0;
    int64_t uncounted_dropped_bytes = 0;
    int64_t last_hb_us = epoch_us();
    int hb_count=0;
    zthread_heartbeat(NULL);
    /* We had best only be 'owned' by one thread at this point. We do
     * a check to make sure terminate succeeds and we don't loop
     * forever, here. */
    while ((mconn_fohh_tlv = zpn_fohh_tlv_get_mconn(fohh_tlv, busy))) {
        struct zpn_mconn *mconn = &(mconn_fohh_tlv->mconn);

        if (!mconn->global_owner) {
            /*
             * Not having a global owner should never occur. But
             * 'continue' here is a clear infinite loop. So we log and
             * attempt mconn termination regardless.
             */
            //continue;
            ZPN_LOG(AL_CRITICAL, "%s: Mconn without global owner", zpn_mconn_fohh_tlv_get_conn(fohh_tlv) ? fohh_description(zpn_mconn_fohh_tlv_get_conn(fohh_tlv)) : "null f_conn");
            ret = ZPN_RESULT_ERR;
            break;
        }

        if (mconn->global_owner_calls) {
            (mconn->global_owner_calls->lock)(mconn,
                                              mconn->self,
                                              mconn->global_owner,
                                              mconn->global_owner_key,
                                              mconn->global_owner_key_length);
        }

        if (mconn_fohh_tlv->mconn.peer) {
            peer_connected = 1;
        }

        res = zpn_mconn_terminate(&(mconn_fohh_tlv->mconn), 1, 0, reason, &uncounted_dropped_bytes);
        if (reason && (0 == strcmp(reason, MT_CLOSED_TLS_CONN_GONE_CLIENT_CLOSED))) {
            if (!peer_connected && uncounted_dropped_bytes) {

                struct zpn_mconn *peer_mconn = NULL;
                int64_t customer_gid = 0;
                if (mconn->global_owner && mconn->global_owner_calls) {
                    peer_mconn = (mconn->global_owner_calls->get_peer)(mconn,
                                                                       mconn->self,
                                                                       mconn->global_owner,
                                                                       mconn->global_owner_key,
                                                                       mconn->global_owner_key_length);

                    customer_gid = (mconn->global_owner_calls->get_customer_gid)(mconn,
                                                                                 mconn->self,
                                                                                 mconn->global_owner,
                                                                                 mconn->global_owner_key,
                                                                                 mconn->global_owner_key_length);
                }
                if (NULL != peer_mconn) {
                    zpn_mconn_client_window_update(peer_mconn, 0, (int)uncounted_dropped_bytes, 0);
                    ZPN_LOG(AL_DEBUG, "%s: client closed mtunnel with uncounted_dropped_bytes %"PRId64", able to update peer" , zpn_mconn_fohh_tlv_get_conn(fohh_tlv) ? fohh_description(zpn_mconn_fohh_tlv_get_conn(fohh_tlv)) : "null f_conn",
                                                                                                            uncounted_dropped_bytes);
                } else {
                    ZPN_LOG(AL_WARNING, "%s: customer_gid %"PRId64"  client closed mtunnel with uncounted_dropped_bytes %"PRId64"! ", zpn_mconn_fohh_tlv_get_conn(fohh_tlv) ? fohh_description(zpn_mconn_fohh_tlv_get_conn(fohh_tlv)) : "null f_conn",
                                                                                                                                      customer_gid, uncounted_dropped_bytes);
                }
            }
        }
        if (res) ret = res; /* Should this break the loop? Probably...? */

        if (mconn->global_owner_calls) {
            (mconn->global_owner_calls->unlock)(mconn,
                                                mconn->self,
                                                mconn->global_owner,
                                                mconn->global_owner_key,
                                                mconn->global_owner_key_length);
        }
        /* If we have too many mconns to be terminated do heartbeat tickle.
         * Only allow the hearbeat tickle pass for duration of 5 * Broker Heartbeat timeout
         * If the function continues to execute beyond this time, we let through normal
         * heartbeat handling so that broker is not completely blocked by one thread forever.
         */
        int64_t now_us = epoch_us();
        if ((hb_count < 5*ZPN_MAX_HEARTBEAT_TIMEOUT) &&
            (now_us >= last_hb_us + SECOND_TO_US(1))) {
            zthread_heartbeat(NULL);
            last_hb_us = now_us;
            hb_count++;
        }
    }

#if OLD_SCHEME
    if (busy < 0) {
        fohh_tlv->next_mconn = NULL;
    }
#endif

    return ret;
}

int zpn_fohh_tlv_destroy(struct zpn_fohh_tlv *fohh_tlv, const char *reason)
{
    int ret = ZPN_RESULT_NO_ERROR;

    /*
     * This non-null reason error code have to eventually go away and the callers have to be specific on why it failed.
     */
    if (!reason) {
        reason = MT_CLOSED_TLS_CONN_GONE;
    }

#if OLD_SCHEME
    ret = terminate_all_mconn_fohh_tlv(fohh_tlv, -1, reason);
#else
    /* Terminate all busy mconn */
    ZPATH_MUTEX_LOCK(&(fohh_tlv->lock), __FILE__, __LINE__);
    fohh_tlv->f_conn_down = 1;
    ZPATH_MUTEX_UNLOCK(&(fohh_tlv->lock), __FILE__, __LINE__);

    /* Terminate all busy mconn */
    ret = terminate_all_mconn_fohh_tlv(fohh_tlv, 1, reason);
    /* Terminate all idle mconn */
    ret = terminate_all_mconn_fohh_tlv(fohh_tlv, 0, reason);
#endif

    ZPATH_MUTEX_LOCK(&(fohh_tlv->lock), __FILE__, __LINE__);

    if (fohh_tlv->tags) {
        argo_hash_free(fohh_tlv->tags);
    }
    fohh_tlv->tags = NULL;
    if (fohh_tlv->drain_ev) {
        event_free(fohh_tlv->drain_ev);
        fohh_tlv->drain_ev = NULL;
    }

    if (fohh_tlv->monitor_ev) {
        event_free(fohh_tlv->monitor_ev);
        fohh_tlv->monitor_ev = NULL;
    }

    if (fohh_tlv->expire_ev) {
        event_free(fohh_tlv->expire_ev);
        fohh_tlv->expire_ev = NULL;
    }

    if (fohh_tlv->tune_ev) {
        event_free(fohh_tlv->tune_ev);
        fohh_tlv->tune_ev = NULL;
    }

    zpn_tlv_clear(&(fohh_tlv->tlv));

    ZPATH_MUTEX_UNLOCK(&(fohh_tlv->lock), __FILE__, __LINE__);

    return ret;
}

void *zpn_fohh_tlv_get_global_owner(struct zpn_fohh_tlv *fohh_tlv,
                                    int32_t tag_id)
{
    struct zpn_mconn_fohh_tlv *mconn_fohh_tlv;
    int64_t incarnation = 0;
    mconn_fohh_tlv = zpn_mconn_fohh_tlv_lookup_tag(fohh_tlv, tag_id, &incarnation);
    if (mconn_fohh_tlv) {
        return mconn_fohh_tlv->mconn.self;
    } else {
        return NULL;
    }
}

void *zpn_fohh_tlv_get_local_owner(struct zpn_fohh_tlv *fohh_tlv,
                                   int32_t tag_id)
{
    struct zpn_mconn_fohh_tlv *mconn_fohh_tlv = NULL;
    int64_t incarnation = 0;
    mconn_fohh_tlv = zpn_mconn_fohh_tlv_lookup_tag(fohh_tlv, tag_id, &incarnation);
    return mconn_fohh_tlv;
}

int zpn_mconn_fohh_tlv_init(struct zpn_mconn_fohh_tlv *mconn_fohh_tlv,
                            void *mconn_self,
                            enum zpn_mconn_type type)
{
    mconn_fohh_tlv->tag_id = 0;
    mconn_fohh_tlv->data_arrived = 0;
    mconn_fohh_tlv->callbacks = 0;
    mconn_fohh_tlv->buffer_under = 0;
    mconn_fohh_tlv->buffer_over = 0;
    mconn_fohh_tlv->data_to_peer_attemp = 0;
    mconn_fohh_tlv->remote_paused = 0;
    mconn_fohh_tlv->pause_sent_us = 0;
    mconn_fohh_tlv->tx_data = 0;
    mconn_fohh_tlv->tx_limit = fohh_tlv_mconn_window_size;
    mconn_fohh_tlv->remote_tx_limit = fohh_tlv_mconn_window_size;
    mconn_fohh_tlv->prev_batch_remote_tx_limit = 0;
    mconn_fohh_tlv->remote_fc_status = flow_ctrl_none;
    mconn_fohh_tlv->busy = -1;
    return zpn_mconn_init(&(mconn_fohh_tlv->mconn), mconn_self, type);
}

const struct zpn_mconn_local_owner_calls zpn_mconn_fohh_tlv_calls = {
    zpn_mconn_fohh_tlv_bind_cb,
    zpn_mconn_fohh_tlv_unbind_cb,
    zpn_mconn_fohh_tlv_lock_cb,
    zpn_mconn_fohh_tlv_unlock_cb,
    zpn_mconn_fohh_tlv_transmit_cb,
    zpn_mconn_fohh_tlv_pause_cb,
    zpn_mconn_fohh_tlv_resume_cb,
    zpn_mconn_fohh_tlv_forward_tunnel_end_cb,
    zpn_mconn_fohh_tlv_window_update_cb,
    zpn_mconn_fohh_tlv_stats_update_cb,
    zpn_mconn_fohh_tlv_disable_read_cb,
    zpn_mconn_fohh_tlv_enable_read_cb
};

int zpn_mconn_fohh_tlv_clean(struct zpn_mconn_fohh_tlv *mconn_fohh_tlv)
{
    if (!zpn_mconn_clean(&(mconn_fohh_tlv->mconn))) {
        ZPN_DEBUG_MCONN("mconn fohh tlv not clean");
        return 0;
    }
    if (mconn_fohh_tlv->tag_id == 0) {
        ZPN_DEBUG_MCONN("mconn fohh tlv clean");
        return 1;
    } else {
        ZPN_DEBUG_MCONN("mconn fohh tlv not clean, tag_id = %ld", (long)mconn_fohh_tlv->tag_id);
        return 0;
    }
}

void zpn_mconn_fohh_tlv_internal_display(struct zpn_mconn_fohh_tlv *mconn_fohh_tlv)
{
    if (!mconn_fohh_tlv) return;

    ZPN_DEBUG_MCONN("tag_id = %d", mconn_fohh_tlv->tag_id);
    ZPN_DEBUG_MCONN("data arrived = %ld, to_peer_attemp = %ld", (long)mconn_fohh_tlv->data_arrived, (long)mconn_fohh_tlv->data_to_peer_attemp);
    ZPN_DEBUG_MCONN("callbacks = %d", mconn_fohh_tlv->callbacks);
    ZPN_DEBUG_MCONN("buffer under = %d, buffer over = %d", mconn_fohh_tlv->buffer_under, mconn_fohh_tlv->buffer_over);
    ZPN_DEBUG_MCONN("flow_control = %d, tx_data = %ld, tx_limit = %ld, last_wnd_tx_update_us = %ld, last_wnd_rx_update_us = %ld",
                    mconn_fohh_tlv->remote_fc_status, (long)mconn_fohh_tlv->tx_data,
                    (long)mconn_fohh_tlv->tx_limit, (long)mconn_fohh_tlv->last_wnd_tx_update_us, (long)mconn_fohh_tlv->last_wnd_rx_update_us);
    zpn_mconn_internal_display(&(mconn_fohh_tlv->mconn));
}



/*
 * This is called by FOHH data connections monitor callback.
 * Shoud be called periodically in minutes.
 *
 * Optionally log the status also during this monitoring event if the caller wishes for it.
 */
void zpn_mconn_fohh_tlv_fc_monitor(struct zpn_fohh_tlv *fohh_tlv, int log_status) {
    if (fohh_tlv) {
        ZPATH_MUTEX_LOCK(&(fohh_tlv->lock), __FILE__, __LINE__);
        struct fohh_connection *f_conn = zpn_mconn_fohh_tlv_get_conn(fohh_tlv);
        int skip = 0;
        skip = fohh_error_debug_check(f_conn);

        if (log_status && !skip) {
            // f_conn is verifid as non-NULL at this point
            struct fohh_connection_stats *fconn_stats = &f_conn->stats;
            ZPN_LOG(AL_INFO,
                    "FC f_conn %s:%s, thread_id: %d, rx = %ld, peer_tx = %ld, tx = %ld, tx_drop = %ld, tx_limit = %ld, tx_limit_update = %ld, r_rx = %ld, r_tx_limit = %ld, tx_wnd_update = %ld, rx_wnd_update = %ld, tx_blocked = %ld, enq = %ld, deq = %ld, "
                    "data_write_blocked = %ld, data_write_blocked_fohh = %ld, data_write_blocked_evbuf = %ld, fohh_fc_blocked = %ld, fohh_fc_blocked_time = %ld, max_fc_blocked_time = %ld, tot_fc_blocked_time = %ld, "
                    "disable read client = %ld, enable = %ld, disable read client tx buff high = %ld enable = %ld, tot us = %ld "
                    "tx_batch_wnd_update = %ld, rx_udp_data_dropped_frame_error = %ld, rx_udp_data_dropped_tx_buf_full = %ld, rx_icmp_error_data_dropped = %ld, rx_icmp6_error_data_dropped = %ld, "
                    "bufferevent_in_len = %"PRIu64", bufferevent_in_len_max = %"PRIu64", tlv_read_buf_len = %"PRIu64", tlv_read_buf_len_max = %"PRIu64", "
                    "bufferevent_out_len = %"PRIu64", bufferevent_out_len_max = %"PRIu64", raw_tlv_buffer_len = %"PRIu64", raw_tlv_buffer_len_max = %"PRIu64", raw_tlv_buffer_enq = %"PRIu64", raw_tlv_buffer_deq = %"PRIu64,
                    fohh_description(f_conn), fohh_state(f_conn),
                    fohh_connection_get_thread_id(f_conn),
                    (long)fohh_tlv->rx_data,
                    (long)fohh_tlv->peer_tx_data, (long)fohh_tlv->tx_data, (long)fohh_tlv->tx_data_drop,
                    (long)fohh_tlv->tx_limit, (long)fohh_tlv->tx_limit_update_us, (long)fohh_tlv->remote_rx_data,
                    (long)fohh_tlv->remote_tx_limit, (long)fohh_tlv->remote_rx_data_change_us,
                    (long)fohh_tlv->last_wnd_update_us, (long)fohh_tlv->fc_blocked_timestamp, (long)fohh_tlv->enq_bytes,
                    (long)fohh_tlv->deq_bytes, (long)fohh_tlv->fohh_data_write_blocked,
                    (long)fohh_tlv->fohh_data_write_fohh_blocked, (long)fohh_tlv->fohh_data_write_evbuf_blocked,
                    (long)fohh_tlv->fohh_fc_blocked, (long)fohh_tlv->fohh_fc_blocked_time,
                    (long)fohh_tlv->max_fohh_fc_blocked_time, (long)fohh_tlv->tot_fohh_fc_blocked_time,
                    (long)fohh_tlv->fohh_connection_disable_read_cnt, (long)fohh_tlv->fohh_connection_enable_read_cnt,
                    (long)fohh_tlv->fohh_connection_disable_read_client_tx_buff_high_cnt, (long)fohh_tlv->fohh_connection_enable_read_client_tx_buff_high_cnt,
                    (long)fohh_tlv->fohh_connection_disable_read_client_tx_buff_high_total_us,
                    (long)fohh_tlv->last_batch_wnd_update_us,
                    (long)fohh_tlv->rx_udp_data_dropped_frame_error, (long)fohh_tlv->rx_udp_data_dropped_tx_buf_full,
                    (long)fohh_tlv->rx_icmp_error_data_dropped, (long)fohh_tlv->rx_icmp6_error_data_dropped,
                    fconn_stats->bufferevent_in_len, fconn_stats->bufferevent_in_len_max, fconn_stats->tlv_read_buf_len,
                    fconn_stats->tlv_read_buf_len_max, fconn_stats->bufferevent_out_len, fconn_stats->bufferevent_out_len_max,
                    fconn_stats->raw_tlv_buffer_len, fconn_stats->raw_tlv_buffer_len_max, fconn_stats->raw_tlv_buffer_enq,
                    fconn_stats->raw_tlv_buffer_deq);
        }

        if (fohh_tlv->tx_data_us) {
            int64_t now_us = epoch_us();
            int64_t delta_us;

            delta_us = now_us - fohh_tlv->tx_data_us;
            if (delta_us > (10*1000000)) {
                /* We haven't sent out any data for 10 seconds */
                if (!fohh_tlv->fc_blocked_timestamp) {
                    struct zpn_mconn_fohh_tlv *mconn_fohh_tlv;
                    int64_t data_size = 0;
                    int64_t longest_blocked_time_us = 0;
                    int32_t longest_blocked_tag_id = 0;

                    /* And we are not flow control blocked */
#if OLD_SCHEME
                    TAILQ_FOREACH(mconn_fohh_tlv, &(fohh_tlv->mconn_list), mconn_list_entry) {
#else
                    TAILQ_FOREACH(mconn_fohh_tlv, &(fohh_tlv->busy_mconn_list), mconn_list_entry) {
#endif
                        if (zpn_mconn_transmit_buffer_exists(&(mconn_fohh_tlv->mconn))) {
                            data_size += zpn_mconn_get_transmit_buffer_len(&(mconn_fohh_tlv->mconn));

                            if (mconn_fohh_tlv->mconn.tx_data_us || mconn_fohh_tlv->mconn.from_peer_data_us) {
                                int64_t blocked_delta_us = now_us - ((mconn_fohh_tlv->mconn.tx_data_us) ? mconn_fohh_tlv->mconn.tx_data_us : mconn_fohh_tlv->mconn.from_peer_data_us);

                                if (blocked_delta_us > longest_blocked_time_us) {
                                    longest_blocked_tag_id = mconn_fohh_tlv->tag_id;
                                    longest_blocked_time_us = blocked_delta_us;
                                }
                            }
                        }
                    }

                    /* We have data to send, but didn't */
                    if (data_size > 0) {
                        ZPN_LOG(AL_WARNING, "FC f_conn %s:%s, we have %ld bytes to send, but didn't for %ld, last time enqueue from peer %ld us ago",
                                fohh_description(f_conn), fohh_state(f_conn),
                                (long)data_size, (long)delta_us, (long)(now_us - fohh_tlv->enq_data_us));
                        if (longest_blocked_tag_id) {
                            ZPN_LOG(AL_WARNING, "FC f_conn %s:%s, mconn with tag_id = %d has longest blocked time of %ld us",
                                    fohh_description(f_conn), fohh_state(f_conn), longest_blocked_tag_id, (long)longest_blocked_time_us);
                        }
                    }
                } else {
                    ZPN_LOG(AL_WARNING, "FC f_conn %s:%s, flow control blocked for %ld, tx stopped for %ld",
                            fohh_description(f_conn), fohh_state(f_conn),
                            (long)(epoch_us() - fohh_tlv->fc_blocked_timestamp), (long)delta_us);
                }
            }
        }
        ZPATH_MUTEX_UNLOCK(&(fohh_tlv->lock), __FILE__, __LINE__);
    }
}

static void zpn_mconn_fohh_tlv_thread_drain_cb(struct fohh_thread *thread, void *cookie, int64_t int_cookie)
{
    struct zpn_fohh_tlv *fohh_tlv = cookie;
    struct fohh_connection *f_conn = NULL;
    int res;

    res = zpn_tlv_incarnation_validation(&(fohh_tlv->tlv), int_cookie);
    if (res) {
        /* TlV or its connection has been reused */
        ZPN_LOG(AL_INFO, "zpn_mconn_fohh_tlv_thread_drain_cb, tlv or its conn incarnation check failed");
        return;
    }

    f_conn = zpn_mconn_fohh_tlv_get_conn(fohh_tlv);

    if (f_conn) {
        __sync_sub_and_fetch_4(&(fohh_tlv->unblock_thread_call_count), 1);
        zpn_fohh_tlv_unblock_cb(fohh_tlv);
    }
}

void zpn_mconn_fohh_tlv_activate_drain_timer(struct zpn_fohh_tlv *fohh_tlv)
{
    int thread_num = fohh_connection_get_thread_id(zpn_mconn_fohh_tlv_get_conn(fohh_tlv));
    int value = __sync_add_and_fetch_4(&(fohh_tlv->unblock_thread_call_count), 1);

    if (value > 2) {
        /* We already have thread call outstanding, no need to make call */
        __sync_sub_and_fetch_4(&(fohh_tlv->unblock_thread_call_count), 1);
        return;
    }

    if (fohh_thread_call(thread_num,
                         zpn_mconn_fohh_tlv_thread_drain_cb,
                         fohh_tlv,
                         zpn_tlv_incarnation(&(fohh_tlv->tlv))) != FOHH_RESULT_NO_ERROR) {
        ZPN_LOG(AL_CRITICAL, "Cannot make fohh_thread_call for async_resume_receive!");
        /* Thread call didn't succeed */
        __sync_sub_and_fetch_4(&(fohh_tlv->unblock_thread_call_count), 1);
    }

    return;
}

void zpn_mconn_fohh_tlv_add_drain_timer(struct fohh_connection *connection, struct zpn_fohh_tlv *fohh_tlv)
{
    zpn_mconn_fohh_tlv_activate_drain_timer(fohh_tlv);
}

void zpn_mconn_fohh_tlv_add_monitor_timer(struct fohh_connection *connection,
                                          struct zpn_fohh_tlv *fohh_tlv,
                                          event_callback_fn monitor_timer_cb,
                                          int64_t secs,
                                          int64_t usecs)
{
    int thread_num = fohh_connection_get_thread_id(connection);
    struct event_base *base = fohh_get_thread_event_base(thread_num);
    struct timeval tv;

    /* Check to see if the connection was reused */
    ZPATH_MUTEX_LOCK(&(fohh_tlv->lock), __FILE__, __LINE__);
    if (fohh_tlv->monitor_ev) {
        event_free(fohh_tlv->monitor_ev);
        fohh_tlv->monitor_ev = NULL;
    }

    /* We assume the fohh connection is established already and is assigned a thread */
    fohh_tlv->monitor_ev = event_new(base,
                                     -1,
                                     EV_PERSIST,
                                     monitor_timer_cb,
                                     fohh_tlv);

    tv.tv_sec = secs;
    tv.tv_usec = usecs;
    if (event_add(fohh_tlv->monitor_ev, &tv)) {
        ZPN_LOG(AL_ERROR, "Could not activate monitor timer");
        event_free(fohh_tlv->monitor_ev);
        fohh_tlv->monitor_ev = NULL;
    }
    ZPATH_MUTEX_UNLOCK(&(fohh_tlv->lock), __FILE__, __LINE__);
}

void zpn_mconn_fohh_tlv_remove_monitor_timer(struct zpn_fohh_tlv *fohh_tlv)
{
    ZPATH_MUTEX_LOCK(&(fohh_tlv->lock), __FILE__, __LINE__);
    if (fohh_tlv->monitor_ev) {
        event_free(fohh_tlv->monitor_ev);
        fohh_tlv->monitor_ev = NULL;
    }
    ZPATH_MUTEX_UNLOCK(&(fohh_tlv->lock), __FILE__, __LINE__);
}

void zpn_mconn_fohh_tlv_add_expire_timer(struct fohh_connection *connection,
                                         struct zpn_fohh_tlv *fohh_tlv,
                                         int exipire_time,
                                         event_callback_fn expire_timer_cb)
{
    int thread_num = fohh_connection_get_thread_id(connection);
    struct event_base *base = fohh_get_thread_event_base(thread_num);
    struct timeval tv;

    /* Check to see if the connection was reused */
    if (fohh_tlv->expire_ev) {
        event_free(fohh_tlv->expire_ev);
        fohh_tlv->expire_ev = NULL;
    }

    /* We assume the fohh connection is established already and is assigned a thread */
    fohh_tlv->expire_ev = event_new(base,
                                    -1,
                                    EV_PERSIST,
                                    expire_timer_cb,
                                    fohh_tlv);

    tv.tv_sec = exipire_time;
    tv.tv_usec = 0;
    if (event_add(fohh_tlv->expire_ev, &tv)) {
        ZPN_LOG(AL_ERROR, "Could not activate expire timer");
        event_free(fohh_tlv->expire_ev);
        fohh_tlv->expire_ev = NULL;
    }
}

void zpn_mconn_fohh_tlv_add_tune_timer(struct fohh_connection *connection,
                                       struct zpn_fohh_tlv *fohh_tlv,
                                       event_callback_fn tune_timer_cb,
                                       int64_t secs,
                                       int64_t usecs)
{
    int thread_num = fohh_connection_get_thread_id(connection);
    struct event_base *base = fohh_get_thread_event_base(thread_num);
    struct timeval tv;

    /* Check to see if the connection was reused */
    if (fohh_tlv->tune_ev) {
        event_free(fohh_tlv->tune_ev);
        fohh_tlv->tune_ev = NULL;
    }

    /* We assume the fohh connection is established already and is assigned a thread */
    fohh_tlv->tune_ev = event_new(base,
                                  -1,
                                  EV_PERSIST,
                                  tune_timer_cb,
                                  fohh_tlv);

    tv.tv_sec = secs;
    tv.tv_usec = usecs;
    if (event_add(fohh_tlv->tune_ev, &tv)) {
        ZPN_LOG(AL_ERROR, "Could not activate tune timer");
        event_free(fohh_tlv->tune_ev);
        fohh_tlv->tune_ev = NULL;
    }
}


void zpn_fohh_tlv_set_tx_limit(struct zpn_fohh_tlv *fohh_tlv, int64_t tx_limit)
{
    fohh_tlv->tx_limit = tx_limit;
}

void zpn_mconn_fohh_tlv_set_tx_limit(struct zpn_mconn_fohh_tlv *mconn_fohh_tlv, int64_t tx_limit)
{
    mconn_fohh_tlv->tx_limit = tx_limit;
}

static int zpn_mconn_fohh_tlv_fohh_window_size(struct zpath_debug_state *request_state,
                                               const char **query_values,
                                               int query_value_count,
                                               void *cookie)
{
    int i;

    if (!query_values[0]) {
        ZDP("Current FOHH window size = %d\n", fohh_tlv_window_size);
    } else {
        i = strtol(query_values[0], NULL, 0);
        if(i <= 0) {
            ZDP("Fohh window size cannot be <= 0");
            return ZPATH_RESULT_BAD_ARGUMENT;
        }
        fohh_tlv_window_size = i;
        fohh_tlv_low_watermark =  (fohh_tlv_window_size >> 1);
        ZDP("Fohh window size is set to %d\n", fohh_tlv_window_size);
    }
    return ZPATH_RESULT_NO_ERROR;
}

static int zpn_mconn_fohh_tlv_fohh_mconn_window_size(struct zpath_debug_state *request_state,
                                                     const char **query_values,
                                                     int query_value_count,
                                                     void *cookie)
{
    int i;

    if (!query_values[0]) {
        ZDP("Current Fohh mconn window size = %d\n", fohh_tlv_mconn_window_size);
    } else {
        i = strtol(query_values[0], NULL, 0);
        if(i <= 0) {
            ZDP("Fohh window size cannot be <= 0");
            return ZPATH_RESULT_BAD_ARGUMENT;
        }
        fohh_tlv_mconn_window_size = i;
        fohh_tlv_mconn_low_watermark = (fohh_tlv_mconn_window_size >> 1);
        ZDP("Fohh mconn window size is set to %d\n", fohh_tlv_mconn_window_size);
    }
    return ZPATH_RESULT_NO_ERROR;
}

static int zpn_mconn_fohh_tlv_fohh_low_watermark_size(struct zpath_debug_state *request_state,
                                                      const char **query_values,
                                                      int query_value_count,
                                                      void *cookie)
{
    int i;

    if (!query_values[0]) {
        ZDP("Current Fohh low watermark size = %d\n", fohh_tlv_low_watermark);
    } else {
        i = strtol(query_values[0], NULL, 0);
        if(i <= 0) {
            ZDP("Fohh low watermark cannot be <= 0");
            return ZPATH_RESULT_BAD_ARGUMENT;
        }
        fohh_tlv_low_watermark = i;
        ZDP("Fohh low watermark is set to %d\n", fohh_tlv_low_watermark);
    }
    return ZPATH_RESULT_NO_ERROR;
}
static int zpn_mconn_fohh_tlv_fohh_mconn_low_watermark_size(struct zpath_debug_state *request_state,
                                                            const char **query_values,
                                                            int query_value_count,
                                                            void *cookie)
{
    int i;

    if (!query_values[0]) {
        ZDP("Current Fohh mconn low watermark size = %d\n", fohh_tlv_mconn_low_watermark);
    } else {
        i = strtol(query_values[0], NULL, 0);
        if(i <= 0) {
            ZDP("Fohh mconn low watermark cannot be <= 0");
            return ZPATH_RESULT_BAD_ARGUMENT;
        }
        fohh_tlv_mconn_low_watermark = i;
        ZDP("Fohh mconn low watermark is set to %d\n", fohh_tlv_mconn_low_watermark);
    }
    return ZPATH_RESULT_NO_ERROR;
}

static int zpn_mconn_fohh_tlv_queue_check(struct zpath_debug_state *request_state,
                                          const char **query_values,
                                          int query_value_count,
                                          void *cookie)
{
    if (!query_values[0]) {
        ZDP("Current queue check value is %s\n", debug_queue_check ? "on" : "off");
    } else {
        if (strcasecmp(query_values[0], "on") == 0) {
            ZDP("Queue check value is set to %s\n", query_values[0]);
            debug_queue_check = 1;
        } else if (strcasecmp(query_values[0], "off") == 0) {
            ZDP("Queue check value is set to %s\n", query_values[0]);
            debug_queue_check = 0;
        } else {
            ZDP("Invalid value %s specified, no change to the queue check value\n", query_values[0]);
        }
    }

    return ZPATH_RESULT_NO_ERROR;
}

static int zpn_mconn_fohh_tlv_stats(struct zpath_debug_state *request_state,
                                    const char **query_values,
                                    int query_value_count,
                                    void *cookie)
{
    char        jsonout[10000];
    if (ARGO_RESULT_NO_ERROR == argo_structure_dump(zpn_mconn_fohh_tlv_stats_description,
                                                    &stats, jsonout, sizeof(jsonout), NULL, 1)) {
        ZDP("%s\n", jsonout);
    }

    return ZPATH_RESULT_NO_ERROR;

}

int zpn_mconn_fohh_tlv_init_debug(void)
{
    int res;

    if (!(zpn_mconn_fohh_tlv_stats_description = argo_register_global_structure(ZPN_MCONN_FOHH_TLV_STATS_HELPER))) {
        return ZPATH_RESULT_ERR;
    }

    res = zpath_debug_add_admin_command("Set FOHH flow control window size",
                                  "/zpn/fc/fohh_window",
                                  zpn_mconn_fohh_tlv_fohh_window_size,
                                  NULL,
                                  "size", "The FOHH window size",
                                  NULL);
    if (res) {
        return res;
    }

    res = zpath_debug_add_admin_command("Set FOHH mconn flow control window size",
                                  "/zpn/fc/fohh_mconn_window",
                                  zpn_mconn_fohh_tlv_fohh_mconn_window_size,
                                  NULL,
                                  "size", "The FOHH mconn window size",
                                  NULL);
    if (res) {
        return res;
    }

    res = zpath_debug_add_admin_command("Set FOHH low watermark",
                                  "/zpn/fc/fohh_low_watermark",
                                  zpn_mconn_fohh_tlv_fohh_low_watermark_size,
                                  NULL,
                                  "size", "The FOHH low watermark size",
                                  NULL);
    if (res) {
        return res;
    }

    res = zpath_debug_add_admin_command("Set FOHH mconn low watermark",
                                  "/zpn/fc/fohh_mconn_low_watermark",
                                  zpn_mconn_fohh_tlv_fohh_mconn_low_watermark_size,
                                  NULL,
                                  "size", "The FOHH mconn low watermark size",
                                  NULL);
    if (res) {
        return res;
    }


    res = zpath_debug_add_write_command("Turn on/off zpn_mconn_fohh_tlv queue check",
                                  "/zpn/mconn_fohh_tlv/queue_check",
                                  zpn_mconn_fohh_tlv_queue_check,
                                  NULL,
                                  "value", "Queue check value on/off",
                                  NULL);

    if(res) {
        return res;
    }

    res = zpath_debug_add_read_command("dump fohh_tlv overrall stats",
                                  "/zpn/mconn_fohh_tlv/stats",
                                  zpn_mconn_fohh_tlv_stats,
                                  NULL,
                                  NULL);

    if(res) {
        return res;
    }

    return ZPATH_RESULT_NO_ERROR;
}


void zpn_mconn_fohh_tlv_app_buffer_tune_enable (int enhanced, int fohh_window, int fohh_mconn_window, int64_t fohh_watermark, int64_t fohh_mconn_watermark)
{
    if (enhanced) {
        fohh_tlv_mconn_window_size = (fohh_mconn_window > 0 )? fohh_mconn_window : FOHH_TLV_EXTENDED_MCONN_WINDOW_SIZE;
        fohh_tlv_window_size = (fohh_window > 0)? fohh_window : FOHH_TLV_EXTENDED_WINDOW_SIZE;
    } else {
        fohh_tlv_mconn_window_size = FOHH_TLV_MCONN_WINDOW_SIZE;
        fohh_tlv_window_size = FOHH_TLV_WINDOW_SIZE;
    }
    if (enhanced && fohh_watermark > 0) {
        fohh_tlv_low_watermark = fohh_watermark;
    } else {
        fohh_tlv_low_watermark = (fohh_tlv_window_size >> 1);
    }
    if (enhanced && fohh_mconn_watermark > 0) {
        fohh_tlv_mconn_low_watermark = fohh_mconn_watermark;
    } else {
        fohh_tlv_mconn_low_watermark = (fohh_tlv_mconn_window_size >> 1);
    }
}

int64_t
zpn_mconn_fohh_tlv_get_fohh_window_size ()
{
    return FOHH_TLV_WINDOW_SIZE;
}

int64_t
zpn_mconn_fohh_tlv_get_fohh_mconn_window_size ()
{
    return FOHH_TLV_MCONN_WINDOW_SIZE;
}
