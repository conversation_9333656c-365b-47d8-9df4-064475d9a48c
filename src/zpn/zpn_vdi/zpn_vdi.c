/*
 * zpn_vdi.c. Copyright (C) 2024 Zscaler, Inc. All Rights Reserved.
 */

#include <string.h>
#include <ctype.h>
#include "zpn/zpn_lib.h"
#include "fohh/fohh.h"
#include "zpath_lib/zpath_customer.h"
#include "zpn/zpn_customer.h"
#include "zpn/zpn_application.h"
#include "zpn/zpn_broker_private.h"
#include "zpn/zpn_rpc.h"
#include "zpn/zpn_scope_ready.h"
#include "zpn/zpn_broker_client_scim.h"
#include "zpn/zbalance/zpn_balance_redirect.h"
#include "zpn/zpn_broker_client.h"
#include "zpath_lib/zpath_local.h"
#include "zpn/zpn_broker_client_private.h"
#include "zpn/zpn_branch_connector.h"
#include "zpn/zpn_location.h"
#include "zpn/zpn_broker_assert.h"
#include "zpn/zpn_pb_client.h"
#include "zpn/zpn_vdi/zpn_vdi.h"

struct zpn_broker_vdi_authentication_stats zpn_vdi_auth_stats = {0};

/*
 *
 */
int zpn_scim_start_vdi(struct zpn_broker_client_fohh_state *c_state, struct zpn_tlv *tlv) {
    int res;
    zpn_client_tracker_start(&c_state->tracker, client_track_scim);
    res = zpn_broker_client_scim_start(
            c_state, zpn_client_authenticate_validate_async_wally_cb, zpn_client_authenticate_validation);
    if (res) {
        ZPN_DEBUG_AUTH("%s: %s: Client scim start customer = %" PRId64 " returned: %s",
                       c_state->tunnel_id,
                       zpn_tlv_description(tlv),
                       c_state->customer_gid,
                       zpn_result_string(res));
        if (res != ZPN_RESULT_ASYNCHRONOUS) {
            zpn_client_tracker_end(&c_state->tracker, client_track_scim, res);
        }
        return res;
    }
    res = zpn_broker_client_scim_done(c_state);
    if (res) {
        ZPN_DEBUG_AUTH("%s: %s: Client scim done for customer = %" PRId64 " returned: %s",
                       c_state->tunnel_id,
                       zpn_tlv_description(tlv),
                       c_state->customer_gid,
                       zpn_result_string(res));
        if (res != ZPN_RESULT_ASYNCHRONOUS) {
            zpn_client_tracker_end(&c_state->tracker, client_track_scim, res);
        }
        return res;
    }
    zpn_client_tracker_end(&c_state->tracker, client_track_scim, res);
    return ZPN_RESULT_NO_ERROR;
}

/* non static for ut*/
int zpn_update_hops_vdi(struct zpn_broker_client_fohh_state *c_state,
                        struct zpn_vdi_client_authenticate *downstream_auth) {
    int ret;
    ret = zpn_update_hops_inner(c_state,
                                downstream_auth->hops_count,
                                downstream_auth->hop_type,
                                downstream_auth->hop_type_count,
                                downstream_auth->brks,
                                downstream_auth->brks_count,
                                downstream_auth->brks_grp,
                                downstream_auth->brks_grp_count,
                                downstream_auth->tunnel_ids,
                                downstream_auth->tunnel_ids_count,
                                downstream_auth->sys_type,
                                downstream_auth->sys_type_count,
                                downstream_auth->hop_debug,
                                downstream_auth->hop_debug_count);

    if (ret == ZPN_RESULT_ERR) {
        ZPN_DEBUG_LOG(AL_ERROR, "%s: Failed to update hop info from downstream.", c_state->tunnel_id);
        return ret;
    }
    zpn_update_current_hop(c_state, downstream_auth->hops_count);
    return ZPN_RESULT_NO_ERROR;
}

/*
 * Client = vdi
 * Tunnel = private_broker
 */
int zpn_pbroker_vdi_client_conn_callback(struct fohh_connection *connection,
                                         enum fohh_connection_state state,
                                         void *cookie) {
    return zpn_broker_client_conn_callback(
            connection, state, cookie, zpn_client_type_vdi, zpn_tunnel_auth_private_broker);
}

/*
 * handler for zpn_vdi_client_authenticate, no point of return error
 */
int zpn_vdi_client_authenticate_cb(void *argo_cookie_ptr, void *argo_structure_cookie_ptr, struct argo_object *object) {
    struct zpn_tlv *tlv;
    int res = 0, len = 0, use_segments = 0;
    char *err_string = "Internal Error";
    char *temp_str = NULL, *domain_str = NULL, *username = NULL;
    struct fohh_connection *fconn = NULL;
    struct zpn_fohh_tlv *fohh_tlv = NULL;
    struct zpn_broker_client_fohh_state *c_state = argo_structure_cookie_ptr;
    struct zpn_vdi_client_authenticate *auth = object->base_structure_void;

    if (!c_state) {
        ZPN_LOG(AL_CRITICAL, "No c_state");
        __sync_add_and_fetch(&(zpn_vdi_auth_stats.system_error), 1);
        return ZPN_RESULT_ERR; // error in  callback
    }

    tlv = c_state_get_tlv(c_state);
    if (zpn_tlv_sanity_check(tlv) != ZPN_RESULT_NO_ERROR) {
        ZPN_LOG(AL_CRITICAL, "tlv no connection?");
        __sync_add_and_fetch(&(zpn_vdi_auth_stats.system_error), 1);
        goto COUNT_ONLY;
    }

    if (auth) {
        if (!auth->username || !auth->last_auth_time) {
            ZPN_LOG(AL_CRITICAL,
                    "Need username = %s and last auth time = %" PRId64 " to process client authenticate",
                    auth->username,
                    auth->last_auth_time);
            err_string = "Username or last auth time cannot be NULL";
            __sync_add_and_fetch(&(zpn_vdi_auth_stats.auth_params_error), 1);
            goto AUTH_FAIL;
        }
    } else {
        ZPN_LOG(AL_CRITICAL, "auth is NULL cannot process vdi client authenticate");
        err_string = "Auth cannot be NULL";
        __sync_add_and_fetch(&(zpn_vdi_auth_stats.system_error), 1);
        goto AUTH_FAIL;
    }

    zpn_client_tracker_end(&c_state->tracker, client_track_clnt_auth_req, 0);

    if (!zpn_is_vdi_enabled_for_customer(c_state->customer_gid)) {
        ZPN_LOG(AL_ERROR,
                "%s: %s: VDI feature is disabled for customer=%" PRId64 " or globally",
                c_state->tunnel_id,
                zpn_tlv_description(tlv),
                c_state->customer_gid);
        err_string = "VDI feature is disabled";
        __sync_add_and_fetch(&(zpn_vdi_auth_stats.vdi_disabled), 1);
        goto AUTH_FAIL;
    }

    if (zpn_debug_get(ZPN_DEBUG_AUTH_IDX)) {
        char dump[8000];
        if (argo_object_dump(object, dump, sizeof(dump), NULL, 1) == ARGO_RESULT_NO_ERROR) {
            ZPN_LOG(AL_WARNING, "Rx: %s", dump);
        }
    }

    fohh_tlv = &(c_state->tlv_state);
    fconn = zpn_mconn_fohh_tlv_get_conn(fohh_tlv);
    if (fohh_connection_set_username(fconn, (char *)auth->username)) {
        ZPN_LOG(AL_ERROR, "failed to set fohh connection username");
        __sync_add_and_fetch(&(zpn_vdi_auth_stats.system_error), 1);
        goto AUTH_FAIL;
    }

    use_segments = zpn_client_static_config[c_state->client_type].segments;

    /*
     * If this is the first auth of a znf client
     * Attach the peer id (znf id) to the auth state so it can
     * propagated to all upstream brokers when they are forwarded the auth message
     */
    if (!auth->downstream_peer_id) {
        auth->downstream_peer_id = zpn_tlv_peer_get_id(tlv);
    }

    /* set this manually as we do not have version cb for vdi */
    c_state->version_supports_reauth = 1;

    // cache the downstream's peer id for use by other modules especially policy
    c_state->downstream_peer_id = auth->downstream_peer_id;

    int64_t prev_customer_gid = c_state->customer_gid;
    int64_t new_customer_gid;

    auth = object->base_structure_void;
    new_customer_gid = ZPATH_GID_GET_CUSTOMER_GID(zpn_tlv_peer_get_id(tlv));
    if (!new_customer_gid) {
        ZPN_LOG(AL_ERROR, "%s: No peer gid from certificate verification", zpn_tlv_description(tlv));
        err_string = "Internal state error";
        __sync_add_and_fetch(&(zpn_vdi_auth_stats.system_error), 1);
        goto AUTH_FAIL;
    }

    c_state->customer_gid = new_customer_gid;
    /* update customer_gid_entry stats with the updated customer_gid */
    zpn_broker_update_cstate_customer_gid_entry(c_state, prev_customer_gid);

    /*
     * For vdi we use default scope i.e. customer gid
     */
    if (!c_state->scope_gid) {
        c_state->scope_gid = c_state->customer_gid;
    }

    res = zpn_scope_ready(
            c_state->scope_gid, zpn_client_authenticate_validate_async_wally_cb, c_state, c_state->incarnation);
    ZPN_DEBUG_CLIENT("tunnel:%s customer:%" PRId64 " scope:%" PRId64 " logger zpn_scope_ready = %s",
                     c_state->tunnel_id,
                     c_state->customer_gid,
                     c_state->scope_gid,
                     zpn_result_string(res));
    if (res) {
        if (res == ZPATH_RESULT_ASYNCHRONOUS) {
            /* Callback will occur */
            c_state->is_customer_ready_async = 1;
            return ZPATH_RESULT_NO_ERROR;
        }
        err_string = "Internal scope error";
        __sync_add_and_fetch(&(zpn_vdi_auth_stats.scope_error), 1);
        goto AUTH_FAIL;
    }

    ZPN_DEBUG_SCOPE("%s: scope ready successful for scope %ld, customer %ld",
                    c_state->tunnel_id,
                    (long)c_state->scope_gid,
                    (long)c_state->customer_gid);

    c_state->auth_complete = 1;
    c_state->auth_request_id = auth->id;

    c_state->auth_request_x = argo_object_copy(object);
    if (!c_state->auth_request_x) {
        ZPN_LOG(AL_CRITICAL, "Memory");
        __sync_add_and_fetch(&(zpn_vdi_auth_stats.system_error), 1);
        goto COUNT_ONLY;
    }

    /* Platform is needed to be set to determine restrict app download capability */
    c_state->platform_type = zpn_broker_client_ret_enum_platform(c_state->tunnel_id, auth->platform);
    zpn_broker_client_count_client_capabilities_sent(c_state, auth, &use_segments);
    zpn_broker_client_record_capabilities_inc(c_state);

    if (zpn_tunnel_auth_znf == c_state->auth_type) {
        if (auth->cloud_name && auth->orgid) {
            if (c_state->customer_gid != zpath_customer_get_by_zia_cloud_orgid(
                                                 auth->cloud_name,
                                                 auth->orgid,
                                                 zpath_customer_get_zia_cloud_prefix_override(c_state->customer_gid))) {
                ZPN_LOG(AL_ERROR,
                        "%s: %s: IGNORED:  Could not match ec provided zia_cloud=%s and zia_orgid=%" PRId64
                        " with cst=%" PRId64,
                        c_state->tunnel_id,
                        zpn_tlv_description(tlv),
                        auth->cloud_name,
                        auth->orgid,
                        c_state->customer_gid);
                // ET-46168
                // in this branch, Cloud Connector has provided cloud name and orgid,
                // but ZIA has different configuration from ZPA as there is no match.
                // To avoid deployment issues, the authentications is allowed to succeed
                // The price is that location based policies wont work as cloud_name is not provided

                // TODO: Revisit/enforce after all ECs in ZIA clouds have caught up.
                // res = ZPN_RESULT_ERR;
                // goto auth_fail;
            } else {
                // even if it is matched, the location policy is disabled
                // TODO: Revisit/enforce after all ECs in ZIA clouds have caught up.
                c_state->zia_cloud_name = ZPN_STRDUP(auth->cloud_name, strlen(auth->cloud_name));
                c_state->orgid = auth->orgid;
                ZPN_LOG(AL_ERROR,
                        "%s: %s: DISABLED: match is found for ec provided zia_cloud=%s and zia_orgid=%" PRId64
                        " with cst=%" PRId64 ", location policy is disabled",
                        c_state->tunnel_id,
                        zpn_tlv_description(tlv),
                        auth->cloud_name,
                        auth->orgid,
                        c_state->customer_gid);
            }
        } else {
            // TODO: Revisit/enforce after all ECs in ZIA clouds have caught up.
            ZPN_LOG(AL_NOTICE,
                    "%s: %s: IGNORED: ec auth missing cloud_name or orgid, location policy won't work",
                    c_state->tunnel_id,
                    zpn_tlv_description(tlv));
        }
    } else if (zpn_tunnel_auth_branch_connector == c_state->auth_type) {
        if (auth->downstream_peer_id && c_state->customer_gid != ZPATH_GID_GET_CUSTOMER_GID(auth->downstream_peer_id)) {
            ZPN_LOG(AL_WARNING,
                    "%s: %s: bc auth with downstream_peer_id=%" PRId64 " not belonging to customer=%" PRId64,
                    c_state->tunnel_id,
                    zpn_tlv_description(tlv),
                    c_state->downstream_peer_id,
                    c_state->customer_gid);
            err_string = "Invalid downstream_peer_id";
            __sync_add_and_fetch(&(zpn_vdi_auth_stats.auth_params_error), 1);
            goto AUTH_FAIL;
        }

        // Pass bc gid as downstream_peer_id upstream and use it for policy.
        if (!auth->downstream_peer_id) {
            auth->downstream_peer_id = zpn_tlv_peer_get_id(tlv);
        }
        c_state->downstream_peer_id = auth->downstream_peer_id;
        c_state->log.branch_gid = c_state->downstream_peer_id;

        if (!auth->cloud_name || !auth->orgid) {
            ZPN_LOG(AL_WARNING,
                    "%s: %s: bc auth missing cloud_name or orgid",
                    c_state->tunnel_id,
                    zpn_tlv_description(tlv));
            err_string = "Missing cloud_name or orgid";
            __sync_add_and_fetch(&(zpn_vdi_auth_stats.auth_params_error), 1);
            goto AUTH_FAIL;
        }

        if (c_state->customer_gid != zpath_customer_get_by_zia_cloud_orgid(
                                             auth->cloud_name,
                                             auth->orgid,
                                             zpath_customer_get_zia_cloud_prefix_override(c_state->customer_gid))) {
            ZPN_LOG(AL_WARNING,
                    "%s: %s: Could not match bc provided zia_cloud=%s and zia_orgid=%" PRId64 " with cst=%" PRId64,
                    c_state->tunnel_id,
                    zpn_tlv_description(tlv),
                    auth->cloud_name,
                    auth->orgid,
                    c_state->customer_gid);
            err_string = "Invalid orgid or cloud_name";
            __sync_add_and_fetch(&(zpn_vdi_auth_stats.orgid_not_configured), 1);
            goto AUTH_FAIL;
        }
        c_state->zia_cloud_name = ZPN_BCA_STRDUP(auth->cloud_name, strlen(auth->cloud_name));
        c_state->orgid = auth->orgid;

        struct zpn_branch_connector *bc = NULL;
        struct zpn_branch_connector_group *bc_group = NULL;
        int res = zpn_bc_get(c_state->downstream_peer_id,
                             &bc,
                             &bc_group,
                             zpn_client_authenticate_validate_async_wally_cb,
                             c_state,
                             c_state->incarnation,
                             &c_state->async_count);
        if (res == ZPN_RESULT_ASYNCHRONOUS) {
            return ZPN_RESULT_NO_ERROR;
        } else if (res) {
            ZPN_LOG(AL_WARNING,
                    "%s: %s: Could not get branch connector info for gid=%" PRId64 ": %s",
                    c_state->tunnel_id,
                    zpn_tlv_description(tlv),
                    c_state->downstream_peer_id,
                    zpn_result_string(res));
            err_string = "branch connector info fetch failed";
            __sync_add_and_fetch(&(zpn_vdi_auth_stats.branch_info_fail), 1);
            goto AUTH_FAIL;
        }

        res = zpn_location_get_location_gids(c_state->customer_gid,
                                             auth->o_location_id,
                                             c_state->zia_cloud_name,
                                             &(c_state->o_location_id),
                                             &(c_state->o_sub_location_id),
                                             zpn_client_authenticate_validate_async_wally_cb,
                                             c_state,
                                             c_state->incarnation);
        if (res == ZPN_RESULT_ASYNCHRONOUS) {
            c_state->async_count++;
            return ZPN_RESULT_NO_ERROR;
        } else if (res == ZPN_RESULT_NOT_FOUND) {
            // Only log when the result was not found
            // Other errors can be logged internally but things like auth without location or zia cloud are ok
            // Because they are not required by us, and the only error we want to log is if an auth sent it all
            // but we still couldn't find it
            ZPN_LOG(AL_WARNING,
                    "%s: customer provided an unknown location id:<%" PRId64
                    "> with zia_cloud<%s> at auth so cannot attach to cstate",
                    c_state->tunnel_id,
                    auth->o_location_id,
                    (c_state->zia_cloud_name && c_state->zia_cloud_name[0] != '\0' ? c_state->zia_cloud_name
                                                                                   : "EMPTY"));
            res = ZPN_RESULT_NO_ERROR;
        }
    }

    c_state->client_aux_id = zpn_tlv_peer_get_aux_id(tlv);

    // Store the country code in hash
    {
        char tmp_cc[64];
        memset(tmp_cc, '\0', 64);
        snprintf(tmp_cc, sizeof(tmp_cc), "COUNTRY_CODE|%s|true", c_state->log.cc);
        tmp_cc[sizeof(tmp_cc) - 1] = '\0';
        size_t tmp_cc_len = strlen(tmp_cc);
        if (c_state->log.cc && !zhash_table_lookup(c_state->general_context_hash, tmp_cc, tmp_cc_len, NULL)) {
            ZPN_DEBUG_CLIENT("%s:Using country code - %s for policies", c_state->tunnel_id, c_state->log.cc);
            if (zhash_table_store(
                        c_state->general_context_hash, tmp_cc, tmp_cc_len, 0, c_state->general_context_hash) !=
                ZHASH_RESULT_NO_ERROR) {
                ZPN_LOG(AL_CRITICAL, "%s: Country code insert to hash failed: %s", c_state->tunnel_id, c_state->log.cc);

                __sync_add_and_fetch(&(zpn_vdi_auth_stats.system_error), 1);
                goto COUNT_ONLY;
            }
        } else if (!c_state->log.cc) {
            ZPN_LOG(AL_WARNING,
                    "%s: Customer %ld - Country code unavailable",
                    c_state->tunnel_id,
                    (long)c_state->customer_gid);
        }
    }

    c_state->auth_complete = 1;
    c_state->auth_epoch_s = auth->last_auth_time;

    if (zpn_update_hops_vdi(c_state, auth) == ZPN_RESULT_ERR) {
        ZPN_LOG(AL_CRITICAL, "%s: Failed to copy downstream hops", c_state->tunnel_id);
        __sync_add_and_fetch(&(zpn_vdi_auth_stats.system_error), 1);
        goto COUNT_ONLY;
    }

    /* Start connect to upstream public Broker (if we are private broker) */
    if ((g_broker_common_cfg->instance_type == ZPN_INSTANCE_TYPE_PRIVATE_BROKER) &&
        (zpn_client_static_config[c_state->client_type].fwd_to_pub)) {
        /* If this is private broker, go ahead and start connection to public broker ... */
        c_state->public_broker_client = zpn_pb_client_create(c_state, zpn_fohh_tlv, NULL);
        if (!c_state->public_broker_client) {
            ZPN_LOG(AL_ERROR, "%s: Could not create pb_client", c_state->tunnel_id);
            __sync_add_and_fetch(&(zpn_vdi_auth_stats.pb_client_fail), 1);
            goto COUNT_ONLY;
        }

        if (zpn_broker_is_dtls_enabled_on_broker()) {
            if (c_state->public_broker_client) {
                if (!c_state->conn_follow_timer) {
                    c_state->conn_follow_timer = event_new(fohh_get_thread_event_base(zpn_tlv_get_thread_id(tlv)),
                                                           -1,
                                                           EV_PERSIST,
                                                           connection_follow_timer_callback,
                                                           c_state);
                    if (!c_state->conn_follow_timer) {
                        ZPN_LOG(AL_CRITICAL, "Could not create timer");
                    } else {
                        ZPN_DEBUG_CLIENT("Created conn follow timer");

                        struct timeval tv;
                        tv.tv_sec = 1;
                        tv.tv_usec = 0;
                        event_add(c_state->conn_follow_timer, &tv);
                    }
                }
            }

            if (!c_state->conn_follow_timer) {
                /*
                 * If we come here, we either could not create pb_client for fohh, or cannot create timer,
                 * so just go ahead and create pb_client for zrdt
                 */
                if (!c_state->public_broker_client_zrdt) {
                    c_state->public_broker_client_zrdt = zpn_pb_client_create(c_state, zpn_zrdt_tlv, NULL);
                    if (!c_state->public_broker_client_zrdt) {
                        ZPN_LOG(AL_ERROR, "%s: Could not create pb_client for zrdt", c_state->tunnel_id);
                    }
                }
            }
        }

        c_state->fwd_to_next_hop = 1;

    } else {
        zpn_set_terminal_auth(c_state);
    }

    if (auth->private_ip) {
        c_state->log.priv_ip = *(auth->private_ip);
    }

    add_client_type_to_hashes(c_state);

    temp_str = ZPN_BCA_STRDUP(auth->username, strlen(auth->username) + 1);
    /* get the username in lower case first */
    username = ZPN_BCA_STRDUP(auth->username, strlen(auth->username) + 1);
    len = strlen(username);
    for (int i = 0; i < len; i++)
        username[i] = tolower(username[i]);

    /* get the domain name from username */
    domain_str = strtok((char *)temp_str, "@");
    domain_str = strtok(NULL, "@");
    if (!domain_str) {
        ZPN_DEBUG_AUTH("%s domain_name is NULL for customer %" PRId64, c_state->tunnel_id, c_state->customer_gid);
        err_string = "Invalid user name";
        __sync_add_and_fetch(&(zpn_vdi_auth_stats.username_invalid), 1);
        goto AUTH_FAIL;
    }

    c_state->domain_name = ZPN_BCA_STRDUP(domain_str, strlen(domain_str) + 1);
    ZPN_DEBUG_AUTH(
            "username = %s domain_name = %s auth->username = %s", username, c_state->domain_name, auth->username);

    c_state->idp_gid = 0;
    res = zpn_get_idp_gid_from_username(c_state);
    if (res && res != ZPN_RESULT_ASYNCHRONOUS && res != ZPN_RESULT_NO_ERROR) {
        ZPN_LOG(AL_ERROR,
                "%s failed to get idp_gid from username for customer %" PRId64 " with res = %s",
                c_state->tunnel_id,
                c_state->customer_gid,
                zpn_result_string(res));
        err_string = "idp gid not found for the user";
        __sync_add_and_fetch(&(zpn_vdi_auth_stats.no_idp_gid_for_user), 1);
        goto AUTH_FAIL;
    } else if (res == ZPN_RESULT_ASYNCHRONOUS) {
        return ZPN_RESULT_NO_ERROR;
    }

    // Check if scim is enabled for this customer, if not fail authentication
    if (!c_state->scim_enabled) {
        ZPN_LOG(AL_ERROR,
                "%s scim_enabled is %d for customer = %" PRId64,
                c_state->tunnel_id,
                c_state->scim_enabled,
                c_state->customer_gid);
        err_string = "scim disabled for customer";
        __sync_add_and_fetch(&(zpn_vdi_auth_stats.scim_disabled), 1);
        goto AUTH_FAIL;
    }

    if (c_state->idp_gid) {
        ZPN_DEBUG_AUTH("%s customer_gid = %" PRId64 " idp_gid = %" PRId64 " domain_name = %s",
                       c_state->tunnel_id,
                       c_state->customer_gid,
                       c_state->idp_gid,
                       c_state->domain_name);
        c_state->user_id = ZPN_BCA_CALLOC(ZPN_CLIENT_VDI_USER_ID);
        /* user_id will be concat of idp_gid and username (in lower case) */
        /* for now lets populate user_id with username, once we get domain id */
        snprintf(c_state->user_id, ZPN_CLIENT_VDI_USER_ID, "%s", username);
    } else {
        // did not get the idp from domain
        ZPN_LOG(AL_ERROR,
                "%s : Did not find idp for customer_gid - %" PRId64 " domain_name: %s",
                c_state->tunnel_id,
                c_state->customer_gid,
                c_state->domain_name);
        err_string = "idp not found for the domain";
        __sync_add_and_fetch(&(zpn_vdi_auth_stats.no_idp_for_domain), 1);
        goto AUTH_FAIL;
    }

    res = zpn_client_authenticate_validation(c_state);
    // should we go to auth_fail ?
    // NO, because zpn_client_authenticate_validation already is handling zpn_send_zpn_vdi_client_authenticate_ack
    // for both success/fail cases
    if (res) {
        ZPN_DEBUG_AUTH("%s zpn_client_authenticate_validation res = %s", c_state->tunnel_id, zpn_result_string(res));
    }

    ZPN_BCA_FREE(temp_str);
    ZPN_BCA_FREE(username);
    temp_str = username = NULL;
    return ZPN_RESULT_NO_ERROR;

AUTH_FAIL:
    ZPN_DEBUG_AUTH("%s: %s: %s: VDI client authentication failed for %s",
                   c_state->tunnel_id,
                   zpn_tlv_peer_cn(tlv),
                   zpn_tlv_description(tlv),
                   c_state->user_id ? c_state->user_id : "Unknown");

    res = zpn_send_zpn_vdi_client_authenticate_ack(tlv,
                                                   0,
                                                   auth ? auth->id : 0,
                                                   c_state->tunnel_id,
                                                   ZPATH_LOCAL_FULL_NAME,
                                                   g_broker_common_cfg->instance_type,
                                                   err_string);
    if (res) {
        ZPN_LOG(AL_WARNING,
                "%s: overrun? zpn_send_zpn_vdi_client_authenticate_ack failed: %s",
                c_state->tunnel_id,
                zpn_result_string(res));
    }

// falls through
COUNT_ONLY:
    c_state->auth_failed = 1;
    c_state->auth_complete = 0;
    // not sending ack, there is some system issue. The client will deal with no ack

    ZPN_BCA_FREE(temp_str);
    ZPN_BCA_FREE(username);
    temp_str = username = NULL;

    __sync_add_and_fetch(&(zpn_vdi_auth_stats.auth_validation_failed), 1);
    return ZPN_RESULT_NO_ERROR;
}

/*
 * copy stats into different variable ?
 * TODO: (VP) not sure why we need this vs direct access to variable zpn_vdi_auth_stats
  * non static for ut
 */
void zpn_broker_get_vdi_auth_stats(struct zpn_broker_vdi_authentication_stats *stats) {
    stats->auth_validation_failed = zpn_vdi_auth_stats.auth_validation_failed;
    stats->no_idp_for_domain = zpn_vdi_auth_stats.no_idp_for_domain;
    stats->no_idp_gid_for_user = zpn_vdi_auth_stats.no_idp_gid_for_user;
    stats->username_invalid = zpn_vdi_auth_stats.username_invalid;
    stats->auth_success = zpn_vdi_auth_stats.auth_success;
    stats->scim_sync_failed = zpn_vdi_auth_stats.scim_sync_failed;
    stats->system_error = zpn_vdi_auth_stats.system_error;
    stats->scim_disabled = zpn_vdi_auth_stats.scim_disabled;
    stats->vdi_disabled = zpn_vdi_auth_stats.vdi_disabled;
    stats->auth_params_error = zpn_vdi_auth_stats.auth_params_error;
    stats->scope_error = zpn_vdi_auth_stats.scope_error;
    stats->orgid_not_configured = zpn_vdi_auth_stats.orgid_not_configured;
    stats->branch_info_fail = zpn_vdi_auth_stats.branch_info_fail;
    stats->pb_client_fail = zpn_vdi_auth_stats.pb_client_fail;
    return;
}
/*
 * debug curl: /zpn/broker/client/vdi_stats
 */
int zpn_broker_client_vdi_debug_stats(struct zpath_debug_state *request_state,
                                      const char **query_values,
                                      int query_value_count,
                                      void *cookie) {
    struct zpn_broker_vdi_authentication_stats stats = {};

    zpn_broker_get_vdi_auth_stats(&stats);

    ZDP("Auth success: %" PRId64 "\n", stats.auth_success);
    ZDP("Invalid username: %" PRId64 "\n", stats.username_invalid);
    ZDP("No idp for domain: %" PRId64 "\n", stats.no_idp_for_domain);
    ZDP("No idp gid for user: %" PRId64 "\n", stats.no_idp_gid_for_user);
    ZDP("Scim sync failed: %" PRId64 "\n", stats.scim_sync_failed);
    ZDP("Auth validation failed: %" PRId64 "\n", stats.auth_validation_failed);
    ZDP("\n");

    return ZPN_RESULT_NO_ERROR;
}

/* Returning error will close the connection. We will kill the
 * connection in the close callback. This is just informational. No
 * need to propagate. */
int pb_client_vdi_authenticate_ack_cb(void *argo_cookie_ptr,
                                      void *argo_structure_cookie_ptr,
                                      struct argo_object *object) {
    struct zpn_vdi_client_authenticate_ack *ack;
    struct zpn_pb_client *pb_client = argo_structure_cookie_ptr;
    struct zpn_broker_client_fohh_state *c_state;

    if (pb_client_client_connection_incarnation(pb_client) != pb_client->inbound_client_connection_incarnation) {
        ZPN_LOG(AL_NOTICE, "Client connection gone, closing.");
        return ZPN_RESULT_BAD_STATE; // drops message, session will timeout
    }

    ack = object->base_structure_void;
    if (ack->error) {
        ZPN_LOG(AL_ERROR, "Could not authenticate with public broker...");
        return ZPN_RESULT_ERR; // drops message, session will timeout
    }

    ZPN_DEBUG_PBC("VDI, Received authenticate, broker = %s, tunnel = %s", ack->current_broker, ack->tunnel_id);
    pb_client->authenticated = 1;
    snprintf(pb_client->broker_tunnel_id, sizeof(pb_client->broker_tunnel_id), "%s", ack->tunnel_id);

    c_state = pb_client_to_c_state(pb_client);
    if (c_state) {
        ZPN_LOG(AL_NOTICE,
                "Client[%s] downstream connection %s,%s with downstream_tunnel_id: %s [%s] connected to upstream "
                "public broker %s via upstream_tunnel_id: %s [%s]",
                (c_state->user_id != NULL) ? c_state->user_id : "unknown",
                pb_client_client_connection_description(pb_client),
                zpn_client_type_string(c_state->client_type),
                c_state->tunnel_id,
                (pb_client->inbound_client_connection_tlv_type == zpn_fohh_tlv) ? "tls" : "dtls",
                pb_client_broker_conn_description(pb_client),
                ack->tunnel_id,
                (pb_client->outbound_tlv_type == zpn_fohh_tlv) ? "tls" : "dtls");
    } else {
        ZPN_LOG(AL_ERROR, "pb_client_to_c_state returned NULL c_state");
        // session will timeout
    }

    return ZPATH_RESULT_NO_ERROR;
}

/*
 * Client = branch connector per user tunnel
 * Tunnel = vdi
 */
static int zpn_broker_bc_vdi_client_conn_callback(struct fohh_connection *connection,
                                                  enum fohh_connection_state state,
                                                  void *cookie) {
    // fohh_set_debug(connection, 1);
    fohh_set_max_tx_history_depth(connection, 10000);
    int res = zpn_broker_client_conn_callback(
            connection, state, cookie, zpn_client_type_vdi, zpn_tunnel_auth_branch_connector);

    return res;
}

/*
 * Client = cloud connector per user tunnel
 * Tunnel = vdi
 */
static int zpn_broker_ec_vdi_client_conn_callback(struct fohh_connection *connection,
                                                  enum fohh_connection_state state,
                                                  void *cookie) {
    // fohh_set_debug(connection, 1);
    fohh_set_max_tx_history_depth(connection, 10000);
    int res = zpn_broker_client_conn_callback(connection, state, cookie, zpn_client_type_vdi, zpn_tunnel_auth_znf);

    return res;
}
/*
 * client = vdi
 * tunnel = pbroker
 *
 * tunnel from private broker to broker on behalf of vdi
 */
void zpn_broker_pb_broker_vdi_client_zdtls_session_callback(struct zdtls_session *zd_sess,
                                                            void *thread_cookie,
                                                            enum zdtls_session_status status,
                                                            const char *reason,
                                                            void *cookie,
                                                            int64_t int_cookie,
                                                            void *tlv_cookie) {
    zpn_broker_client_zdtls_session_callback(zd_sess,
                                             thread_cookie,
                                             status,
                                             reason,
                                             cookie,
                                             int_cookie,
                                             zpn_client_type_vdi,
                                             zpn_tunnel_auth_private_broker);
}
/*
 * Client = edge connector with vdi support
 * Tunnel = znf
 */
void zpn_broker_ec_vdi_client_zdtls_session_callback(struct zdtls_session *zd_sess,
                                                     void *thread_cookie,
                                                     enum zdtls_session_status status,
                                                     const char *reason,
                                                     void *cookie,
                                                     int64_t int_cookie,
                                                     void *tlv_cookie) {
    zpn_broker_client_zdtls_session_callback(
            zd_sess, thread_cookie, status, reason, cookie, int_cookie, zpn_client_type_vdi, zpn_tunnel_auth_znf);
}

/*
 * Client = branch connector with vdi support
 * Tunnel = bc
 */
void zpn_broker_bc_vdi_client_zdtls_session_callback(struct zdtls_session *zd_sess,
                                                     void *thread_cookie,
                                                     enum zdtls_session_status status,
                                                     const char *reason,
                                                     void *cookie,
                                                     int64_t int_cookie,
                                                     void *tlv_cookie) {
    zpn_broker_client_zdtls_session_callback(zd_sess,
                                             thread_cookie,
                                             status,
                                             reason,
                                             cookie,
                                             int_cookie,
                                             zpn_client_type_vdi,
                                             zpn_tunnel_auth_branch_connector);
}

/*
 * register sni servers for VDI
 */
int zpn_client_vdi_registration(struct fohh_generic_server *sni_server, const char *cloud_name) {
    struct fohh_connection *f_conn = NULL;
    char sni_str[ZPN_MAX_SNI_NAME_LEN + 1];
    int res = 0;
    // BC to broker
    /***********************************************************************
     *
     * Branch connector users to broker ( VDI <----> BC <-----> broker)
     */
    if (g_broker_common_cfg->instance_type == ZPN_INSTANCE_TYPE_PUBLIC_BROKER) {
        f_conn = fohh_server_create(1,                               // int quiet,
                                    argo_serialize_json_no_newline,  // enum argo_serialize_mode encoding,
                                    fohh_connection_style_argo_tlv,  // enum fohh_connection_style style,
                                    NULL,                            // void *cookie,
                                    zpn_broker_bc_vdi_client_conn_callback,
                                    zpn_fohh_tlv_data_callback,
                                    zpn_broker_client_unblock_callback,
                                    NULL,
                                    NULL,
                                    0,
                                    NULL,                                           // char *root_cert_file_name,
                                    NULL,                                           // char *my_cert_file_name,
                                    NULL,                                           // char *my_cert_key_file_name,
                                    1,                                              // int require_client_cert,
                                    1,                                              // int use_ssl);
                                    zpn_broker_client_ctx_callback,                 // ssl ctx callback
                                    zpn_bc_client_verify_callback,                  // verify callback
                                    zpn_broker_client_post_verify_region_check_cb,  // post verify callback
                                    0,                                              //  binary argo.
                                    12 * 60 * 60);                                  // 12 hour timeout.
        if (f_conn) {
            fohh_set_info_callback(
                    f_conn, zpn_broker_znf_conn_info_callback, zpn_broker_verify_alt_cloud_info_callback);
        }
    } else if (g_broker_common_cfg->instance_type == ZPN_INSTANCE_TYPE_PRIVATE_BROKER) {
        /* Register for ZAPP clients coming in using new SNI... */
        f_conn = fohh_server_create_ex(1,                               // int quiet,
                                       argo_serialize_json_no_newline,  // enum argo_serialize_mode encoding,
                                       fohh_connection_style_argo_tlv,  // enum fohh_connection_style style,
                                       NULL,                            // void *cookie,
                                       zpn_broker_bc_vdi_client_conn_callback,
                                       zpn_fohh_tlv_data_callback,
                                       zpn_broker_client_unblock_callback,
                                       NULL,
                                       NULL,
                                       0,
                                       NULL,  // char *root_cert_file_name,
                                       NULL,  // char *my_cert_file_name,
                                       NULL,  // char *my_cert_key_file_name,
                                       g_broker_common_cfg->private_broker.pkey_mem,
                                       1,                               // int require_client_cert,
                                       1,                               // int use_ssl);
                                       zpn_broker_client_ctx_callback,  // ssl ctx callback
                                       zpn_bc_client_verify_callback,   // verify callback
                                       NULL,
                                       0,              //  binary argo.
                                       12 * 60 * 60);  // 12 hour timeout.
    }

    if (!f_conn) {
        ZPN_LOG(AL_ERROR, "Could not create bc_vdi server connection");
        return ZPN_RESULT_ERR;
    }

    fohh_set_version_callback(f_conn, client_conn_version_callback);

    snprintf(sni_str, sizeof(sni_str), "bc_vdi.%s", cloud_name);
    res = fohh_generic_server_register(sni_server, f_conn, sni_str, 1, FOHH_WORKER_ZPN_BRANCH_CONNECTOR);

    if (res) {
        ZPN_LOG(AL_ERROR, "Could not register generic server VDI for %s", sni_str);
        return ZPN_RESULT_ERR;
    } else {
        zpn_broker_client_add_sni(sni_str, 1, 0);
        ZPN_DEBUG_STARTUP("%s: Registered new VDI client sni server: %s", fohh_description(f_conn), sni_str);
    }

#if 1
    /* Listen on DTLS connection */
    res = zpn_broker_client_listen_zrdt(sni_str, 1, zpn_client_type_vdi, zpn_tunnel_auth_branch_connector);
    if (res) {
        return ZPN_RESULT_ERR;
    } else {
        zpn_broker_client_add_sni(sni_str, 1, 1);
    }
#endif

    // EC to Broker
    /***********************************************************************
     *
     * Cloud connector users to broker ( VDI <----> EC <-----> broker)
     */
    if (g_broker_common_cfg->instance_type == ZPN_INSTANCE_TYPE_PUBLIC_BROKER) {
        f_conn = fohh_server_create(1,                               // int quiet,
                                    argo_serialize_json_no_newline,  // enum argo_serialize_mode encoding,
                                    fohh_connection_style_argo_tlv,  // enum fohh_connection_style style,
                                    NULL,                            // void *cookie,
                                    zpn_broker_ec_vdi_client_conn_callback,
                                    zpn_fohh_tlv_data_callback,
                                    zpn_broker_client_unblock_callback,
                                    NULL,
                                    NULL,
                                    0,
                                    NULL,                                           // char *root_cert_file_name,
                                    NULL,                                           // char *my_cert_file_name,
                                    NULL,                                           // char *my_cert_key_file_name,
                                    1,                                              // int require_client_cert,
                                    1,                                              // int use_ssl);
                                    zpn_broker_client_ctx_callback,                 // ssl ctx callback
                                    zpn_znf_client_verify_callback,                 // verify callback
                                    zpn_broker_client_post_verify_region_check_cb,  // post verify callback
                                    0,                                              //  binary argo.
                                    12 * 60 * 60);                                  // 12 hour timeout.
        if (f_conn) {
            fohh_set_info_callback(
                    f_conn, zpn_broker_znf_conn_info_callback, zpn_broker_verify_alt_cloud_info_callback);
        }
    } else if (g_broker_common_cfg->instance_type == ZPN_INSTANCE_TYPE_PRIVATE_BROKER) {
        /* Register for ZAPP clients coming in using new SNI... */
        f_conn = fohh_server_create_ex(1,                               // int quiet,
                                       argo_serialize_json_no_newline,  // enum argo_serialize_mode encoding,
                                       fohh_connection_style_argo_tlv,  // enum fohh_connection_style style,
                                       NULL,                            // void *cookie,
                                       zpn_broker_ec_vdi_client_conn_callback,
                                       zpn_fohh_tlv_data_callback,
                                       zpn_broker_client_unblock_callback,
                                       NULL,
                                       NULL,
                                       0,
                                       NULL,  // char *root_cert_file_name,
                                       NULL,  // char *my_cert_file_name,
                                       NULL,  // char *my_cert_key_file_name,
                                       g_broker_common_cfg->private_broker.pkey_mem,
                                       1,                               // int require_client_cert,
                                       1,                               // int use_ssl);
                                       zpn_broker_client_ctx_callback,  // ssl ctx callback
                                       zpn_znf_client_verify_callback,  // verify callback
                                       NULL,
                                       0,              //  binary argo.
                                       12 * 60 * 60);  // 12 hour timeout.
    }

    if (!f_conn) {
        ZPN_LOG(AL_ERROR, "Could not create ec server connection");
        return ZPN_RESULT_ERR;
    }

    fohh_set_version_callback(f_conn, client_conn_version_callback);

    snprintf(sni_str, sizeof(sni_str), "ec_vdi.%s", cloud_name);
    res = fohh_generic_server_register(sni_server, f_conn, sni_str, 1, FOHH_WORKER_ZPN_EDGE_CONNECTOR);

    if (res) {
        ZPN_LOG(AL_ERROR, "Could not register VDI generic server for %s", sni_str);
        return ZPN_RESULT_ERR;
    } else {
        zpn_broker_client_add_sni(sni_str, 1, 0);
        ZPN_DEBUG_STARTUP("%s: Registered new VDI client sni server: %s", fohh_description(f_conn), sni_str);
    }

#if 1
    /* Listen on DTLS connection */
    res = zpn_broker_client_listen_zrdt(sni_str, 1, zpn_client_type_vdi, zpn_tunnel_auth_znf);
    if (res) {
        return ZPN_RESULT_ERR;
    } else {
        zpn_broker_client_add_sni(sni_str, 1, 1);
    }
#endif
    /***********************************************************************
     *
     * Branch Connector Client from private broker to broker for pb_bc_vdi sni
     */
    if (g_broker_common_cfg->instance_type == ZPN_INSTANCE_TYPE_PUBLIC_BROKER) {
        /* Register for ZAPP clients coming in using new SNI... */
        f_conn = fohh_server_create(0,                               // int quiet,
                                    argo_serialize_binary,           // enum argo_serialize_mode encoding,
                                    fohh_connection_style_argo_tlv,  // enum fohh_connection_style style,
                                    NULL,                            // void *cookie,
                                    zpn_pbroker_vdi_client_conn_callback,
                                    zpn_fohh_tlv_data_callback,
                                    zpn_broker_client_unblock_callback,
                                    NULL,
                                    NULL,
                                    0,
                                    NULL,                                // char *root_cert_file_name,
                                    NULL,                                // char *my_cert_file_name,
                                    NULL,                                // char *my_cert_key_file_name,
                                    1,                                   // int require_client_cert,
                                    1,                                   // int use_ssl);
                                    zpn_broker_client_ctx_callback,      // ssl ctx callback
                                    zpn_pbroker_client_verify_callback,  // Will be arriving in a tunnel from pbroker
                                    zpn_broker_client_post_verify_region_check_cb,  // post verify callback
                                    1,                                              // Allow binary argo.
                                    12 * 60 * 60);                                  // 12 hour timeout.

        if (!f_conn) {
            ZPN_LOG(AL_ERROR, "Could not create VDI pb_bc_vdi server connection");
            return ZPN_RESULT_ERR;
        }

        fohh_set_version_callback(f_conn, client_conn_version_callback);

        snprintf(sni_str, sizeof(sni_str), "pb_bc_vdi.%s", cloud_name);
        res = fohh_generic_server_register(sni_server, f_conn, sni_str, 1, FOHH_WORKER_ZPN_PBDATA);

        if (res) {
            ZPN_LOG(AL_ERROR, "Could not register generic server for  VDI  %s", sni_str);
            return ZPN_RESULT_ERR;
        } else {
            zpn_broker_client_add_sni(sni_str, 1, 0);
            ZPN_DEBUG_STARTUP("%s: Registered new  VDI client sni server: %s", fohh_description(f_conn), sni_str);
        }
    }

    /***********************************************************************
     *
     * Edge Connector Client from private broker to broker, pb_ec_vdi sni
     */
    if (g_broker_common_cfg->instance_type == ZPN_INSTANCE_TYPE_PUBLIC_BROKER) {
        /* Register for ZAPP clients coming in using new SNI... */
        f_conn = fohh_server_create(0,                               // int quiet,
                                    argo_serialize_binary,           // enum argo_serialize_mode encoding,
                                    fohh_connection_style_argo_tlv,  // enum fohh_connection_style style,
                                    NULL,                            // void *cookie,
                                    zpn_pbroker_vdi_client_conn_callback,
                                    zpn_fohh_tlv_data_callback,
                                    zpn_broker_client_unblock_callback,
                                    NULL,
                                    NULL,
                                    0,
                                    NULL,                                // char *root_cert_file_name,
                                    NULL,                                // char *my_cert_file_name,
                                    NULL,                                // char *my_cert_key_file_name,
                                    1,                                   // int require_client_cert,
                                    1,                                   // int use_ssl);
                                    zpn_broker_client_ctx_callback,      // ssl ctx callback
                                    zpn_pbroker_client_verify_callback,  // Will be arriving in a tunnel from pbroker
                                    zpn_broker_client_post_verify_region_check_cb,  // post verify callback
                                    1,                                              // Allow binary argo.
                                    12 * 60 * 60);                                  // 12 hour timeout.

        if (!f_conn) {
            ZPN_LOG(AL_ERROR, "Could not create pb_ec_vdi server connection");
            return ZPN_RESULT_ERR;
        }

        fohh_set_version_callback(f_conn, client_conn_version_callback);

        snprintf(sni_str, sizeof(sni_str), "pb_ec_vdi.%s", cloud_name);
        res = fohh_generic_server_register(sni_server, f_conn, sni_str, 1, FOHH_WORKER_ZPN_PBDATA);

        if (res) {
            ZPN_LOG(AL_ERROR, "Could not register generic VDI server for %s", sni_str);
            return ZPN_RESULT_ERR;
        } else {
            zpn_broker_client_add_sni(sni_str, 1, 0);
            ZPN_DEBUG_STARTUP("%s: Registered new  VDI client sni server: %s", fohh_description(f_conn), sni_str);
        }
    }

    return ZPN_RESULT_NO_ERROR;
}
